<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Section extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'document_id',
        'section_number',
        'title',
        'content',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'metadata' => 'array',
        'section_number' => 'integer',
    ];

    /**
     * Get the document that owns the section.
     *
     * @return BelongsTo
     */
    public function document(): BelongsTo
    {
        return $this->belongsTo(Document::class);
    }

    /**
     * Get the subsections for this section.
     *
     * @return HasMany
     */
    public function subsections(): HasMany
    {
        return $this->hasMany(Subsection::class);
    }

    /**
     * Get the questions for this section.
     *
     * @return HasMany
     */
    public function questions(): Has<PERSON>any
    {
        return $this->hasMany(Question::class);
    }
}
