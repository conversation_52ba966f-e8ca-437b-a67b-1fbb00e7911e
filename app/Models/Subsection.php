<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Subsection extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'section_id',
        'subsection_number',
        'title',
        'content',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'metadata' => 'array',
        'subsection_number' => 'integer',
    ];

    /**
     * Get the section that owns the subsection.
     *
     * @return BelongsTo
     */
    public function section(): BelongsTo
    {
        return $this->belongsTo(Section::class);
    }

    /**
     * Get the document through the section.
     */
    public function document()
    {
        return $this->hasOneThrough(
            Document::class,
            Section::class,
            'id', // Foreign key on sections table
            'id', // Foreign key on documents table
            'section_id', // Local key on subsections table
            'document_id' // Local key on sections table
        );
    }

    /**
     * Get the questions for this subsection.
     *
     * @return HasMany
     */
    public function questions(): HasMany
    {
        return $this->hasMany(Question::class);
    }
}
