<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Parameter extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'question_id',
        'header_orientation',
        'headers',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'headers' => 'array',
    ];

    /**
     * Get the question that owns the parameter.
     */
    public function question(): BelongsTo
    {
        return $this->belongsTo(Question::class);
    }

    /**
     * Get the parameter values for the parameter.
     */
    public function parameterValues(): HasMany
    {
        return $this->hasMany(ParameterValue::class);
    }

    /**
     * Store parameter values from a 2D array into the parameter_values table.
     *
     * @param array $valuesArray 2D array of values
     * @return void
     */
    public function storeParameterValues(array $valuesArray): void
    {
        // Clear existing parameter values
        $this->parameterValues()->delete();

        // Store new values
        foreach ($valuesArray as $rowIndex => $row) {
            if (is_array($row)) {
                foreach ($row as $columnIndex => $value) {
                    $this->parameterValues()->create([
                        'row_index' => $rowIndex,
                        'column_index' => $columnIndex,
                        'value' => $value,
                    ]);
                }
            }
        }
    }

    /**
     * Get parameter values as a 2D array.
     *
     * @return array
     */
    public function getParameterValuesArray(): array
    {
        $values = [];

        foreach ($this->parameterValues as $parameterValue) {
            $values[$parameterValue->row_index][$parameterValue->column_index] = $parameterValue->value;
        }

        // Sort by row and column indices
        ksort($values);
        foreach ($values as &$row) {
            ksort($row);
        }

        return $values;
    }
}
