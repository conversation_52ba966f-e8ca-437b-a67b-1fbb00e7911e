<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Article extends Model
{
    use HasFactory;

    protected $fillable = [
        'article_family_id',
        'article_code',
        'article_name',
        'article_type',
        'available_stock',
        'batch_size',
        'bulk_code',
        'is_complex',
        'is_sample',
        'product_type',
        'target_coverage_months',
        'monthly_budget',
        'material_type',
        'unit_of_measure',
        'is_active',
    ];

    protected $casts = [
        'is_complex' => 'boolean',
        'is_sample' => 'boolean',
        'is_active' => 'boolean',
        'batch_size' => 'decimal:2',
        'monthly_budget' => 'decimal:2',
    ];

    /**
     * Get the article family that this article belongs to
     */
    public function family(): BelongsTo
    {
        return $this->belongsTo(ArticleFamily::class, 'article_family_id');
    }

    /**
     * Get the production orders associated with this article (if it's a finished product).
     */
    public function productionOrders(): HasMany
    {
        return $this->hasMany(ProductionOrder::class, 'article_id');
    }

    /**
     * Get the compositions where this article is the finished product.
     */
    public function compositionsAsFinishedProduct(): HasMany
    {
        return $this->hasMany(ProductComposition::class, 'finished_product_article_id');
    }

    /**
     * Get the compositions where this article is a primary material.
     */
    public function compositionsAsPrimaryMaterial(): HasMany
    {
        return $this->hasMany(ProductComposition::class, 'primary_material_article_id');
    }

    /**
     * Get the primary materials used in this article (if it's a finished product).
     */
    public function primaryMaterials(): BelongsToMany
    {
        return $this->belongsToMany(Article::class, 'product_compositions', 'finished_product_article_id', 'primary_material_article_id')
                    ->wherePivot('quantity_per_unit', '>', 0)
                    ->withPivot('quantity_per_unit', 'quantity_per_lot', 'unit_of_measure');
    }

    /**
     * Get the finished products that use this article (if it's a primary material).
     */
    public function finishedProducts(): BelongsToMany
    {
        return $this->belongsToMany(Article::class, 'product_compositions', 'primary_material_article_id', 'finished_product_article_id')
                    ->withPivot('quantity_per_unit', 'quantity_per_lot', 'unit_of_measure');
    }

    // ------------- SCOPES ---------------

    /**
     * Scope a query to only include finished products.
     */
    public function scopeFinishedProducts(Builder $query): void
    {
        $query->where('article_type', 'finished_product');
    }

    /**
     * Scope a query to only include primary materials.
     */
    public function scopePrimaryMaterials(Builder $query): void
    {
        $query->where('article_type', 'primary_material');
    }
}
