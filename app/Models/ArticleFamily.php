<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ArticleFamily extends Model
{
    use HasFactory;

    protected $fillable = [
        'family_code',
        'family_name',
    ];

    /**
     * Get all articles belonging to this family
     */
    public function articles(): HasMany
    {
        return $this->hasMany(Article::class);
    }
}
