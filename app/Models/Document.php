<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class Document extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'original_filename',
        'file_hash',
        'mime_type',
        'file_size',
        'html_content',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'metadata' => 'array',
        'file_size' => 'integer',
    ];

    /**
     * Get document by original file hash
     *
     * @param string $hash
     * @return self|null
     */
    public static function findByHash(string $hash): ?self
    {
        return static::where('file_hash', $hash)->first();
    }

    /**
     * Get the sections for this document.
     *
     * @return HasMany
     */
    public function sections(): HasMany
    {
        return $this->hasMany(Section::class);
    }

    /**
     * Get all subsections for this document through sections.
     *
     * @return HasManyThrough
     */
    public function subsections(): HasManyThrough
    {
        return $this->hasManyThrough(Subsection::class, Section::class);
    }
}
