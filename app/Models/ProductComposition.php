<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductComposition extends Model
{
    use HasFactory;

    protected $fillable = [
        'finished_product_article_id',
        'primary_material_article_id',
        'quantity_per_unit',
        'quantity_per_lot',
        'unit_of_measure',
    ];

    protected $casts = [
        'quantity_per_unit' => 'decimal:4',
        'quantity_per_lot' => 'decimal:4',
    ];

    /**
     * Get the finished product article that this composition belongs to.
     */
    public function finishedProductArticle(): BelongsTo
    {
        return $this->belongsTo(Article::class, 'finished_product_article_id');
    }

    /**
     * Get the primary material article used in this composition.
     */
    public function primaryMaterialArticle(): BelongsTo
    {
        return $this->belongsTo(Article::class, 'primary_material_article_id');
    }
}
