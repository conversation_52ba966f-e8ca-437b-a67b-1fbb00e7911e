<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Question extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'subsection_id',
        'section_id',
        'question_text',
    ];

    /**
     * Get the subsection that owns the question.
     */
    public function subsection(): BelongsTo
    {
        return $this->belongsTo(Subsection::class);
    }

    /**
     * Get the section that owns the question.
     */
    public function section(): BelongsTo
    {
        return $this->belongsTo(Section::class);
    }

    /**
     * Get the parameters for the question.
     */
    public function parameters(): HasMany
    {
        return $this->hasMany(Parameter::class);
    }
}
