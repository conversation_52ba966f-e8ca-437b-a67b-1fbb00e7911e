<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasFactory, Notifiable, HasRoles;

    protected $fillable = [
        'name',
        'email',
        'password',
        'user_type',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function isSuperuser(): bool
    {
        return $this->user_type === 'superuser';
    }

    public function isAdmin(): bool
    {
        return $this->user_type === 'admin';
    }

    public function isClient(): bool
    {
        return $this->user_type === 'client';
    }

    public function canCreateRoles(): bool
    {
        return $this->isAdmin() || $this->isSuperuser();
    }

    public function canManageUsers(): bool
    {
        return $this->isAdmin() || $this->isSuperuser();
    }
}
