<?php

namespace App\Http\Controllers\Permissions;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleController extends Controller
{
    use AuthorizesRequests;

    public function index()
    {
        $currentUser = Auth::user();
        $allRoles = Role::withCount(['permissions', 'users'])
            ->with(['users' => function ($query) {
                $query->select('id', 'name', 'email', 'user_type');
            }])
            ->get();

        // Filter roles based on what current user can view
        $visibleRoles = $allRoles->filter(function ($role) use ($currentUser) {
            return $currentUser->can('view', $role);
        });

        $roles = $visibleRoles->map(function ($role) use ($currentUser) {
            return [
                'id' => $role->id,
                'name' => $role->name,
                'display_name' => $this->formatDisplayName($role->name),
                'description' => $role->description ?: $this->formatDisplayName($role->name),
                'permissions_count' => $role->permissions_count,
                'users_count' => $role->users_count,
                'created_at' => $role->created_at->format('Y-m-d'),
                'can_edit' => $currentUser->can('update', $role),
                'can_delete' => $currentUser->can('delete', $role),
                'users' => $role->users,
            ];
        })->values(); // Reset array keys because we used ->filter()

        return Inertia::render('roles/index', [
            'roles' => $roles,
        ]);
    }

    public function create()
    {
        $permissions = Permission::all()->groupBy('category')->map(function ($permissions, $category) {
            return [
                'category' => $category ?: 'Uncategorized',
                'permissions' => $permissions->map(function ($permission) {
                    return [
                        'id' => $permission->id,
                        'name' => $permission->name,
                        'display_name' => $this->formatDisplayName($permission->name),
                        'description' => $permission->description ?: $this->formatDisplayName($permission->name),
                    ];
                }),
            ];
        })->values();

        return Inertia::render('roles/create', [
            'permissions' => $permissions,
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-z0-9-]+$/',
                Rule::unique('roles', 'name'),
            ],
            'display_name' => 'required|string|max:255',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id',
        ], [
            'name.regex' => 'Role name must be lowercase with hyphens only (e.g., custom-manager).',
            'name.unique' => 'A role with this name already exists.',
        ]);

        $role = Role::create([
            'name' => $validated['name'],
            'guard_name' => 'web',
        ]);

        // Assign permissions if provided
        if (!empty($validated['permissions'])) {
            $permissions = Permission::whereIn('id', $validated['permissions'])->get();
            $role->givePermissionTo($permissions);
        }

        return redirect()->route('roles.index')
            ->with('success', "Role '{$validated['display_name']}' created successfully.");
    }

    public function edit(Role $role)
    {
        $permissions = Permission::all()->groupBy('category')->map(function ($permissions, $category) use ($role) {
            return [
                'category' => $category ?: 'Uncategorized',
                'permissions' => $permissions->map(function ($permission) use ($role) {
                    return [
                        'id' => $permission->id,
                        'name' => $permission->name,
                        'display_name' => $this->formatDisplayName($permission->name),
                        'description' => $permission->description ?: $this->formatDisplayName($permission->name),
                        'assigned' => $role->hasPermissionTo($permission->name),
                    ];
                }),
            ];
        })->values();

        $roleData = [
            'id' => $role->id,
            'name' => $role->name,
            'display_name' => $this->formatDisplayName($role->name),
            'description' => $role->description ?: $this->formatDisplayName($role->name),
            'permissions_count' => $role->permissions()->count(),
            'users_count' => $role->users()->count(),
            'created_at' => $role->created_at->format('Y-m-d'),
            'updated_at' => $role->updated_at->format('Y-m-d'),
            'can_edit' => Auth::user()->can('update', $role),
            'can_delete' => Auth::user()->can('delete', $role),
            'users' => $role->users()->select('id', 'name', 'email', 'user_type')->get(),
            'current_permissions' => $role->permissions->pluck('id')->toArray(),
        ];

        return Inertia::render('roles/edit', [
            'role' => $roleData,
            'permissions' => $permissions,
        ]);
    }

    public function update(Request $request, Role $role)
    {
        $validated = $request->validate([
            'name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-z0-9-]+$/',
                Rule::unique('roles', 'name')->ignore($role->id),
            ],
            'display_name' => 'required|string|max:255',
        ], [
            'name.regex' => 'Role name must be lowercase with hyphens only (e.g., custom-manager).',
            'name.unique' => 'A role with this name already exists.',
        ]);

        $role->update([
            'name' => $validated['name'],
        ]);

        return redirect()->route('roles.edit', $role)
            ->with('success', "Role '{$validated['display_name']}' updated successfully.");
    }

    public function destroy(Role $role)
    {
        $roleName = $this->formatDisplayName($role->name);

        $role->users()->detach();

        $role->delete();

        return redirect()->route('roles.index')
            ->with('success', "Role '{$roleName}' deleted successfully.");
    }

    public function updatePermissions(Request $request, Role $role)
    {
        $validated = $request->validate([
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        $permissions = Permission::whereIn('id', $validated['permissions'] ?? [])->get();

        $role->syncPermissions($permissions);

        return redirect()->route('roles.edit', $role)
            ->with('success', 'Role permissions updated successfully.');
    }

    private function formatDisplayName(string $name): string
    {
        return ucwords(str_replace(['-', '_'], ' ', $name));
    }
}
