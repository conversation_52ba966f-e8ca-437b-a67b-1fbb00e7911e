<?php

namespace App\Http\Controllers\Permissions;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Spatie\Permission\Models\Role;

class UserRoleController extends Controller
{
    public function show(User $user)
    {
        $availableRoles = $this->getAvailableRolesForUserType($user->user_type);

        $userData = [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'user_type' => $user->user_type,
            'current_roles' => $user->getRoleNames()->toArray(), // spatie
            'direct_permissions' => $user->getDirectPermissions()->pluck('name')->toArray(), // spatie
            'created_at' => $user->created_at->format('Y-m-d'),
        ];

        $rolesData = $availableRoles->map(function ($role) {
            return [
                'id' => $role->id,
                'name' => $role->name,
                'display_name' => $this->formatDisplayName($role->name),
                'description' => $role->description ?: $this->formatDisplayName($role->name),
                'permissions_count' => $role->permissions()->count(),
            ];
        });

        return Inertia::render('users/roles', [
            'user' => $userData,
            'roles' => $rolesData,
        ]);
    }

    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'roles' => 'array',
            'roles.*' => 'string|exists:roles,name',
        ]);

        $availableRoles = $this->getAvailableRolesForUserType($user->user_type);
        $availableRoleNames = $availableRoles->pluck('name')->toArray();

        $rolesToAssign = array_intersect($validated['roles'] ?? [], $availableRoleNames);

        $currentUser = Auth::user();
        if (!$currentUser->can('updateUserRoles', [$user, $rolesToAssign])) {
            return redirect()->back()
                ->with('error', 'You do not have permission to assign these roles.');
        }

        $user->syncRoles($rolesToAssign);

        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        return redirect()->route('users.index')
            ->with('success', 'User roles updated successfully.');
    }

    public function assignRole(Request $request, User $user)
    {
        $validated = $request->validate([
            'role' => 'required|string|exists:roles,name',
        ]);

        $role = Role::findByName($validated['role']);

        // Use policy to check if current user can assign this role to the target user
        if (!Auth::user()->can('assignRole', [$user, $role->name])) {
            return redirect()->back()
                ->with('error', 'You do not have permission to assign this role.');
        }

        // Check if user already has this role
        if ($user->hasRole($role->name)) {
            return redirect()->back()
                ->with('error', 'User already has this role.');
        }

        $user->assignRole($role);

        return redirect()->back()
            ->with('success', "Role '{$this->formatDisplayName($role->name)}' assigned successfully.");
    }

    public function removeRole(Request $request, User $user)
    {
        $validated = $request->validate([
            'role' => 'required|string|exists:roles,name',
        ]);

        $role = Role::findByName($validated['role']);

        // Check if user has this role
        if (!$user->hasRole($role->name)) {
            return redirect()->back()
                ->with('error', 'User does not have this role.');
        }

        $user->removeRole($role);

        return redirect()->back()
            ->with('success', "Role '{$this->formatDisplayName($role->name)}' removed successfully.");
    }

    private function getAvailableRolesForUserType(string $userType)
    {
        $currentUser = Auth::user();
        $availableRoles = [];
        $allRoles = Role::all();

        foreach ($allRoles as $role) {
            $canAssign = false;

            // Check if current user can assign this role to current user type
            $dummyUser = new User(['user_type' => $userType]);
            if ($currentUser->can('assignRole', [$dummyUser, $role->name])) {
                $canAssign = true;
            }

            // For client users, also check if admin can assign admin role (for promotion)
            if ($userType === 'client' && $role->name === 'admin') {
                $dummyAdminUser = new User(['user_type' => 'admin']);
                if ($currentUser->can('assignRole', [$dummyAdminUser, $role->name])) {
                    $canAssign = true;
                }
            }

            if ($canAssign) {
                $availableRoles[] = $role;
            }
        }

        return collect($availableRoles);
    }

    private function formatDisplayName(string $name): string
    {
        return ucwords(str_replace(['-', '_'], ' ', $name));
    }
}
