<?php

namespace App\Http\Controllers\Permissions;

use App\Http\Controllers\Controller;
use App\Models\User;
use Inertia\Inertia;
use Spatie\Permission\Models\Permission;

class PermissionController extends Controller
{
    public function index()
    {
        $permissions = Permission::withCount(['roles', 'users'])
            ->with(['roles:id,name', 'users:id,name,email,user_type'])
            ->get()
            ->groupBy('category')
            ->map(function ($permissions, $category) {
                return [
                    'category' => $category ?: 'Uncategorized',
                    'permissions' => $permissions->map(function ($permission) {
                        $totalUsersWithPermission = User::permission($permission->name)->count();

                        return [
                            'id' => $permission->id,
                            'name' => $permission->name,
                            'display_name' => $this->formatDisplayName($permission->name),
                            'description' => $permission->description ?: $this->formatDisplayName($permission->name),
                            'roles_count' => $permission->roles_count,
                            'users_count' => $totalUsersWithPermission,
                            'created_at' => $permission->created_at->format('Y-m-d'),
                            'roles' => $permission->roles->map(function ($role) {
                                return [
                                    'id' => $role->id,
                                    'name' => $role->name,
                                    'display_name' => $this->formatDisplayName($role->name),
                                ];
                            }),
                            'users' => $permission->users->map(function ($user) {
                                return [
                                    'id' => $user->id,
                                    'name' => $user->name,
                                    'email' => $user->email,
                                    'user_type' => $user->user_type,
                                ];
                            }),
                        ];
                    }),
                ];
            })
            ->values();

        $allPermissions = Permission::withCount(['roles', 'users'])->get();

        $totalUserAssignments = 0;
        foreach ($allPermissions as $permission) {
            $totalUserAssignments += User::permission($permission->name)->count();
        }

        $stats = [
            'total_permissions' => $allPermissions->count(),
            'total_categories' => $permissions->count(),
            'total_role_assignments' => $allPermissions->sum('roles_count'),
            'total_user_assignments' => $totalUserAssignments,
        ];

        return Inertia::render('permissions/index', [
            'permissions' => $permissions,
            'stats' => $stats,
        ]);
    }

    private function formatDisplayName(string $name): string
    {
        return ucwords(str_replace(['-', '_'], ' ', $name));
    }
}
