<?php

namespace App\Http\Controllers;

use App\Models\Article;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Services\MaterialConstraintsService;
use Illuminate\Http\JsonResponse;

class ArticleController extends Controller
{
    public function index()
    {
        // Get all articles with eager loading
        $articles = Article::with('family')->get();

        return Inertia::render('article/index', [
            'articles' => $articles
        ]);
    }

    /**
     * Fetch article data for AG Grid using Laravel's paginator
     */
    public function fetchData(Request $request)
    {
        try {
            // Parse pagination parameters
            $perPage = $request->input('perPage', 100);
            $startRow = $request->input('startRow', 0);
            $page = floor($startRow / $perPage) + 1;

            // Validate request parameters
            $validated = $request->validate([
                'perPage' => 'nullable|integer|min:1|max:500',
                'sortField' => 'nullable|string',
                'sortOrder' => 'nullable|string|in:asc,desc',
                'filterModel' => 'nullable|array',
            ]);

            // Build the query
            $query = Article::with('family');

            // Apply sorting
            $sortField = $request->input('sortField', 'id');
            $sortOrder = $request->input('sortOrder', 'asc');

            // Handle related table sorting
            if (str_starts_with($sortField, 'family.')) {
                // Join with article_families table to enable sorting on articleFamily fields
                $query->join('article_families', 'articles.article_family_id', '=', 'article_families.id');

                // Extract the field name from the related table
                $familyField = str_replace('family.', '', $sortField);
                $query->orderBy('article_families.' . $familyField, $sortOrder);
            } else {
                // For fields directly on the articles table
                $query->orderBy('articles.' . $sortField, $sortOrder);
            }

            // Apply filtering
            $filterModel = $request->input('filterModel', []);
            foreach ($filterModel as $field => $filter) {
                if (isset($filter['filter'])) {
                    // Handle filtering for fields from related tables
                    if (str_starts_with($field, 'family.')) {
                        if (!$query->getQuery()->joins) {
                            // Only join if not already joined
                            $query->join('article_families', 'articles.article_family_id', '=', 'article_families.id');
                        }
                        $familyField = str_replace('family.', '', $field);
                        $query->where('article_families.' . $familyField, 'like', '%' . $filter['filter'] . '%');
                    } else {
                        $query->where('articles.' . $field, 'like', '%' . $filter['filter'] . '%');
                    }
                }
            }

            // Get paginated results
            $paginator = $query->paginate($perPage, ['*'], 'page', $page);

            // Format response for AG Grid
            return response()->json([
                'rows' => $paginator->items(),
                'lastRow' => $paginator->total(),
                'pagination' => [
                    'current_page' => $paginator->currentPage(),
                    'last_page' => $paginator->lastPage(),
                    'per_page' => $paginator->perPage(),
                    'total' => $paginator->total()
                ]
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'error' => 'Error processing request: ' . $e->getMessage(),
                'rows' => [],
                'lastRow' => 0
            ], 500);
        }
    }

    public function compositionPage(Request $request)
    {
        return Inertia::render('product-composition/index', [
            'id' => $request->query('id')
        ]);
    }

    /**
     * Get a finished product by article ID
     * 
     * @param int $articleId
     * @return JsonResponse
     */
    public function getByArticleId($articleId): JsonResponse
    {
        try {
            // Find the article that is a finished product
            $article = Article::where('id', $articleId)
                           ->where('article_type', 'finished_product')
                           ->with('family')
                           ->firstOrFail();
            
            return response()->json($article);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Produit fini non trouvé pour cet article',
                'error' => $e->getMessage(),
            ], 404);
        }
    }

    /**
     * Get material constraints data for a specific finished product
     * 
     * @param int $finishedProductId
     * @return JsonResponse
     */
    public function getMaterialConstraints($finishedProductId): JsonResponse
    {
        try {
            // First verify that the ID belongs to a valid finished product (an article with type 'finished_product')
            $finishedProduct = Article::where('id', $finishedProductId)
                                     ->where('article_type', 'finished_product')
                                     ->firstOrFail();
                                     
            $materialConstraintsService = new MaterialConstraintsService();
            $constraints = $materialConstraintsService->calculateConstraints($finishedProductId);

            return response()->json([
                'success' => true,
                'material_constrained_lots' => $constraints['material_constrained_lots'] ?? 0,
                'limiting_materials' => $constraints['limiting_materials'] ?? '',
                'critical_materials_detail' => $constraints['critical_materials_detail'] ?? ''
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Impossible de récupérer les contraintes matérielles',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get all primary materials
     * 
     * @return JsonResponse
     */
    public function getPrimaryMaterials(): JsonResponse
    {
        try {
            // Get all primary material articles
            $primaryMaterials = Article::where('article_type', 'primary_material')
                                      ->select('id', 'article_code', 'article_name', 'available_stock', 'material_type', 'unit_of_measure')
                                      ->with('family')
                                      ->get();
            
            return response()->json($primaryMaterials);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching primary materials',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
