<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{

    public function index()
    {
        $currentUser = Auth::user();
        $allUsers = User::with(['roles'])->get();

        // Filter users based on what current user can view
        $visibleUsers = $allUsers->filter(function ($user) use ($currentUser) {
            return $currentUser->can('view', $user);
        });

        $users = $visibleUsers->map(function ($user) use ($currentUser) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'user_type' => $user->user_type,
                'roles' => $user->getRoleNames()->toArray(),
                'permissions_count' => $user->getAllPermissions()->count(),
                'created_at' => $user->created_at->format('Y-m-d'),
                'can_edit' => $currentUser->can('update', $user),
                'can_delete' => $currentUser->can('delete', $user),
            ];
        })->values() // Reset array keys because we used ->filter()
        ->sortBy(function ($user) use ($currentUser) {
            return $user['id'] === $currentUser->id ? 0 : 1;
        })->values();

        $stats = [
            'total_users' => $visibleUsers->count(),
            'superusers' => $visibleUsers->where('user_type', 'superuser')->count(),
            'admins' => $visibleUsers->where('user_type', 'admin')->count(),
            'clients' => $visibleUsers->where('user_type', 'client')->count(),
        ];

        return Inertia::render('users/index', [
            'users' => $users,
            'stats' => $stats,
        ]);
    }

    public function create()
    {
        $roles = Role::all()->map(function ($role) {
            return [
                'id' => $role->id,
                'name' => $role->name,
                'permissions_count' => $role->permissions->count(),
                'available_for' => $this->getRoleAvailableFor($role->name),
            ];
        });

        return Inertia::render('users/create', [
            'roles' => $roles
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'user_type' => 'required|string|in:superuser,admin,client',
            'roles' => 'nullable|array',
            'roles.*' => 'string|exists:roles,name',
        ]);

        // Check if current user can create users of this type
        $currentUser = Auth::user();

        if (!$currentUser->can('createUserType', [User::class, $request->user_type])) {
            return redirect()->back()
                ->with('error', 'You do not have permission to create users of this type.')
                ->withInput();
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'user_type' => $request->user_type,
        ]);

        // Assign roles if provided, but validate each role assignment
        if ($request->has('roles') && !empty($request->roles)) {
            $currentUser = Auth::user();
            $validRoles = [];

            foreach ($request->roles as $roleName) {
                if ($currentUser->can('assignRole', [$user, $roleName])) {
                    $validRoles[] = $roleName;
                }
            }

            if (!empty($validRoles)) {
                $user->assignRole($validRoles);
            }
        }

        return redirect()->route('users.index')
            ->with('message', 'User created successfully');
    }

    public function edit(User $user)
    {
        $user->load('roles');

        $userData = [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'user_type' => $user->user_type ?? 'client',
            'roles' => $user->roles->pluck('name'),
            'direct_permissions' => $user->getDirectPermissions()->pluck('name'), // getDirectPermissions buildin spatie function
            'permissions_count' => $user->getAllPermissions()->count(), // getAllPermissions buildin spatie function
            'created_at' => $user->created_at->format('Y-m-d'),
            'updated_at' => $user->updated_at->format('Y-m-d'),
            'can_edit' => Auth::user()->can('update', $user),
            'can_delete' => Auth::user()->can('delete', $user),
        ];

        return Inertia::render('users/edit', [
            'user' => $userData,
        ]);
    }

    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
        ]);

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
        ]);

        return redirect()->route('users.index', $user)
            ->with('message', 'User profile updated successfully');
    }

    public function destroy(User $user)
    {
        $user->delete();

        return redirect()->route('users.index')
            ->with('message', 'User deleted successfully');
    }

    /**
     * Get which user types can have this role based on current user's permissions.
     */
    private function getRoleAvailableFor(string $roleName): array
    {
        $currentUser = Auth::user();
        $availableUserTypes = [];

        // Test each user type to see if current user can assign this role to them
        $userTypes = ['superuser', 'admin', 'client'];

        foreach ($userTypes as $userType) {
            $dummyUser = new User(['user_type' => $userType]);
            if ($currentUser->can('assignRole', [$dummyUser, $roleName])) {
                $availableUserTypes[] = $userType;
            }
        }

        return $availableUserTypes;
    }
}
