<?php

namespace App\Http\Controllers\Dle;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Section;
use App\Models\Subsection;
use App\Models\Document;
use Illuminate\Support\Facades\DB;

class DocumentSubsectionController extends Controller
{
    public function storeSubsection(Request $request)
    {
        DB::beginTransaction();

        try {
            $request->validate([
                'section_id' => 'required|exists:sections,id',
                'title' => 'required|string',
                'subsection_number' => 'required|integer',
            ]);

            $sectionId = $request->input('section_id');
            $section = Section::findOrFail($sectionId);

            $subsectionNumber = $request->input('subsection_number');

            $existingSubsection = Subsection::where('section_id', $sectionId)
                ->where('subsection_number', $subsectionNumber)
                ->first();

            if ($existingSubsection) {
                return response()->json([
                    'error' => 'A subsection with this number already exists',
                    'subsection_number' => $subsectionNumber,
                ], 422);
            }

            $title = $request->input('title');
            $content = $request->input('content', '<ol><li>' . $title . '</li></ol>');

            $subsection = Subsection::create([
                'section_id' => $sectionId,
                'subsection_number' => $subsectionNumber,
                'title' => $title,
                'content' => $content,
                'metadata' => [
                    'generation_date' => now()->toIso8601String(),
                    'full_number' => $section->section_number . '.' . $subsectionNumber,
                ],
            ]);

            $this->updateParentSectionContent($section, $subsection);

            DB::commit();

            return response()->json([
                'message' => 'Subsection created successfully',
                'subsection' => $subsection,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Failed to create subsection',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function deleteSubsection($id)
    {
        try {
            $subsection = Subsection::findOrFail($id);

            $subsectionId = $subsection->id;

            $subsection->delete();

            return response()->json([
                'message' => 'Subsection deleted successfully',
                'subsection_id' => $subsectionId
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to delete subsection',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function updateSubsection($id, Request $request)
    {
        DB::beginTransaction();

        try {
            $subsection = Subsection::findOrFail($id);
            $section = Section::findOrFail($subsection->section_id);
            $document = Document::findOrFail($section->document_id);

            $subsectionMetadata = $subsection->metadata ?? [];
            $subsectionMetadata['last_edited'] = now()->toIso8601String();
            $subsectionMetadata['edit_count'] = ($subsectionMetadata['edit_count'] ?? 0) + 1;

            $originalContent = $subsection->content;
            $updatedContent = null;

            if ($request->has('content')) {
                $content = $request->input('content');

                if (is_array($content) && isset($content['html']) && isset($content['json'])) {
                    $updatedContent = $content['html'];
                    $subsection->content = $updatedContent;
                    $subsectionMetadata['tiptap_json'] = $content['json'];
                    $subsectionMetadata['content_format'] = 'html_with_json';
                } else if (is_array($content) || is_object($content)) {
                    $subsection->content = json_encode($content);
                    $subsectionMetadata['content_format'] = 'tiptap_json';
                } else if (is_string($content)) {
                    $updatedContent = $content;
                    $subsection->content = $updatedContent;
                    $subsectionMetadata['content_format'] = 'html';
                }

                if ($updatedContent) {
                    $this->updateParentDocument($document, $originalContent, $updatedContent);
                }
            }

            if ($request->has('title')) {
                $subsection->title = $request->input('title');
            }

            $subsection->metadata = $subsectionMetadata;
            $subsection->save();

            DB::commit();

            return response()->json([
                'message' => 'Subsection updated successfully',
                'subsection_id' => $subsection->id,
                'subsection' => $subsection
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Failed to update subsection',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    private function updateParentDocument(Document $document, $originalContent, $updatedContent)
    {
        if (!$originalContent || !$updatedContent) {
            return;
        }

        $documentHtml = $document->html_content;

        if (strpos($documentHtml, $originalContent) !== false) {
            $document->html_content = str_replace($originalContent, $updatedContent, $documentHtml);
            $document->save();
        }
    }

    private function updateParentSectionContent(Section $section, Subsection $subsection)
    {
        $subsectionMarker = "<p><strong>" . $subsection->metadata['full_number'] . ":</strong> " . $subsection->title . "</p>";

        $originalSectionContent = $section->content;

        $section->content .= $subsectionMarker;
        $section->save();

        $document = Document::findOrFail($section->document_id);
        $this->updateParentDocument($document, $originalSectionContent, $section->content);
    }
}
