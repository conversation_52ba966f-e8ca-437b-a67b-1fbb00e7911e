<?php

namespace App\Http\Controllers\Dle;

use App\Http\Controllers\Controller;
use App\Models\Document;
use App\Models\Question;
use App\Models\Section;
use App\Models\Subsection;
use Illuminate\Http\Request;

class QuestionController extends Controller
{
    public function store(Request $request)
    {
        $request->validate([
            'subsection_id' => 'nullable|exists:subsections,id',
            'section_id' => 'nullable|exists:sections,id',
            'question_text' => 'required|string',
        ]);

        if (!$request->subsection_id && !$request->section_id) {
            return response()->json([
                'error' => 'Either subsection_id or section_id is required'
            ], 422);
        }

        if ($request->subsection_id && $request->section_id) {
            return response()->json([
                'error' => 'Cannot specify both subsection_id and section_id'
            ], 422);
        }

        try {
            $questionData = [
                'question_text' => $request->question_text,
            ];

            if ($request->subsection_id) {
                $subsection = Subsection::findOrFail($request->subsection_id);
                $section = Section::findOrFail($subsection->section_id);
                $questionData['subsection_id'] = $request->subsection_id;
            } else {
                $section = Section::findOrFail($request->section_id);
                $questionData['section_id'] = $request->section_id;
            }

            $question = Question::create($questionData);

            $questionMarker = "<p><strong>Q:</strong> " . $request->question_text . "</p>";
            $originalSectionContent = $section->content;

            if ($request->subsection_id) {
                $subsection->content .= $questionMarker;
                $subsection->save();
            }

            $section->content .= $questionMarker;
            $section->save();

            $document = Document::findOrFail($section->document_id);
            $this->updateParentDocument($document, $originalSectionContent, $section->content);

            return response()->json([
                'message' => 'Question created successfully',
                'question' => $question,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to create question',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getQuestionsBySubsection($subsectionId)
    {
        try {
            $subsection = Subsection::findOrFail($subsectionId);
            $questions = $subsection->questions()->get();

            return response()->json([
                'questions' => $questions,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch questions',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getQuestionsBySection($sectionId)
    {
        try {
            $section = Section::findOrFail($sectionId);
            $questions = $section->questions()->get();

            return response()->json([
                'questions' => $questions,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch questions',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getQuestionsByDocument($documentId)
    {
        try {
            Document::findOrFail($documentId);

            $subsectionQuestions = Question::join('subsections', 'questions.subsection_id', '=', 'subsections.id')
                ->join('sections', 'subsections.section_id', '=', 'sections.id')
                ->where('sections.document_id', $documentId)
                ->whereNotNull('questions.subsection_id')
                ->select('questions.*')
                ->with(['subsection.section']);

            $sectionQuestions = Question::join('sections', 'questions.section_id', '=', 'sections.id')
                ->where('sections.document_id', $documentId)
                ->whereNotNull('questions.section_id')
                ->select('questions.*')
                ->with(['section']);

            $questions = $subsectionQuestions->union($sectionQuestions)->get();

            return response()->json([
                'questions' => $questions,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch questions',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $question = Question::findOrFail($id);

            $questionId = $question->id;

            $question->delete();

            return response()->json([
                'message' => 'Question deleted successfully',
                'question_id' => $questionId
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to delete question',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    private function updateParentDocument(Document $document, $originalContent, $updatedContent)
    {
        if (!$originalContent || !$updatedContent) {
            return;
        }

        $documentHtml = $document->html_content;

        if (strpos($documentHtml, $originalContent) !== false) {
            $document->html_content = str_replace($originalContent, $updatedContent, $documentHtml);
            $document->save();
        }
    }
}
