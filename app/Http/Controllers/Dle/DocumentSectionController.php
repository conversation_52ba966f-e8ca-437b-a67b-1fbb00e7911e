<?php

namespace App\Http\Controllers\Dle;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Section;
use App\Models\Subsection;
use App\Models\Document;
use Exception;
use Illuminate\Support\Facades\DB;

class DocumentSectionController extends Controller
{
    public function getSections($id)
    {
        try {
            $document = Document::findOrFail($id);

            $sections = $document->sections()
                ->with([
                    'questions' => function ($query) {
                        $query->orderBy('id', 'asc');
                    },
                    'subsections' => function ($query) {
                        $query->orderBy('subsection_number', 'asc')
                            ->with(['questions' => function ($query) {
                                $query->orderBy('id', 'asc');
                            }]);
                    }
                ])
                ->orderBy('section_number', 'asc')
                ->get();

            return response()->json([
                'document' => [
                    'id' => $document->id,
                    'title' => $document->title,
                    'original_filename' => $document->original_filename,
                ],
                'sections' => $sections
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to retrieve document sections',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function updateSection($id, Request $request)
    {
        DB::beginTransaction();

        try {
            $section = Section::findOrFail($id);
            $document = Document::findOrFail($section->document_id);

            $sectionMetadata = $section->metadata ?? [];
            $sectionMetadata['last_edited'] = now()->toIso8601String();
            $sectionMetadata['edit_count'] = ($sectionMetadata['edit_count'] ?? 0) + 1;

            $originalContent = $section->content;
            $updatedContent = null;

            if ($request->has('content')) {
                $content = $request->input('content');

                if (is_array($content) && isset($content['html']) && isset($content['json'])) {
                    $updatedContent = $content['html'];
                    $section->content = $updatedContent;
                    $sectionMetadata['tiptap_json'] = $content['json'];
                    $sectionMetadata['content_format'] = 'html_with_json';
                } else if (is_array($content) || is_object($content)) {
                    $section->content = json_encode($content);
                    $sectionMetadata['content_format'] = 'tiptap_json';
                } else if (is_string($content)) {
                    $updatedContent = $content;
                    $section->content = $updatedContent;
                    $sectionMetadata['content_format'] = 'html';
                }

                if ($updatedContent) {
                    $this->updateParentDocument($document, $originalContent, $updatedContent);
                }
            }

            $section->metadata = $sectionMetadata;
            $section->save();

            DB::commit();

            return response()->json([
                'message' => 'Section updated successfully',
                'section_id' => $section->id
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Failed to update section',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    private function updateParentDocument(Document $document, $originalContent, $updatedContent)
    {
        if (!$originalContent || !$updatedContent) {
            return;
        }

        $documentHtml = $document->html_content;

        if (strpos($documentHtml, $originalContent) !== false) {
            $document->html_content = str_replace($originalContent, $updatedContent, $documentHtml);
            $document->save();
        }
    }

    public function storeClientSections(Request $request)
    {
        DB::beginTransaction();

        try {
            $request->validate([
                'document_id' => 'required|exists:documents,id',
                'sections' => 'required|array',
                'sections.*.title' => 'required|string',
                'sections.*.content' => 'required|string',
                'sections.*.subsections' => 'array',
                'sections.*.subsections.*.title' => 'required_with:sections.*.subsections|string',
                'sections.*.subsections.*.content' => 'required_with:sections.*.subsections|string',
                'replace_existing' => 'boolean',
            ]);

            $documentId = $request->input('document_id');
            $document = Document::findOrFail($documentId);
            $replaceExisting = $request->input('replace_existing', false);

            $existingSections = Section::where('document_id', $documentId)
                ->with('subsections')
                ->get()
                ->keyBy('section_number');

            $savedSections = [];
            $savedSubsections = [];
            $processedSectionNumbers = [];

            foreach ($request->input('sections') as $index => $sectionData) {
                $sectionNumber = $index + 1;
                $processedSectionNumbers[] = $sectionNumber;

                if ($existingSections->has($sectionNumber)) {
                    $section = $existingSections->get($sectionNumber);

                    $section->title = $sectionData['title'];
                    $section->content = $sectionData['content'];

                    $sectionMetadata = $section->metadata ?? [];
                    $sectionMetadata['last_updated'] = now()->toIso8601String();
                    $section->metadata = $sectionMetadata;

                    $section->save();
                } else {
                    $section = Section::create([
                        'document_id' => $documentId,
                        'section_number' => $sectionNumber,
                        'title' => $sectionData['title'],
                        'content' => $sectionData['content'],
                        'metadata' => [
                            'generation_date' => now()->toIso8601String(),
                        ],
                    ]);
                }

                $savedSections[] = $section->id;

                $existingSubsections = $section->subsections->keyBy('subsection_number');
                $processedSubsectionNumbers = [];

                if (isset($sectionData['subsections']) && is_array($sectionData['subsections'])) {
                    foreach ($sectionData['subsections'] as $subIndex => $subsectionData) {
                        $subsectionNumber = $subIndex + 1;
                        $processedSubsectionNumbers[] = $subsectionNumber;

                        if ($existingSubsections->has($subsectionNumber)) {
                            $subsection = $existingSubsections->get($subsectionNumber);

                            $subsection->title = $subsectionData['title'];
                            $subsection->content = $subsectionData['content'];

                            $subsectionMetadata = $subsection->metadata ?? [];
                            $subsectionMetadata['last_updated'] = now()->toIso8601String();
                            $subsection->metadata = $subsectionMetadata;

                            $subsection->save();
                        } else {
                            $subsection = Subsection::create([
                                'section_id' => $section->id,
                                'subsection_number' => $subsectionNumber,
                                'title' => $subsectionData['title'],
                                'content' => $subsectionData['content'],
                                'metadata' => [
                                    'generation_date' => now()->toIso8601String(),
                                    'full_number' => $sectionNumber . '.' . $subsectionNumber,
                                ],
                            ]);
                        }

                        $savedSubsections[] = $subsection->id;
                    }
                }

                if ($replaceExisting && $existingSubsections->count() > 0) {
                    foreach ($existingSubsections as $subsectionNumber => $subsection) {
                        if (!in_array($subsectionNumber, $processedSubsectionNumbers)) {
                            $subsection->delete();
                        }
                    }
                }
            }

            if ($replaceExisting && $existingSections->count() > 0) {
                foreach ($existingSections as $sectionNumber => $section) {
                    if (!in_array($sectionNumber, $processedSectionNumbers)) {
                        $section->delete();
                    }
                }
            }

            $metadata = $document->metadata ?? [];
            $metadata['sections_updated'] = now()->toIso8601String();
            $metadata['sections_count'] = count($savedSections);
            $metadata['subsections_count'] = count($savedSubsections);

            $document->metadata = $metadata;
            $document->save();

            DB::commit();

            return response()->json([
                'message' => 'Sections and subsections saved successfully',
                'document_id' => $documentId,
                'section_count' => count($savedSections),
                'subsection_count' => count($savedSubsections),
                'sections' => $savedSections,
                'subsections' => $savedSubsections,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Failed to save sections',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function deleteSection($id)
    {
        try {
            $section = Section::findOrFail($id);

            $sectionId = $section->id;

            $section->delete();

            return response()->json([
                'message' => 'Section deleted successfully',
                'section_id' => $sectionId
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to delete section',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function storeSection(Request $request)
    {
        DB::beginTransaction();

        try {
            $request->validate([
                'document_id' => 'required|exists:documents,id',
                'title' => 'required|string',
                'section_number' => 'required|integer',
            ]);

            $documentId = $request->input('document_id');
            $document = Document::findOrFail($documentId);

            $sectionNumber = $request->input('section_number');

            $existingSection = Section::where('document_id', $documentId)
                ->where('section_number', $sectionNumber)
                ->first();

            if ($existingSection) {
                return response()->json([
                    'error' => 'A section with this number already exists',
                    'section_number' => $sectionNumber,
                ], 422);
            }

            $title = $request->input('title');
            $content = $request->input('content', '<ol><li>' . $title . '</li></ol>');

            $section = Section::create([
                'document_id' => $documentId,
                'section_number' => $sectionNumber,
                'title' => $title,
                'content' => $content,
                'metadata' => [
                    'generation_date' => now()->toIso8601String(),
                ],
            ]);

            $document->save();

            DB::commit();

            return response()->json([
                'message' => 'Section created successfully',
                'section' => $section,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => 'Failed to create section',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
