<?php

namespace App\Http\Controllers\Dle;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Str;
use App\Models\Document;
use App\Models\Section;
use App\Models\Subsection;
use Exception;
use Illuminate\Support\Facades\DB;

class DocumentConversionController extends Controller
{
    protected string $tempDisk = 'temp';

    public function index()
    {
        try {
            $documents = Document::orderBy('created_at', 'desc')->get();

            return response()->json([
                'documents' => $documents
            ]);
        } catch (Exception $e) {
            return response()->json([
                'error' => 'Failed to retrieve documents',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function convertToRawHtml(Request $request)
    {
        $filename = null;
        $outputFilename = null;

        try {
            $request->validate([
                'document' => 'required|file|mimes:docx'
            ]);

            if (!Storage::disk($this->tempDisk)->exists('')) {
                Storage::disk($this->tempDisk)->makeDirectory('');
            }

            $file = $request->file('document');
            $originalFileName = $file->getClientOriginalName();
            $fileSize = $file->getSize();
            $mimeType = $file->getMimeType();
            $fileContents = file_get_contents($file->getRealPath());
            $fileHash = hash('sha256', $fileContents);

            $existingDocument = Document::findByHash($fileHash);

            if ($existingDocument) {
                return response()->json([
                    'html' => $existingDocument->html_content,
                    'document_id' => $existingDocument->id,
                    'message' => 'Document retrieved from database',
                    'is_cached' => true
                ]);
            }

            $filename = Str::random(20) . '.docx';
            $file->storeAs('', $filename, $this->tempDisk);

            $inputPath = Storage::disk($this->tempDisk)->path($filename);
            $outputFilename = Str::random(20) . '.html';
            $outputPath = Storage::disk($this->tempDisk)->path($outputFilename);

            $process = Process::run("pandoc -f docx -t html \"{$inputPath}\" -o \"{$outputPath}\"");

            if (!$process->successful()) {
                if ($filename) Storage::disk($this->tempDisk)->delete($filename);
                return response()->json(['error' => 'Pandoc conversion to HTML failed.'], 500);
            }

            $html = Storage::disk($this->tempDisk)->get($outputFilename);
            $title = pathinfo($originalFileName, PATHINFO_FILENAME);

            $document = Document::create([
                'title' => $title,
                'original_filename' => $originalFileName,
                'file_hash' => $fileHash,
                'mime_type' => $mimeType,
                'file_size' => $fileSize,
                'html_content' => $html,
                'metadata' => [
                    'conversion_date' => now()->toIso8601String(),
                ]
            ]);

            $cleanupFiles = [$filename, $outputFilename];
            Storage::disk($this->tempDisk)->delete(array_filter($cleanupFiles));

            return response()->json([
                'html' => $html,
                'document_id' => $document->id,
                'message' => 'Document converted to HTML and stored successfully',
                'is_cached' => false
            ]);
        } catch (Exception $e) {
            $cleanupFiles = [];
            if ($filename && Storage::disk($this->tempDisk)->exists($filename)) $cleanupFiles[] = $filename;
            if ($outputFilename && Storage::disk($this->tempDisk)->exists($outputFilename)) $cleanupFiles[] = $outputFilename;

            if (!empty($cleanupFiles)) {
                Storage::disk($this->tempDisk)->delete($cleanupFiles);
            }

            return response()->json([
                'error' => 'An unexpected error occurred during document conversion.',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getStoredDocument($id)
    {
        try {
            $document = Document::findOrFail($id);

            return response()->json([
                'html' => $document->html_content,
                'document_id' => $document->id,
                'original_filename' => $document->original_filename,
                'file_size' => $document->file_size,
                'metadata' => $document->metadata
            ]);
        } catch (Exception $e) {
            return response()->json([
                'error' => 'Document not found or could not be retrieved',
                'message' => $e->getMessage()
            ], 404);
        }
    }

    public function update($id, Request $request)
    {
        try {
            $document = Document::findOrFail($id);

            $metadata = $document->metadata ?? [];
            $metadata['last_edited'] = now()->toIso8601String();
            $metadata['edit_count'] = ($metadata['edit_count'] ?? 0) + 1;

            if ($request->has('content')) {
                $content = $request->input('content');

                if (is_array($content) && isset($content['html']) && isset($content['json'])) {
                    $document->html_content = $content['html'];

                    $metadata['tiptap_json'] = $content['json'];
                    $metadata['content_format'] = 'html_with_json';
                } else if (is_array($content) || is_object($content)) {
                    $document->html_content = json_encode($content);
                    $metadata['content_format'] = 'tiptap_json';
                } else if (is_string($content)) {
                    $document->html_content = $content;
                    $metadata['content_format'] = 'html';
                }
            }

            $document->metadata = $metadata;
            $document->save();

            return response()->json([
                'message' => 'Document updated successfully',
                'document_id' => $document->id
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to update document',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $document = Document::findOrFail($id);

            $document->sections()->delete();

            $document->delete();

            return response()->json([
                'message' => 'Document deleted successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'error' => 'Failed to delete document',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function duplicateDocument($id)
    {
        try {
            $originalDocument = Document::findOrFail($id);

            DB::beginTransaction();

            $duplicatedDocument = new Document([
                'title' => 'Copie de ' . $originalDocument->title,
                'original_filename' => 'Copie de ' . $originalDocument->original_filename,
                'file_hash' => hash('sha256', $originalDocument->file_hash . time()),
                'mime_type' => $originalDocument->mime_type,
                'file_size' => $originalDocument->file_size,
                'html_content' => $originalDocument->html_content,
            ]);

            $metadata = $originalDocument->metadata ?? [];
            $metadata['duplicated_from'] = $originalDocument->id;
            $metadata['duplicated_at'] = now()->toIso8601String();
            $duplicatedDocument->metadata = $metadata;

            $duplicatedDocument->save();

            $rootSections = $originalDocument->sections;
            $sectionIdMap = [];

            foreach ($rootSections as $rootSection) {
                $duplicatedRootSection = $this->duplicateSection($rootSection, $duplicatedDocument->id);
                $sectionIdMap[$rootSection->id] = $duplicatedRootSection->id;
            }

            DB::commit();

            return response()->json([
                'message' => 'Document duplicated successfully',
                'original_document_id' => $originalDocument->id,
                'duplicated_document_id' => $duplicatedDocument->id
            ]);
        } catch (Exception $e) {
            DB::rollBack();

            return response()->json([
                'error' => 'Failed to duplicate document',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    private function duplicateSection(Section $originalSection, int $documentId): Section
    {
        $duplicatedSection = new Section([
            'document_id' => $documentId,
            'section_number' => $originalSection->section_number,
            'title' => $originalSection->title,
            'content' => $originalSection->content,
            'metadata' => $originalSection->metadata
        ]);

        $duplicatedSection->save();

        // Duplicate subsections
        foreach ($originalSection->subsections as $subsection) {
            $duplicatedSubsection = new Subsection([
                'section_id' => $duplicatedSection->id,
                'subsection_number' => $subsection->subsection_number,
                'title' => $subsection->title,
                'content' => $subsection->content,
                'metadata' => $subsection->metadata
            ]);

            $duplicatedSubsection->save();
        }

        return $duplicatedSection;
    }
}
