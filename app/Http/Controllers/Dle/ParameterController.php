<?php

namespace App\Http\Controllers\Dle;

use App\Http\Controllers\Controller;
use App\Models\Parameter;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Exception;

class ParameterController extends Controller
{
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'question_id' => 'required|exists:questions,id',
            'header_orientation' => 'required|in:vertical,horizontal',
            'headers' => 'required|json',
            'values' => 'required|json',
        ]);

        try {
            DB::beginTransaction();

            // Create the parameter
            $parameter = Parameter::create([
                'question_id' => $validated['question_id'],
                'header_orientation' => $validated['header_orientation'],
                'headers' => json_decode($validated['headers']),
            ]);

            // Store the parameter values using the new normalized structure
            $valuesArray = json_decode($validated['values'], true);
            $parameter->storeParameterValues($valuesArray);

            DB::commit();

            // Load the parameter values for the response
            $parameter->load('parameterValues');

            return response()->json([
                'id' => $parameter->id,
                'question_id' => $parameter->question_id,
                'header_orientation' => $parameter->header_orientation,
                'headers' => $parameter->headers,
                'values' => $parameter->getParameterValuesArray(),
                'created_at' => $parameter->created_at,
                'updated_at' => $parameter->updated_at,
            ], 201);

        } catch (Exception $e) {
            DB::rollBack();

            return response()->json([
                'error' => 'Failed to store parameter',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified parameter.
     */
    public function show(Parameter $parameter): JsonResponse
    {
        $parameter->load('parameterValues');

        return response()->json([
            'id' => $parameter->id,
            'question_id' => $parameter->question_id,
            'header_orientation' => $parameter->header_orientation,
            'headers' => $parameter->headers,
            'values' => $parameter->getParameterValuesArray(),
            'created_at' => $parameter->created_at,
            'updated_at' => $parameter->updated_at,
        ]);
    }

    /**
     * Update the specified parameter.
     */
    public function update(Request $request, Parameter $parameter): JsonResponse
    {
        $validated = $request->validate([
            'header_orientation' => 'sometimes|in:vertical,horizontal',
            'headers' => 'sometimes|json',
            'values' => 'sometimes|json',
        ]);

        try {
            DB::beginTransaction();

            // Update parameter basic fields
            if (isset($validated['header_orientation'])) {
                $parameter->header_orientation = $validated['header_orientation'];
            }

            if (isset($validated['headers'])) {
                $parameter->headers = json_decode($validated['headers']);
            }

            $parameter->save();

            // Update parameter values if provided
            if (isset($validated['values'])) {
                $valuesArray = json_decode($validated['values'], true);
                $parameter->storeParameterValues($valuesArray);
            }

            DB::commit();

            // Load the parameter values for the response
            $parameter->load('parameterValues');

            return response()->json([
                'id' => $parameter->id,
                'question_id' => $parameter->question_id,
                'header_orientation' => $parameter->header_orientation,
                'headers' => $parameter->headers,
                'values' => $parameter->getParameterValuesArray(),
                'created_at' => $parameter->created_at,
                'updated_at' => $parameter->updated_at,
            ]);

        } catch (Exception $e) {
            DB::rollBack();

            return response()->json([
                'error' => 'Failed to update parameter',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified parameter.
     */
    public function destroy(Parameter $parameter): JsonResponse
    {
        try {
            $parameter->delete(); // This will cascade delete parameter values

            return response()->json([
                'message' => 'Parameter deleted successfully'
            ]);

        } catch (Exception $e) {
            return response()->json([
                'error' => 'Failed to delete parameter',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get parameters by question ID.
     */
    public function getParametersByQuestion(int $questionId): JsonResponse
    {
        try {
            $parameters = Parameter::where('question_id', $questionId)
                ->with('parameterValues')
                ->get()
                ->map(function ($parameter) {
                    return [
                        'id' => $parameter->id,
                        'question_id' => $parameter->question_id,
                        'header_orientation' => $parameter->header_orientation,
                        'headers' => $parameter->headers,
                        'values' => $parameter->getParameterValuesArray(),
                        'created_at' => $parameter->created_at,
                        'updated_at' => $parameter->updated_at,
                    ];
                });

            return response()->json($parameters);

        } catch (Exception $e) {
            return response()->json([
                'error' => 'Failed to retrieve parameters',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
