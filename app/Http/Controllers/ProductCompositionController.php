<?php

namespace App\Http\Controllers;

use App\Models\Article;
use App\Models\ProductComposition;
use Illuminate\Http\Request;

class ProductCompositionController extends Controller
{
    /**
     * Get all compositions for a specific finished product
     */
    public function index(Request $request)
    {
        $request->validate([
            'finished_product_id' => 'required|exists:articles,id',
        ]);

        $compositions = ProductComposition::with(['primaryMaterialArticle'])
            ->where('finished_product_article_id', $request->finished_product_id)
            ->get();

        return response()->json($compositions);
    }

    /**
     * Store a newly created product composition in storage.
     */
    public function store(Request $request)
    {
        // Validate the request data
        $validated = $request->validate([
            'finished_product_id' => 'required|exists:articles,id',
            'primary_material_id' => [
                'required',
                'exists:articles,id',
            ],
            'quantity_per_unit' => 'required|numeric|gt:0',
            'unit_of_measure' => 'required|string|in:g,mg,kg,ml,l',
        ]);

        try {
            // Get the finished product to retrieve batch size
            $finishedProduct = Article::where('id', $validated['finished_product_id'])
                                      ->where('article_type', 'finished_product')
                                      ->firstOrFail();
            $batchSize = $finishedProduct->batch_size;

            // Calculate quantity per lot
            $quantityPerLot = $validated['quantity_per_unit'] * $batchSize;

            // Create a new composition
            $composition = ProductComposition::create([
                'finished_product_article_id' => $validated['finished_product_id'],
                'primary_material_article_id' => $validated['primary_material_id'],
                'quantity_per_unit' => $validated['quantity_per_unit'],
                'quantity_per_lot' => $quantityPerLot,
                'unit_of_measure' => $validated['unit_of_measure'],
            ]);

            return response()->json($composition, 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create product composition',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a product composition
     */
    public function destroy($id)
    {
        try {
            $composition = ProductComposition::findOrFail($id);
            $composition->delete();
            
            return response()->json(['message' => 'Composition deleted successfully']);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete composition',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
