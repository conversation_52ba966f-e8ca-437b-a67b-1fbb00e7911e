<?php

namespace App\Http\Controllers;

use App\Models\ArticleFamily;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ArticleFamilyController extends Controller
{
    /**
     * Display the article family index page
     */
    public function index()
    {
        return Inertia::render('article-family/index');
    }

    /**
     * Fetch article family data for AG Grid using Laravel's paginator
     */
    public function fetchData(Request $request)
    {
        try {
            // Parse pagination parameters
            $perPage = $request->input('perPage', 100);
            $startRow = $request->input('startRow', 0);
            $page = floor($startRow / $perPage) + 1;

            // Validate request parameters
            $validated = $request->validate([
                'perPage' => 'nullable|integer|min:1|max:500',
                'sortField' => 'nullable|string',
                'sortOrder' => 'nullable|string|in:asc,desc',
                'filterModel' => 'nullable|array',
            ]);

            // Build the query
            $query = ArticleFamily::query();

            // Apply sorting
            $sortField = $request->input('sortField', 'id');
            $sortOrder = $request->input('sortOrder', 'asc');
            $query->orderBy($sortField, $sortOrder);

            // Apply filtering
            $filterModel = $request->input('filterModel', []);
            foreach ($filterModel as $field => $filter) {
                if (isset($filter['filter'])) {
                    $query->where($field, 'like', '%' . $filter['filter'] . '%');
                }
            }

            // Get paginated results
            $paginator = $query->paginate($perPage, ['*'], 'page', $page);

            // Format response for AG Grid
            return response()->json([
                'rows' => $paginator->items(),
                'lastRow' => $paginator->total(),
                'pagination' => [
                    'current_page' => $paginator->currentPage(),
                    'last_page' => $paginator->lastPage(),
                    'per_page' => $paginator->perPage(),
                    'total' => $paginator->total()
                ]
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'error' => 'Error processing request',
                'rows' => [],
                'lastRow' => 0
            ], 500);
        }
    }
}
