<?php

namespace App\Policies;

use App\Models\Article;
use App\Models\User;

class ArticlePolicy
{
    public function viewAny(User $user): bool
    {
        return $user->can('view-article');
    }

    public function view(User $user, Article $article): bool
    {
        return $user->can('view-article');
    }

    public function create(User $user): bool
    {
        return $user->can('create-article');
    }

    public function update(User $user, Article $article): bool
    {
        return $user->can('edit-article');
    }

    public function delete(User $user, Article $article): bool
    {
        return $user->can('delete-article');
    }

    public function import(User $user): bool
    {
        return $user->can('import-article');
    }
}
