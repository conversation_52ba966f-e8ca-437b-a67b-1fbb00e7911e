<?php

namespace App\Policies;

use App\Models\User;
use Spatie\Permission\Models\Role;

class RolePolicy
{
    public function viewAny(User $user): bool
    {
        return $user->can('view-role');
    }

    public function view(User $user, Role $role): bool
    {
        if (!$user->can('view-role')) {
            return false;
        }

        // Apply hierarchy-based role visibility
        return $this->canViewRole($user, $role);
    }

    public function create(User $user): bool
    {
        return $user->can('create-role');
    }

    public function update(User $user, Role $role): bool
    {
        // Check if user has permission to edit roles
        if (!$user->can('edit-role')) {
            return false;
        }

        // System roles cannot be edited
        $systemRoles = ['super-user'];
        return !in_array($role->name, $systemRoles);
    }

    public function delete(User $user, Role $role): bool
    {
        // Check if user has permission to delete roles
        if (!$user->can('delete-role')) {
            return false;
        }

        // Default roles cannot be deleted
        $defaultRoles = ['super-user', 'admin'];
        if (in_array($role->name, $defaultRoles)) {
            return false;
        }

        // Roles with assigned users cannot be deleted
        return $role->users()->count() === 0;
    }

    public function assignPermissions(User $user, Role $role): bool
    {
        return $user->can('assign-permission') && $this->update($user, $role);
    }

    private function canViewRole(User $user, Role $role): bool
    {
        // Superusers can view all roles
        if ($user->user_type === 'superuser') {
            return true;
        }

        // Admin users cannot view superuser roles
        if ($user->user_type === 'admin') {
            $superuserRoles = ['super-user'];
            return !in_array($role->name, $superuserRoles);
        }

        // Client users can view all non-system roles (if they have view-role permission)
        if ($user->user_type === 'client') {
            $systemRoles = ['super-user', 'admin'];
            return !in_array($role->name, $systemRoles);
        }

        return false;
    }
}
