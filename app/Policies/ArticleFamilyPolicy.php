<?php

namespace App\Policies;

use App\Models\ArticleFamily;
use App\Models\User;

class ArticleFamilyPolicy
{
    public function viewAny(User $user): bool
    {
        return $user->can('view-article-family');
    }

    public function view(User $user, ArticleFamily $articleFamily): bool
    {
        return $user->can('view-article-family');
    }

    public function create(User $user): bool
    {
        return $user->can('create-article-family');
    }

    public function update(User $user, ArticleFamily $articleFamily): bool
    {
        return $user->can('edit-article-family');
    }

    public function delete(User $user, ArticleFamily $articleFamily): bool
    {
        return $user->can('delete-article-family');
    }

    public function export(User $user): bool
    {
        return $user->can('export-article-family');
    }

    public function filter(User $user): bool
    {
        return $user->can('filter-article-family');
    }
}
