<?php

namespace App\Policies;

use App\Models\User;

class UserPolicy
{
    public function viewAny(User $user): bool
    {
        return $user->can('view-user');
    }

    public function view(User $user, User $model): bool
    {
        if (!$user->can('view-user')) {
            return false;
        }

        // Apply hierarchy-based visibility
        return $this->canViewUserType($user, $model);
    }

    public function create(User $user): bool
    {
        return $user->can('create-user');
    }

    public function createUserType(User $user, string $userType): bool
    {
        // Must have basic create permission first
        if (!$user->can('create-user')) {
            return false;
        }

        // Clients cannot create any users
        if ($user->user_type === 'client') {
            return false;
        }

        // Superusers can create any user type
        if ($user->user_type === 'superuser') {
            return true;
        }

        // Admins can create admin and client users, but not superusers
        if ($user->user_type === 'admin') {
            return in_array($userType, ['admin', 'client']);
        }

        return false;
    }

    public function update(User $user, User $model): bool
    {
        if (!$user->can('edit-user')) {
            return false;
        }

        // User type hierarchy: superuser > admin > client
        // Users can only edit users of lower or equal hierarchy level
        return $this->canManageUserType($user, $model);
    }

    public function delete(User $user, User $model): bool
    {
        if (!$user->can('delete-user')) {
            return false;
        }

        if ($user->id === $model->id) {
            return false;
        }

        // User type hierarchy: superuser > admin > client
        // Users can only delete users of lower hierarchy level (admins cannot touch superusers)
        return $this->canManageUserType($user, $model);
    }

    private function canAccessUserType(User $authUser, User $targetUser, string $action = 'view'): bool
    {
        $hierarchy = [
            'superuser' => 3,
            'admin' => 2,
            'client' => 1,
        ];

        $targetLevel = $hierarchy[$targetUser->user_type] ?? 0;

        // Allow self-editing for management actions
        if ($action === 'manage' && $authUser->id === $targetUser->id) {
            return true;
        }

        // Superusers can view/manage anyone
        if ($authUser->user_type === 'superuser') {
            return true;
        }

        // Admins logic
        if ($authUser->user_type === 'admin') {
            return $targetLevel <= 2; // Can view/manage admin (2) and client (1), but not superuser (3)
        }

        // Clients logic
        if ($authUser->user_type === 'client') {
            return $targetLevel <= 1; // Can only view/manage client (1)
        }

        return false;
    }

    private function canViewUserType(User $authUser, User $targetUser): bool
    {
        return $this->canAccessUserType($authUser, $targetUser, 'view');
    }

    private function canManageUserType(User $authUser, User $targetUser): bool
    {
        return $this->canAccessUserType($authUser, $targetUser, 'manage');
    }

    public function assignRole(User $user, User $model, string $roleName): bool
    {
        // Clients cannot assign any roles
        if ($user->user_type === 'client') {
            return false;
        }

        // Superusers can assign any role to anyone
        if ($user->user_type === 'superuser') {
            return true;
        }

        // Admins can assign roles based on simple rules:
        if ($user->user_type === 'admin') {
            // 1. Can assign 'admin' role to admin and client users (for promotion/maintenance)
            if ($roleName === 'admin' && in_array($model->user_type, ['admin', 'client'])) {
                return true;
            }

            // 2. Can assign non-system roles to admin and client users
            $systemRoles = ['super-user', 'admin'];
            if (!in_array($roleName, $systemRoles)) {
                return in_array($model->user_type, ['admin', 'client']);
            }
        }

        return false;
    }

    public function updateUserRoles(User $user, User $model, array $rolesToAssign): bool
    {
        if (!$this->update($user, $model)) {
            return false;
        }

        foreach ($rolesToAssign as $roleName) {
            if (!$this->assignRole($user, $model, $roleName)) {
                return false;
            }
        }

        $this->handleUserTypeChange($model, $rolesToAssign);

        return true;
    }

    private function handleUserTypeChange(User $model, array $rolesToAssign): void
    {
        $hasAdminRole = in_array('admin', $rolesToAssign);
        $hasNonAdminRoles = !empty(array_diff($rolesToAssign, ['admin']));

        // Handle admin → client (downgrade)
        if ($model->user_type === 'admin' && !$hasAdminRole && $hasNonAdminRoles) {
            $model->update(['user_type' => 'client']);
        }

        // Handle client → admin (upgrade)
        if ($model->user_type === 'client' && $hasAdminRole) {
            $model->update(['user_type' => 'admin']);
        }
    }
}
