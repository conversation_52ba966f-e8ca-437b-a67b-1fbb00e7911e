<?php

namespace App\Services;

use App\Models\Article;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class MaterialConstraintsService
{
    /**
     * Calculate material constraints for a finished product
     * 
     * @param int $finishedProductId
     * @return array
     */
    public function calculateConstraints(int $finishedProductId): array
    {
        try {
            // Get the finished product (now an Article) with compositions
            $finishedProduct = Article::where('id', $finishedProductId)
                                    ->where('article_type', 'finished_product')
                                    ->with('compositionsAsFinishedProduct.primaryMaterialArticle')
                                    ->firstOrFail();
            
            // Calculate constraints for each material
            $materialsData = $this->calculateMaterialData($finishedProduct);
            
            // Calculate the final constraints
            return $this->buildConstraintsResult($materialsData);
        } catch (ModelNotFoundException) {
            // Handle the case where the finished product is not found
            return [
                'material_constrained_lots' => 0,
                'limiting_materials' => '',
                'critical_materials_detail' => ''
            ];
        }
    }
    
    /**
     * Calculate data for each material in the product composition
     * 
     * @param Article $finishedProduct
     * @return Collection
     */
    private function calculateMaterialData(Article $finishedProduct): Collection
    {
        return $finishedProduct->compositionsAsFinishedProduct->map(function ($composition) {
            $primaryMaterialArticle = $composition->primaryMaterialArticle;
            
            // Calculate how many lots can be produced with this material
            $possibleLots = floor($primaryMaterialArticle->available_stock / $composition->quantity_per_lot);
            
            // Calculate if this material is insufficient for a single lot
            $isInsufficient = $primaryMaterialArticle->available_stock < $composition->quantity_per_lot;
            
            // Calculate how much more material is needed for one lot
            $materialNeededForOneLot = $isInsufficient 
                ? $composition->quantity_per_lot - $primaryMaterialArticle->available_stock 
                : 0;
                
            return [
                'primary_material_id' => $primaryMaterialArticle->id,
                'article_code' => $primaryMaterialArticle->article_code,
                'article_name' => $primaryMaterialArticle->article_name,
                'material_type' => $primaryMaterialArticle->material_type,
                'quantity_per_unit' => $composition->quantity_per_unit,
                'quantity_per_lot' => $composition->quantity_per_lot,
                'unit_of_measure' => $composition->unit_of_measure,
                'available_stock' => $primaryMaterialArticle->available_stock,
                'possible_lots' => $possibleLots,
                'is_insufficient' => $isInsufficient,
                'material_needed_for_one_lot' => $materialNeededForOneLot
            ];
        });
    }
    
    /**
     * Build the final constraints result from material data
     * 
     * @param Collection $materialsData
     * @return array
     */
    private function buildConstraintsResult(Collection $materialsData): array
    {
        // If no materials data, return zero constraints
        if ($materialsData->isEmpty()) {
            return [
                'material_constrained_lots' => 0,
                'limiting_materials' => '',
                'critical_materials_detail' => ''
            ];
        }
        
        // Find minimum possible lots across all materials
        $materialConstrainedLots = $materialsData->min('possible_lots');
        
        // Sort materials by possible lots (ascending) and then by name
        $sortedMaterials = $materialsData->sortBy([
            ['possible_lots', 'asc'],
            ['article_name', 'asc']
        ]);
        
        // Generate limiting materials string
        $limitingMaterials = $sortedMaterials
            ->map(function ($material) {
                return "{$material['article_name']} ({$material['possible_lots']} lots, {$material['available_stock']} {$material['unit_of_measure']} disponible)";
            })
            ->join(', ');
            
        // Generate critical materials detail
        $criticalMaterialsDetail = $sortedMaterials
            ->map(function ($material) {
                return "{$material['article_name']} ({$material['available_stock']} {$material['unit_of_measure']} disponible, nécessite {$material['quantity_per_lot']} {$material['unit_of_measure']} par lot)";
            })
            ->join("\n");
            
        return [
            'material_constrained_lots' => $materialConstrainedLots ?? 0,
            'limiting_materials' => $limitingMaterials,
            'critical_materials_detail' => $criticalMaterialsDetail
        ];
    }
}
