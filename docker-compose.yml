services:
  db:
    image: postgres:17
    container_name: pharmes_advantryx_v1_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: pharmes_advantryx_v1
      POSTGRES_USER: laravel
      POSTGRES_PASSWORD: password
      POSTGRES_ROOT_PASSWORD: admin
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - pharmes_advantryx_v1

networks:
  pharmes_advantryx_v1:
    driver: bridge

volumes:
  postgres_data:
    driver: local
