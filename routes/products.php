<?php

use App\Http\Controllers\ArticleController;
use App\Http\Controllers\ArticleFamilyController;
use App\Http\Controllers\ProductCompositionController;
use App\Models\Article;
use App\Models\ArticleFamily;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'verified'])->group(function () {
    // Article Family Routes
    Route::get('/article-families', [ArticleFamilyController::class, 'index'])->name('article-families.index')->can('viewAny', ArticleFamily::class);
    Route::get('/article-families/data', [ArticleFamilyController::class, 'fetchData'])->name('article-families.data')->can('viewAny', ArticleFamily::class);

    // Article Routes
    Route::get('/articles', [ArticleController::class, 'index'])->name('articles.index')->can('viewAny', Article::class);
    Route::get('/articles/data', [ArticleController::class, 'fetchData'])->name('articles.data')->can('viewAny', Article::class);

    // Product Composition Routes
    Route::get('/product-composition', [ArticleController::class, 'compositionPage'])->name('product-composition.page');
    Route::get('/finished-products/by-article/{articleId}', [ArticleController::class, 'getByArticleId'])->name('finished-products.by-article');
    Route::get('/finished-products/{id}/material-constraints', [ArticleController::class, 'getMaterialConstraints'])->name('finished-products.material-constraints');
    Route::get('/product-compositions', [ProductCompositionController::class, 'index'])->name('product-compositions.index');
    Route::post('/product-compositions', [ProductCompositionController::class, 'store'])->name('product-compositions.store');
    Route::delete('/product-compositions/{id}', [ProductCompositionController::class, 'destroy'])->name('product-compositions.destroy');

    // Primary Material Routes
    Route::get('/primary-materials', [ArticleController::class, 'getPrimaryMaterials'])->name('primary-materials.index');
});
