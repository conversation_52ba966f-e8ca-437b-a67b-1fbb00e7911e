<?php

use App\Http\Controllers\Permissions\PermissionController;
use App\Http\Controllers\Permissions\RoleController;
use App\Http\Controllers\Permissions\UserRoleController;
use App\Http\Controllers\UserController;
use App\Models\User;
use Illuminate\Support\Facades\Route;
use Spatie\Permission\Models\Role;

Route::middleware(['auth', 'verified'])->group(function () {
    // User Management Routes
    Route::get('/users', [UserController::class, 'index'])->name('users.index')->can('viewAny', User::class);
    Route::get('/users/create', [UserController::class, 'create'])->name('users.create')->can('create', User::class);
    Route::post('/users', [UserController::class, 'store'])->name('users.store')->can('create', User::class);
    Route::get('/users/{user}/edit', [UserController::class, 'edit'])->name('users.edit')->can('view', 'user');
    Route::put('/users/{user}', [UserController::class, 'update'])->name('users.update')->can('update', 'user');
    Route::delete('/users/{user}', [UserController::class, 'destroy'])->name('users.destroy')->can('delete', 'user');

    // User Role Management Routes
    Route::get('/users/{user}/roles', [UserRoleController::class, 'show'])->name('users.roles')->can('view', 'user');
    Route::put('/users/{user}/roles', [UserRoleController::class, 'update'])->name('users.roles.update')->can('update', 'user');
    Route::post('/users/{user}/roles/assign', [UserRoleController::class, 'assignRole'])->name('users.roles.assign')->can('update', 'user');
    Route::delete('/users/{user}/roles/remove', [UserRoleController::class, 'removeRole'])->name('users.roles.remove')->can('update', 'user');

    // Role Management Routes
    Route::get('/roles', [RoleController::class, 'index'])->name('roles.index')->can('viewAny', Role::class);
    Route::get('/roles/create', [RoleController::class, 'create'])->name('roles.create')->can('create', Role::class);
    Route::post('/roles', [RoleController::class, 'store'])->name('roles.store')->can('create', Role::class);
    Route::get('/roles/{role}/edit', [RoleController::class, 'edit'])->name('roles.edit')->can('view', 'role');
    Route::put('/roles/{role}', [RoleController::class, 'update'])->name('roles.update')->can('update', 'role');
    Route::delete('/roles/{role}', [RoleController::class, 'destroy'])->name('roles.destroy')->can('delete', 'role');
    Route::put('roles/{role}/permissions', [RoleController::class, 'updatePermissions'])->name('roles.permissions.update')->can('assignPermissions', 'role');

    // Permission Management Route
    Route::get('permissions', [PermissionController::class, 'index'])->name('permissions.index');
});
