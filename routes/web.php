<?php

use App\Http\Controllers\CategoryController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    Route::resource('categories', CategoryController::class);
});

require __DIR__ . '/permissions.php';
require __DIR__ . '/products.php';
require __DIR__ . '/dle.php';
require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
