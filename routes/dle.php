<?php

use App\Http\Controllers\Dle\DocumentConversionController;
use App\Http\Controllers\Dle\DocumentSectionController;
use App\Http\Controllers\Dle\DocumentSubsectionController;
use App\Http\Controllers\Dle\ParameterController;
use App\Http\Controllers\Dle\QuestionController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::middleware(['auth', 'verified'])->group(function () {
    // DLE: Dossier de lot electronique
    Route::get('allin-editor', function () {
        return Inertia::render('allin-editor/index');
    })->name('allin-editor');

    Route::post('convert-docx-to-raw-html', [DocumentConversionController::class, 'convertToRawHtml'])->name('convert.raw-html');
    Route::get('pandoc-documents', [DocumentConversionController::class, 'index'])->name('pandoc-documents.index');
    Route::get('pandoc-documents/{id}', [DocumentConversionController::class, 'getStoredDocument'])->name('pandoc-documents.show');
    Route::put('pandoc-documents/{id}', [DocumentConversionController::class, 'update'])->name('pandoc-documents.update');
    Route::delete('pandoc-documents/{id}', [DocumentConversionController::class, 'destroy'])->name('pandoc-documents.destroy');
    Route::post('pandoc-documents/{id}/duplicate', [DocumentConversionController::class, 'duplicateDocument'])->name('pandoc-documents.duplicate');

    // Document sections
    Route::get('pandoc-documents/{id}/sections', [DocumentSectionController::class, 'getSections'])->name('pandoc-documents.sections');
    Route::get('document-sections/{id}', [DocumentSectionController::class, 'getSections'])->name('document-sections.get');
    Route::put('document-sections/{id}', [DocumentSectionController::class, 'updateSection'])->name('document-sections.update');
    Route::post('document-sections/client', [DocumentSectionController::class, 'storeClientSections'])->name('document-sections.store-client');
    Route::post('document-sections/store', [DocumentSectionController::class, 'storeSection'])->name('document-sections.store');
    Route::delete('document-sections/{id}', [DocumentSectionController::class, 'deleteSection'])->name('document-sections.delete');

    // Document subsections
    Route::post('document-subsections/store', [DocumentSubsectionController::class, 'storeSubsection'])->name('document-subsections.store');
    Route::delete('document-subsections/{id}', [DocumentSubsectionController::class, 'deleteSubsection'])->name('document-subsections.delete');
    Route::put('document-subsections/{id}', [DocumentSubsectionController::class, 'updateSubsection'])->name('document-subsections.update');

    // Questions
    Route::post('questions', [QuestionController::class, 'store'])->name('questions.store');
    Route::get('subsections/{id}/questions', [QuestionController::class, 'getQuestionsBySubsection'])->name('questions.by-subsection');
    Route::get('sections/{id}/questions', [QuestionController::class, 'getQuestionsBySection'])->name('questions.by-section');
    Route::get('documents/{id}/questions', [QuestionController::class, 'getQuestionsByDocument'])->name('questions.by-document');
    Route::delete('questions/{id}', [QuestionController::class, 'destroy'])->name('questions.destroy');

    // Parameters
    Route::post('parameter', [ParameterController::class, 'store'])->name('parameters.store');
    Route::get('parameter/{parameter}', [ParameterController::class, 'show'])->name('parameters.show');
    Route::put('parameter/{parameter}', [ParameterController::class, 'update'])->name('parameters.update');
    Route::delete('parameter/{parameter}', [ParameterController::class, 'destroy'])->name('parameters.destroy');
    Route::get('questions/{questionId}/parameters', [ParameterController::class, 'getParametersByQuestion'])->name('parameters.by-question');

    Route::get('document-llm', function () {
        return Inertia::render('demo-llm/index');
    })->name('document-llm');

    Route::get('document-chat', function () {
        return Inertia::render('chat/index');
    })->name('document-chat');
});
