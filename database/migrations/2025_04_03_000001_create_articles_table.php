<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('articles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('article_family_id')->nullable()->constrained('article_families')->onDelete('set null')->onUpdate('cascade');
            $table->string('article_code', 255)->unique();
            $table->string('article_name', 255);
            $table->enum('article_type', ['finished_product', 'primary_material'])->comment('Type of article');
            $table->integer('available_stock')->default(0)->comment('stock_disponible');
            $table->decimal('batch_size', 10, 2)->nullable()->comment('taille_lot');

            // Columns from finished_products (nullable)
            $table->string('bulk_code', 50)->nullable()->comment('code_vrac (finished_product)');
            $table->boolean('is_complex')->nullable()->comment('Complex articles: 4-6 months coverage (finished_product)');
            $table->boolean('is_sample')->nullable()->comment('echantillon (finished_product)');
            $table->string('product_type', 50)->nullable()->comment('OFFICINAL or HOSPITALIER (finished_product)');
            $table->integer('target_coverage_months')->nullable()->comment('coverture_cible_mois (finished_product)');
            $table->decimal('monthly_budget', 10, 2)->nullable()->comment('budget_mensuel (finished_product)');

            // Columns from primary_materials (nullable)
            $table->enum('material_type', ['principe_actif', 'excipient'])->nullable()->comment('Type of raw material (primary_material)');
            $table->string('unit_of_measure', 20)->nullable()->comment('Unit of measure (primary_material)');

            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index('article_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('articles');
    }
};
