<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_compositions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('finished_product_article_id')->comment('References a finished product article')->constrained('articles')->onDelete('cascade');
            $table->foreignId('primary_material_article_id')->comment('References a primary material article')->constrained('articles')->onDelete('cascade');
            $table->decimal('quantity_per_unit', 10, 4)->comment('Quantity per product unit');
            $table->decimal('quantity_per_lot', 10, 4)->comment('Total quantity per production lot');
            $table->string('unit_of_measure', 20)->default('g');
            $table->timestamps();
            
            // Ensure unique combinations of finished product and primary material
            $table->unique(['finished_product_article_id', 'primary_material_article_id'], 'product_composition_fp_article_pm_article_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_compositions');
    }
}; 