<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('article_families', function (Blueprint $table) {
            $table->id();
            $table->string('family_code', 255)->unique()->comment('c_famille_article');
            $table->string('family_name', 255)->comment('libelle_famille_article');
            $table->timestamps();
        });

        // Add a check constraint to ensure family_code is either 'MP', 'PF', 'AC' or 'SF'
        DB::statement('ALTER TABLE article_families ADD CONSTRAINT check_family_code_valid 
            CHECK (family_code IN (\'MP\', \'PF\', \'AC\', \'SF\'))');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('article_families');
    }
};
