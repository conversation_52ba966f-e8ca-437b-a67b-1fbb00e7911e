<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * This migration adds all custom fields to Spatie permission tables.
     * Consolidates all customizations in one place for cleaner migration history.
     */
    public function up(): void
    {
        // Add custom fields to permissions table
        Schema::table('permissions', function (Blueprint $table) {
            $table->string('category')->nullable()->after('guard_name')
                ->comment('Category for grouping permissions (e.g., User Management, Product Management)');
            $table->text('description')->nullable()->after('category')
                ->comment('Human-readable description of what this permission allows');
        });

        // Add custom fields to roles table
        Schema::table('roles', function (Blueprint $table) {
            $table->text('description')->nullable()->after('guard_name')
                ->comment('Human-readable description of the role and its purpose');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove custom fields from roles table
        Schema::table('roles', function (Blueprint $table) {
            $table->dropColumn('description');
        });

        // Remove custom fields from permissions table
        Schema::table('permissions', function (Blueprint $table) {
            $table->dropColumn(['category', 'description']);
        });
    }
};
