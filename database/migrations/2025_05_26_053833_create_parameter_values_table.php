<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('parameter_values', function (Blueprint $table) {
            $table->id();
            $table->foreignId('parameter_id')->constrained('parameters')->onDelete('cascade');
            $table->integer('row_index'); // Row position in the table (0-based)
            $table->integer('column_index'); // Column position in the table (0-based)
            $table->text('value')->nullable(); // The actual cell value
            $table->timestamps();

            // Add indexes for better performance
            $table->index(['parameter_id', 'row_index', 'column_index']);
            $table->index('parameter_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('parameter_values');
    }
};
