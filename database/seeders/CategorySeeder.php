<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            ['name' => 'Electronics', 'description' => 'Electronic devices and gadgets', 'is_active' => true],
            ['name' => 'Clothing', 'description' => 'Apparel and fashion items', 'is_active' => true],
            ['name' => 'Books', 'description' => 'Books and educational materials', 'is_active' => true],
            ['name' => 'Home & Garden', 'description' => 'Home improvement and gardening supplies', 'is_active' => true],
            ['name' => 'Sports & Outdoors', 'description' => 'Sports equipment and outdoor gear', 'is_active' => true],
            ['name' => 'Automotive', 'description' => 'Car parts and automotive accessories', 'is_active' => true],
            ['name' => 'Health & Beauty', 'description' => 'Health and beauty products', 'is_active' => true],
            ['name' => 'Toys & Games', 'description' => 'Toys and gaming products', 'is_active' => true],
            ['name' => 'Food & Beverages', 'description' => 'Food items and beverages', 'is_active' => true],
            ['name' => 'Jewelry', 'description' => 'Jewelry and accessories', 'is_active' => true],
            ['name' => 'Computers', 'description' => 'Computer hardware and software', 'is_active' => true],
            ['name' => 'Mobile Phones', 'description' => 'Smartphones and mobile accessories', 'is_active' => true],
            ['name' => 'Furniture', 'description' => 'Home and office furniture', 'is_active' => true],
            ['name' => 'Kitchen', 'description' => 'Kitchen appliances and utensils', 'is_active' => true],
            ['name' => 'Pet Supplies', 'description' => 'Pet food and accessories', 'is_active' => true],
            ['name' => 'Office Supplies', 'description' => 'Office equipment and stationery', 'is_active' => true],
            ['name' => 'Musical Instruments', 'description' => 'Musical instruments and equipment', 'is_active' => true],
            ['name' => 'Art & Crafts', 'description' => 'Art supplies and craft materials', 'is_active' => true],
            ['name' => 'Baby Products', 'description' => 'Baby care and nursery items', 'is_active' => true],
            ['name' => 'Travel', 'description' => 'Travel accessories and luggage', 'is_active' => true],
            ['name' => 'Photography', 'description' => 'Cameras and photography equipment', 'is_active' => true],
            ['name' => 'Fitness', 'description' => 'Fitness equipment and supplements', 'is_active' => true],
            ['name' => 'Gaming', 'description' => 'Video games and gaming consoles', 'is_active' => true],
            ['name' => 'Movies & TV', 'description' => 'Movies, TV shows, and entertainment', 'is_active' => true],
            ['name' => 'Tools', 'description' => 'Hand tools and power tools', 'is_active' => true],
            ['name' => 'Shoes', 'description' => 'Footwear for all occasions', 'is_active' => true],
            ['name' => 'Watches', 'description' => 'Wristwatches and timepieces', 'is_active' => true],
            ['name' => 'Bags', 'description' => 'Handbags, backpacks, and luggage', 'is_active' => true],
            ['name' => 'Lighting', 'description' => 'Indoor and outdoor lighting solutions', 'is_active' => true],
            ['name' => 'Cleaning', 'description' => 'Cleaning supplies and equipment', 'is_active' => true],
            ['name' => 'Gardening', 'description' => 'Gardening tools and plants', 'is_active' => true],
            ['name' => 'Stationery', 'description' => 'Writing materials and paper products', 'is_active' => true],
            ['name' => 'Cosmetics', 'description' => 'Makeup and beauty products', 'is_active' => true],
            ['name' => 'Skincare', 'description' => 'Skincare products and treatments', 'is_active' => true],
            ['name' => 'Supplements', 'description' => 'Health supplements and vitamins', 'is_active' => true],
            ['name' => 'Outdoor Gear', 'description' => 'Camping and hiking equipment', 'is_active' => true],
            ['name' => 'Board Games', 'description' => 'Board games and puzzles', 'is_active' => true],
            ['name' => 'Collectibles', 'description' => 'Collectible items and memorabilia', 'is_active' => true],
            ['name' => 'Vintage', 'description' => 'Vintage and antique items', 'is_active' => false],
            ['name' => 'Handmade', 'description' => 'Handcrafted and artisan products', 'is_active' => true],
            ['name' => 'Digital Products', 'description' => 'Software and digital downloads', 'is_active' => true],
            ['name' => 'Gift Cards', 'description' => 'Gift cards and vouchers', 'is_active' => true],
            ['name' => 'Industrial', 'description' => 'Industrial equipment and supplies', 'is_active' => true],
            ['name' => 'Medical', 'description' => 'Medical equipment and supplies', 'is_active' => true],
            ['name' => 'Educational', 'description' => 'Educational materials and courses', 'is_active' => true],
            ['name' => 'Religious', 'description' => 'Religious books and items', 'is_active' => true],
            ['name' => 'Party Supplies', 'description' => 'Party decorations and supplies', 'is_active' => true],
            ['name' => 'Seasonal', 'description' => 'Seasonal and holiday items', 'is_active' => false],
            ['name' => 'Luxury', 'description' => 'High-end luxury products', 'is_active' => true],
            ['name' => 'Eco-Friendly', 'description' => 'Environmentally friendly products', 'is_active' => true],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
