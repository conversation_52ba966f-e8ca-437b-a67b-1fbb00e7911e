<?php

namespace Database\Seeders;

use App\Models\Article;
use App\Models\ArticleFamily;
use Illuminate\Database\Seeder;

class ArticleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the IDs of the MP and PF families
        $mpFamily = ArticleFamily::where('family_code', 'MP')->first();
        $pfFamily = ArticleFamily::where('family_code', 'PF')->first();

        // Create 5 primary material articles (MP)
        for ($i = 1; $i <= 5; $i++) {
            Article::create([
                'article_family_id' => $mpFamily->id,
                'article_code' => 'MP-' . str_pad($i, 3, '0', STR_PAD_LEFT),
                'article_name' => 'Matière Première ' . $i,
                'article_type' => 'primary_material',
                'available_stock' => rand(100, 1000),
                'batch_size' => null,
                'is_active' => true,
                'material_type' => ($i % 2 == 0) ? 'excipient' : 'principe_actif',
                'unit_of_measure' => 'kg',
                'bulk_code' => null,
                'is_complex' => null,
                'is_sample' => null,
                'product_type' => null,
                'target_coverage_months' => null,
                'monthly_budget' => null,
            ]);
        }

        // Create 5 finished product articles (PF)
        for ($i = 1; $i <= 5; $i++) {
            Article::create([
                'article_family_id' => $pfFamily->id,
                'article_code' => 'PF-' . str_pad($i, 3, '0', STR_PAD_LEFT),
                'article_name' => 'Produit Fini ' . $i,
                'article_type' => 'finished_product',
                'available_stock' => rand(10, 100),
                'batch_size' => rand(1000, 5000) / 100,
                'is_active' => true,
                'bulk_code' => 'VRAC-' . str_pad($i, 3, '0', STR_PAD_LEFT),
                'is_complex' => ($i % 3 == 0),
                'is_sample' => ($i == 5),
                'product_type' => ($i % 2 == 0) ? 'HOSPITALIER' : 'OFFICINAL',
                'target_coverage_months' => rand(3, 6),
                'monthly_budget' => rand(5000, 20000) / 100,
                'material_type' => null,
                'unit_of_measure' => null,
            ]);
        }
    }
}
