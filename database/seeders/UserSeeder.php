<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // Superuser (you)
        User::create([
            'name' => 'Super User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'user_type' => 'superuser',
        ]);

        // Admin (your client)
        User::create([
            'name' => 'John Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'user_type' => 'admin',
        ]);

        // Clients (admin's clients)
        User::create([
            'name' => 'Jane Editor',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'user_type' => 'client',
        ]);

        User::create([
            'name' => 'Bob Viewer',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'user_type' => 'client',
        ]);

        User::create([
            'name' => 'Alice Customer',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'user_type' => 'client',
        ]);

        // Product Catalog Manager (pharmaceutical specialist)
        User::create([
            'name' => 'Catalog Manager',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'user_type' => 'client',
        ]);
    }
}
