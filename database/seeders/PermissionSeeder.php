<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions for existing models
        $permissions = [
            // User permissions
            [
                'name' => 'view-user',
                'category' => 'User Management',
                'description' => 'Can view user listings'
            ],
            [
                'name' => 'create-user',
                'category' => 'User Management',
                'description' => 'Can create new users'
            ],
            [
                'name' => 'edit-user',
                'category' => 'User Management',
                'description' => 'Can modify existing users'
            ],
            [
                'name' => 'delete-user',
                'category' => 'User Management',
                'description' => 'Can remove users'
            ],

            // Role permissions
            [
                'name' => 'view-role',
                'category' => 'Role Management',
                'description' => 'Can view role listings'
            ],
            [
                'name' => 'create-role',
                'category' => 'Role Management',
                'description' => 'Can create new roles'
            ],
            [
                'name' => 'edit-role',
                'category' => 'Role Management',
                'description' => 'Can modify existing roles'
            ],
            [
                'name' => 'delete-role',
                'category' => 'Role Management',
                'description' => 'Can remove roles'
            ],

            // Permission permissions
            [
                'name' => 'view-permission',
                'category' => 'Permission Management',
                'description' => 'Can view permission listings'
            ],
            [
                'name' => 'assign-permission',
                'category' => 'Permission Management',
                'description' => 'Can assign permissions to roles'
            ],

            // Article Family permissions
            [
                'name' => 'view-article-family',
                'category' => 'Article Family Management',
                'description' => 'Can view article family listings and details'
            ],
            [
                'name' => 'create-article-family',
                'category' => 'Article Family Management',
                'description' => 'Can create new article families'
            ],
            [
                'name' => 'edit-article-family',
                'category' => 'Article Family Management',
                'description' => 'Can modify existing article families'
            ],
            [
                'name' => 'delete-article-family',
                'category' => 'Article Family Management',
                'description' => 'Can remove article families'
            ],
            [
                'name' => 'export-article-family',
                'category' => 'Article Family Management',
                'description' => 'Can export article family data to CSV/Excel'
            ],
            [
                'name' => 'filter-article-family',
                'category' => 'Article Family Management',
                'description' => 'Can apply filters to article family data'
            ],

            // Article permissions
            [
                'name' => 'view-article',
                'category' => 'Article Management',
                'description' => 'Can view article listings and details'
            ],
            [
                'name' => 'create-article',
                'category' => 'Article Management',
                'description' => 'Can create new articles'
            ],
            [
                'name' => 'edit-article',
                'category' => 'Article Management',
                'description' => 'Can modify existing articles'
            ],
            [
                'name' => 'delete-article',
                'category' => 'Article Management',
                'description' => 'Can remove articles'
            ],
            [
                'name' => 'import-article',
                'category' => 'Article Management',
                'description' => 'Can import articles from external files'
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::create($permission);
        }

        // Create roles with descriptions
        $superAdminRole = Role::create([
            'name' => 'super-user',
            'description' => 'Full system access with all permissions'
        ]);
        $adminRole = Role::create([
            'name' => 'admin',
            'description' => 'Can manage users and create custom roles'
        ]);
        $editorRole = Role::create([
            'name' => 'editor',
            'description' => 'Can manage content and data'
        ]);
        $viewerRole = Role::create([
            'name' => 'viewer',
            'description' => 'Read-only access to system data'
        ]);
        $customerRole = Role::create([
            'name' => 'customer',
            'description' => 'Limited access to system features'
        ]);
        $productCatalogManagerRole = Role::create([
            'name' => 'product-catalog-manager',
            'description' => 'Pharmaceutical product catalog manager with full access to article families'
        ]);

        // Assign permissions to roles

        // Super Admin gets all permissions
        $superAdminRole->givePermissionTo(Permission::all());

        // Admin gets most permissions (can manage users and roles)
        $adminRole->givePermissionTo([
            'view-user',
            'create-user',
            'edit-user',
            'delete-user',
            'view-role',
            'create-role',
            'edit-role',
            'delete-role',
            'view-permission',
            'assign-permission',
            'view-article-family',
            'create-article-family',
            'edit-article-family',
            'delete-article-family',
            'export-article-family',
            'filter-article-family',
            'view-article',
            'create-article',
            'edit-article',
            'delete-article',
            'import-article',
        ]);

        // Editor can manage content
        $editorRole->givePermissionTo([
            'view-article-family',
            'create-article-family',
            'edit-article-family',
            'export-article-family',
            'filter-article-family',
            'view-article',
            'create-article',
            'edit-article',
            'import-article',
            'view-user',
        ]);

        // Viewer can only view
        $viewerRole->givePermissionTo([
            'view-article-family',
            'filter-article-family',
            'view-article',
            'view-user',
        ]);

        // Customer has limited access
        $customerRole->givePermissionTo([
            'view-article-family',
            'view-article',
        ]);

        // Product Catalog Manager has full access to article families and articles
        $productCatalogManagerRole->givePermissionTo([
            'view-article-family',
            'create-article-family',
            'edit-article-family',
            'delete-article-family',
            'export-article-family',
            'filter-article-family',
            'view-article',
            'create-article',
            'edit-article',
            'delete-article',
            'import-article',
        ]);

        // Assign roles to users
        $superuser = User::where('email', '<EMAIL>')->first();
        $admin = User::where('email', '<EMAIL>')->first();
        $editor = User::where('email', '<EMAIL>')->first();
        $viewer = User::where('email', '<EMAIL>')->first();
        $customer = User::where('email', '<EMAIL>')->first();
        $productCatalogManager = User::where('email', '<EMAIL>')->first();

        if ($superuser) $superuser->assignRole('super-user');
        if ($admin) $admin->assignRole('admin');
        if ($editor) $editor->assignRole('editor');
        if ($viewer) $viewer->assignRole('viewer');
        if ($customer) $customer->assignRole('customer');
        if ($productCatalogManager) $productCatalogManager->assignRole('product-catalog-manager');

        // Example: Give direct permission to a user (not through role)
        if ($editor) {
            $editor->givePermissionTo('view-user'); // Direct permission
        }
    }
}
