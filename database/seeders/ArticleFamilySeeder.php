<?php

namespace Database\Seeders;

use App\Models\ArticleFamily;
use Illuminate\Database\Seeder;

class ArticleFamilySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if we already have the main article families, create them if they don't exist
        if (!ArticleFamily::where('family_code', 'MP')->exists()) {
            ArticleFamily::create([
                'family_code' => 'MP',
                'family_name' => 'Matière Première',
            ]);
        }

        if (!ArticleFamily::where('family_code', 'PF')->exists()) {
            ArticleFamily::create([
                'family_code' => 'PF',
                'family_name' => 'Produit Fini',
            ]);
        }

        if (!ArticleFamily::where('family_code', 'AC')->exists()) {
            ArticleFamily::create([
                'family_code' => 'AC',
                'family_name' => 'Article de Conditionnement',
            ]);
        }

        if (!ArticleFamily::where('family_code', 'SF')->exists()) {
            ArticleFamily::create([
                'family_code' => 'SF',
                'family_name' => 'Semi-Fini',
            ]);
        }
    }
}
