import AppLayout from '@/layouts/app-layout';
import Heading from '@/components/heading';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Save, Shield } from 'lucide-react';
import PermissionsSelector from './components/PermissionsSelector';

interface Permission {
    id: number;
    name: string;
    display_name: string;
    description: string;
}

interface PermissionCategory {
    category: string;
    permissions: Permission[];
}

interface Props {
    permissions: PermissionCategory[];
}

const breadcrumbItems = [
    { title: 'Dashboard', href: route('dashboard') },
    { title: 'Roles', href: route('roles.index') },
    { title: 'Create Role', href: route('roles.create') },
];

export default function CreateRole({ permissions }: Props) {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        display_name: '',
        permissions: [] as number[],
    });

    const convertToKebabCase = (input: string): string => {
        return input
            .toLowerCase()
            .trim()
            .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
            .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
    };

    const handleDisplayNameChange = (value: string) => {
        setData('display_name', value);
        const kebabName = convertToKebabCase(value);
        setData('name', kebabName);
    };

    const handlePermissionChange = (permissionId: number, checked: boolean) => {
        setData('permissions', checked
            ? [...data.permissions, permissionId]
            : data.permissions.filter(id => id !== permissionId)
        );
    };

    const handleCategoryToggle = (categoryPermissions: Permission[], checked: boolean) => {
        const permissionIds = categoryPermissions.map(p => p.id);
        setData('permissions', checked
            ? [...new Set([...data.permissions, ...permissionIds])]
            : data.permissions.filter(id => !permissionIds.includes(id))
        );
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('roles.store'));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbItems}>
            <div className="space-y-6 p-6">
                <Head title="Create Role" />
                <div>
                    <div className="flex items-center justify-between">
                        <div>
                            <Heading title="Create New Role" description="Create a custom role with specific permissions" />
                        </div>
                        <Button variant="outline" asChild>
                            <Link href={route('roles.index')}>
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Roles
                            </Link>
                        </Button>
                    </div>
                </div>

                <div className="grid gap-6 lg:grid-cols-3">
                    {/* Role Information */}
                    <div className="lg:col-span-1">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Shield className="mr-2 h-5 w-5" />
                                    Role Information
                                </CardTitle>
                                <CardDescription>
                                    Basic information about the role
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <form onSubmit={handleSubmit}>
                                    <div className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="display_name">Role Name</Label>
                                            <Input
                                                id="display_name"
                                                placeholder="e.g., Software Manager"
                                                value={data.display_name}
                                                onChange={(e) => handleDisplayNameChange(e.target.value)}
                                                className={errors.display_name ? 'border-red-500' : ''}
                                            />
                                            {errors.display_name && (
                                                <p className="text-xs text-red-500">{errors.display_name}</p>
                                            )}
                                            <p className="text-xs text-muted-foreground">
                                                Enter the role name as you want it to appear in the interface.
                                            </p>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="name" className="text-xs text-red-500">Database Name (DEBUG)</Label>
                                            <Input
                                                id="name"
                                                value={data.name}
                                                disabled
                                                className="bg-muted text-muted-foreground"
                                            />
                                            {errors.name && (
                                                <p className="text-xs text-red-500">{errors.name}</p>
                                            )}
                                            <p className="text-xs text-muted-foreground">
                                                This is how the role will be saved in the database (auto-generated from above).
                                            </p>
                                        </div>

                                        <Separator />

                                        <div className="space-y-2">
                                            <Label>Selected Permissions</Label>
                                            <div className="text-sm text-muted-foreground">
                                                {data.permissions.length} permissions selected
                                            </div>
                                        </div>

                                        <Button type="submit" className="w-full" disabled={processing}>
                                            <Save className="mr-2 h-4 w-4" />
                                            {processing ? 'Creating...' : 'Create Role'}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Permissions Selection */}
                    <div className="lg:col-span-2">
                        <PermissionsSelector
                            permissions={permissions}
                            selectedPermissions={data.permissions}
                            onPermissionChange={handlePermissionChange}
                            onCategoryToggle={handleCategoryToggle}
                        />
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
