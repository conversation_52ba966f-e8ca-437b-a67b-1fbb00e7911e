import Heading from '@/components/heading';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem, SharedData } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Edit, Plus, Shield, Trash2 } from 'lucide-react';

interface User {
    id: number;
    name: string;
    email: string;
    user_type: string;
}

interface Role {
    id: number;
    name: string;
    display_name: string;
    description: string;
    permissions_count: number;
    users_count: number;
    created_at: string;
    can_edit: boolean;
    can_delete: boolean;
    users: User[];
}

interface Props {
    roles: Role[];
}

const breadcrumbItems: BreadcrumbItem[] = [
    { title: 'Dashboard', href: route('dashboard') },
    { title: 'Roles', href: route('roles.index') },
];

const getRoleType = (roleName: string): string => {
    if (roleName.includes('super')) {
        return 'superuser';
    }
    if (roleName === 'admin') {
        return 'admin';
    }
    // All other roles (editor, viewer, customer, etc.) are client roles
    return 'client';
};

const getRoleTypeColor = (roleType: string) => {
    switch (roleType) {
        case 'superuser':
            return 'destructive';
        case 'admin':
            return 'default';
        case 'client':
            return 'secondary';
        default:
            return 'outline';
    }
};

export default function RolesIndex({ roles }: Props) {
    const { auth } = usePage<SharedData>().props;

    const hasPermission = (permission: string): boolean => {
        return auth.permissions?.includes(permission) || false;
    };

    const handleDeleteRole = (role: Role) => {
        if (confirm(`Are you sure you want to delete the role "${role.display_name}"? This action cannot be undone.`)) {
            router.delete(route('roles.destroy', role.id), {
                onSuccess: () => {
                    // Success message will be handled by the backend
                },
                onError: () => {
                    alert('Failed to delete role. Please try again.');
                },
            });
        }
    };

    const getDeleteTooltipMessage = (role: Role): string => {
        if (!role.can_delete) {
            const defaultRoles = ['super-user', 'admin'];
            if (defaultRoles.includes(role.name)) {
                return 'Default roles cannot be deleted';
            }
            if (role.users_count > 0) {
                return `Cannot delete role with ${role.users_count} assigned user${role.users_count > 1 ? 's' : ''}`;
            }
        }
        return 'Delete this role';
    };

    const getEditTooltipMessage = (role: Role): string => {
        if (!role.can_edit) {
            return 'System roles cannot be edited';
        }
        return 'Edit this role';
    };

    return (
        <AppLayout breadcrumbs={breadcrumbItems}>
            <div className="space-y-6 p-6">
                <Head title="Roles Management" />
                <div>
                    <div className="flex items-center justify-between">
                        <div>
                            <Heading title="Roles Management" description="Manage user roles and their permissions" />
                        </div>
                        {hasPermission('create-role') && (
                            <Button asChild>
                                <Link href={route('roles.create')}>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Create Role
                                </Link>
                            </Button>
                        )}
                    </div>
                </div>

                {/* Summary Stats */}
                <div className="grid gap-4 md:grid-cols-4">
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Total Roles</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{roles.length}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Client Roles</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{roles.filter((role: Role) => getRoleType(role.name) === 'client').length}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{roles.reduce((sum: number, role: Role) => sum + role.users_count, 0)}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Avg Permissions</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {roles.length > 0
                                    ? Math.round(roles.reduce((sum: number, role: Role) => sum + role.permissions_count, 0) / roles.length)
                                    : 0}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Roles Table */}
                <Card>
                    <CardContent className="p-0">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Role</TableHead>
                                    <TableHead>Description</TableHead>
                                    <TableHead>Type</TableHead>
                                    <TableHead>Permissions</TableHead>
                                    <TableHead>Users</TableHead>
                                    <TableHead>Created</TableHead>
                                    <TableHead className="w-[80px]">Edit</TableHead>
                                    <TableHead className="w-[80px]">Delete</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {roles.map((role, index) => (
                                    <TableRow key={role.id} className={index % 2 === 0 ? 'bg-muted/50' : ''}>
                                        <TableCell>
                                            <div className="flex items-center space-x-2">
                                                <Shield className="text-primary h-4 w-4" />
                                                <div>
                                                    <div className="font-medium">{role.display_name}</div>
                                                </div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="text-muted-foreground max-w-xs truncate text-sm">{role.description}</div>
                                        </TableCell>
                                        <TableCell>
                                            <Badge variant={getRoleTypeColor(getRoleType(role.name))}>
                                                {getRoleType(role.name)}
                                            </Badge>
                                        </TableCell>
                                        <TableCell>
                                            <Badge variant="secondary">{role.permissions_count}</Badge>
                                        </TableCell>
                                        <TableCell>
                                            <Badge variant="outline">{role.users_count}</Badge>
                                        </TableCell>
                                        <TableCell>
                                            <div className="text-muted-foreground text-sm">{new Date(role.created_at).toLocaleDateString()}</div>
                                        </TableCell>
                                        <TableCell>
                                            <TooltipProvider delayDuration={0}>
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <div className="inline-block">
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                className={role.can_edit
                                                                    ? ""
                                                                    : "text-muted-foreground cursor-not-allowed"
                                                                }
                                                                disabled={!role.can_edit}
                                                                asChild={role.can_edit}
                                                            >
                                                                {role.can_edit ? (
                                                                    <Link href={`/roles/${role.id}/edit`}>
                                                                        <Edit className="h-4 w-4" />
                                                                    </Link>
                                                                ) : (
                                                                    <Edit className="h-4 w-4" />
                                                                )}
                                                            </Button>
                                                        </div>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>{getEditTooltipMessage(role)}</p>
                                                    </TooltipContent>
                                                </Tooltip>
                                            </TooltipProvider>
                                        </TableCell>
                                        <TableCell>
                                            <TooltipProvider delayDuration={0}>
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <div className="inline-block">
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                className={role.can_delete
                                                                    ? "text-destructive hover:text-destructive"
                                                                    : "text-muted-foreground cursor-not-allowed"
                                                                }
                                                                disabled={!role.can_delete}
                                                                onClick={() => role.can_delete && handleDeleteRole(role)}
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>{getDeleteTooltipMessage(role)}</p>
                                                    </TooltipContent>
                                                </Tooltip>
                                            </TooltipProvider>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
