import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Check, Search, X } from 'lucide-react';
import { useState } from 'react';

interface Permission {
    id: number;
    name: string;
    display_name: string;
    description: string;
}

interface PermissionCategory {
    category: string;
    permissions: Permission[];
}

interface PermissionsSelectorProps {
    permissions: PermissionCategory[];
    selectedPermissions: number[];
    onPermissionChange: (permissionId: number, checked: boolean) => void;
    onCategoryToggle: (categoryPermissions: Permission[], checked: boolean) => void;
    showChangeBadges?: boolean;
    originalPermissions?: number[];
    title?: string;
    description?: string;
}

export default function PermissionsSelector({
    permissions,
    selectedPermissions,
    onPermissionChange,
    onCategoryToggle,
    showChangeBadges = false,
    originalPermissions = [],
    title = "Permissions",
    description = "Select the permissions this role should have"
}: PermissionsSelectorProps) {
    const [searchTerm, setSearchTerm] = useState('');

    const isCategorySelected = (categoryPermissions: Permission[]) => {
        const permissionIds = categoryPermissions.map(p => p.id);
        return permissionIds.every(id => selectedPermissions.includes(id));
    };

    const isCategoryPartiallySelected = (categoryPermissions: Permission[]) => {
        const permissionIds = categoryPermissions.map(p => p.id);
        const selectedCount = permissionIds.filter(id => selectedPermissions.includes(id)).length;
        return selectedCount > 0 && selectedCount < permissionIds.length;
    };

    // Filter permissions based on search term
    const filterPermissions = (categories: PermissionCategory[]) => {
        if (!searchTerm.trim()) {
            return categories;
        }

        const lowerSearchTerm = searchTerm.toLowerCase();
        return categories.map(category => ({
            ...category,
            permissions: category.permissions.filter(permission =>
                permission.display_name.toLowerCase().includes(lowerSearchTerm) ||
                permission.description.toLowerCase().includes(lowerSearchTerm) ||
                permission.name.toLowerCase().includes(lowerSearchTerm)
            )
        })).filter(category => category.permissions.length > 0);
    };

    // Group permissions into logical tabs (without filtering when searching globally)
    const groupPermissionsByTab = () => {
        if (searchTerm.trim()) {
            // When searching, show all matching permissions in a single view
            return {
                systemManagement: [],
                contentManagement: [],
                searchResults: filterPermissions(permissions)
            };
        }

        // Normal tab view when not searching
        const systemManagement = permissions.filter(cat =>
            ['User Management', 'Role Management', 'Permission Management'].includes(cat.category)
        );

        const contentManagement = permissions.filter(cat =>
            ['Article Family Management', 'Article Management'].includes(cat.category)
        );

        return {
            systemManagement,
            contentManagement,
            searchResults: []
        };
    };

    const { systemManagement, contentManagement, searchResults } = groupPermissionsByTab();

    // Helper component to render permission categories in grid layout
    const renderPermissionCategories = (categories: PermissionCategory[]) => (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((permissionCategory) => (
                <div key={permissionCategory.category} className="space-y-3 border rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                        <Checkbox
                            id={`category-${permissionCategory.category}`}
                            checked={isCategorySelected(permissionCategory.permissions)}
                            onCheckedChange={(checked) =>
                                onCategoryToggle(permissionCategory.permissions, checked as boolean)
                            }
                            className={isCategoryPartiallySelected(permissionCategory.permissions) ? 'data-[state=checked]:bg-orange-500' : ''}
                        />
                        <Label
                            htmlFor={`category-${permissionCategory.category}`}
                            className="text-sm font-medium"
                        >
                            {permissionCategory.category}
                        </Label>
                        {showChangeBadges && (
                            <Badge variant="outline" className="text-xs">
                                {permissionCategory.permissions.filter((p) => selectedPermissions.includes(p.id)).length}/{permissionCategory.permissions.length}
                            </Badge>
                        )}
                    </div>

                    <div className="ml-6 space-y-2">
                        {permissionCategory.permissions.map((permission) => {
                            const isSelected = selectedPermissions.includes(permission.id);
                            const wasOriginallySelected = originalPermissions.includes(permission.id);
                            const hasChanged = showChangeBadges && isSelected !== wasOriginallySelected;

                            return (
                                <div key={permission.id} className="flex items-start space-x-2">
                                    <Checkbox
                                        id={`permission-${permission.id}`}
                                        checked={isSelected}
                                        onCheckedChange={(checked) =>
                                            onPermissionChange(permission.id, checked as boolean)
                                        }
                                    />
                                    <div className={showChangeBadges ? "flex-1 space-y-1" : "space-y-1"}>
                                        <div className="flex items-center space-x-2">
                                            <Label
                                                htmlFor={`permission-${permission.id}`}
                                                className="text-sm"
                                            >
                                                {permission.display_name}
                                            </Label>
                                            {hasChanged && (
                                                <Badge variant={isSelected ? 'default' : 'destructive'} className="text-xs">
                                                    {isSelected ? (
                                                        <>
                                                            <Check className="mr-1 h-3 w-3" />
                                                            Added
                                                        </>
                                                    ) : (
                                                        <>
                                                            <X className="mr-1 h-3 w-3" />
                                                            Removed
                                                        </>
                                                    )}
                                                </Badge>
                                            )}
                                        </div>
                                        <p className="text-xs text-muted-foreground">
                                            {permission.description}
                                        </p>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>
            ))}
        </div>
    );

    return (
        <Card>
            <CardHeader>
                <CardTitle>{title}</CardTitle>
                <CardDescription>{description}</CardDescription>
            </CardHeader>
            <CardContent>
                {/* Search Input */}
                <div className="mb-6">
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                        <Input
                            placeholder="Search permissions by name or description..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10"
                        />
                    </div>
                </div>

{searchTerm.trim() ? (
                    // Global search results view
                    <div>
                        <div className="mb-4">
                            <h3 className="text-lg font-medium">
                                Search Results {searchResults.length > 0 && `(${searchResults.reduce((acc, cat) => acc + cat.permissions.length, 0)} permissions found)`}
                            </h3>
                        </div>
                        {searchResults.length > 0 ? (
                            renderPermissionCategories(searchResults)
                        ) : (
                            <div className="text-center py-8 text-muted-foreground">
                                No permissions found matching "{searchTerm}".
                            </div>
                        )}
                    </div>
                ) : (
                    // Normal tab view when not searching
                    <Tabs defaultValue="system" className="w-full">
                        <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="system">System</TabsTrigger>
                            <TabsTrigger value="content">Content</TabsTrigger>
                        </TabsList>

                        <TabsContent value="system" className="mt-6">
                            {systemManagement.length > 0 ? (
                                renderPermissionCategories(systemManagement)
                            ) : (
                                <div className="text-center py-8 text-muted-foreground">
                                    No system permissions available.
                                </div>
                            )}
                        </TabsContent>

                        <TabsContent value="content" className="mt-6">
                            {contentManagement.length > 0 ? (
                                renderPermissionCategories(contentManagement)
                            ) : (
                                <div className="text-center py-8 text-muted-foreground">
                                    No content permissions available.
                                </div>
                            )}
                        </TabsContent>
                    </Tabs>
                )}
            </CardContent>
        </Card>
    );
}
