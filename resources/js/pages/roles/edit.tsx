import Heading from '@/components/heading';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Calendar, Save, Shield, Users } from 'lucide-react';
import { FormEvent, useState } from 'react';
import PermissionsSelector from './components/PermissionsSelector';

// TypeScript interfaces for backend data
interface User {
    id: number;
    name: string;
    email: string;
    user_type: string;
}

interface Permission {
    id: number;
    name: string;
    display_name: string;
    description: string;
    assigned?: boolean;
}

interface PermissionCategory {
    category: string;
    permissions: Permission[];
}

interface Role {
    id: number;
    name: string;
    display_name: string;
    description: string;
    permissions_count: number;
    users_count: number;
    created_at: string;
    updated_at: string;
    can_edit: boolean;
    can_delete: boolean;
    users: User[];
    current_permissions: number[];
}

interface Props {
    role: Role;
    permissions: PermissionCategory[];
}

const getBreadcrumbItems = (role: Role) => [
    { title: 'Dashboard', href: route('dashboard') },
    { title: 'Roles', href: route('roles.index') },
    { title: role.name, href: route('roles.edit', role.id) },
];

export default function EditRole({ role, permissions }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        name: role.name,
        display_name: role.display_name,
    });

    const convertToKebabCase = (input: string): string => {
        return input
            .toLowerCase()
            .trim()
            .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
            .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
    };

    const handleDisplayNameChange = (value: string) => {
        setData('display_name', value);
        const kebabName = convertToKebabCase(value);
        setData('name', kebabName);
    };

    const { data: permissionData, setData: setPermissionData, put: putPermissions, processing: permissionProcessing } = useForm({
        permissions: role.current_permissions,
    });

    const [hasPermissionChanges, setHasPermissionChanges] = useState(false);

    const handleSave = (e: FormEvent) => {
        e.preventDefault();
        put(route('roles.update', role.id));
    };

    const handlePermissionChange = (permissionId: number, checked: boolean) => {
        const newPermissions = checked
            ? [...permissionData.permissions, permissionId]
            : permissionData.permissions.filter((id: number) => id !== permissionId);

        setPermissionData('permissions', newPermissions);
        setHasPermissionChanges(JSON.stringify(newPermissions.sort()) !== JSON.stringify(role.current_permissions.sort()));
    };

    const handleCategoryToggle = (categoryPermissions: Permission[], checked: boolean) => {
        const permissionIds = categoryPermissions.map((p) => p.id);
        const newPermissions = checked
            ? [...new Set([...permissionData.permissions, ...permissionIds])]
            : permissionData.permissions.filter((id: number) => !permissionIds.includes(id));

        setPermissionData('permissions', newPermissions);
        setHasPermissionChanges(JSON.stringify(newPermissions.sort()) !== JSON.stringify(role.current_permissions.sort()));
    };

    const resetPermissionChanges = () => {
        setPermissionData('permissions', role.current_permissions);
        setHasPermissionChanges(false);
    };

    const handlePermissionSubmit = (e: FormEvent) => {
        e.preventDefault();
        putPermissions(route('roles.permissions.update', role.id), {
            onSuccess: () => {
                setHasPermissionChanges(false);
            }
        });
    };

    return (
        <AppLayout breadcrumbs={getBreadcrumbItems(role)}>
            <div className="space-y-6 p-6">
                <Head title={`Edit Role: ${role.name}`} />
                <div>
                    <div className="flex items-center justify-between">
                        <div>
                            <Heading title={`Edit Role: ${role.name}`} description="Modify role information and manage permissions" />
                        </div>
                        <div className="flex space-x-2">
                            <Button variant="outline" asChild>
                                <Link href={route('roles.index')}>
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    Back to Roles
                                </Link>
                            </Button>
                        </div>
                    </div>
                </div>

                <div className="grid gap-6 lg:grid-cols-3">
                    {/* Role Information */}
                    <div className="lg:col-span-1">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Shield className="mr-2 h-5 w-5" />
                                    Role Information
                                </CardTitle>
                                <CardDescription>
                                    Basic information about the role
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <form onSubmit={handleSave}>
                                    <div className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="display_name">Role Name</Label>
                                            <Input
                                                id="display_name"
                                                placeholder="e.g., Software Manager"
                                                value={data.display_name}
                                                onChange={(e) => handleDisplayNameChange(e.target.value)}
                                                disabled={!role.can_edit}
                                                className={errors.display_name ? 'border-red-500' : ''}
                                            />
                                            {errors.display_name && (
                                                <p className="text-xs text-red-500">{errors.display_name}</p>
                                            )}
                                            <p className="text-xs text-muted-foreground">
                                                Enter the role name as you want it to appear in the interface.
                                            </p>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="name" className="text-xs text-red-500">Database Name (DEBUG)</Label>
                                            <Input
                                                id="name"
                                                value={data.name}
                                                disabled
                                                className="bg-muted text-muted-foreground"
                                            />
                                            {errors.name && (
                                                <p className="text-xs text-red-500">{errors.name}</p>
                                            )}
                                            <p className="text-xs text-muted-foreground">
                                                This is how the role will be saved in the database (auto-generated from above).
                                            </p>
                                        </div>

                                        <Separator />

                                        <div className="space-y-2">
                                            <Label>Selected Permissions</Label>
                                            <div className="text-sm text-muted-foreground">
                                                {permissionData.permissions.length} permissions selected
                                            </div>
                                        </div>

                                        <Button type="submit" className="w-full" disabled={processing || !role.can_edit}>
                                            <Save className="mr-2 h-4 w-4" />
                                            {processing ? 'Saving...' : 'Save Changes'}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>

                        {/* Role Profile Section */}
                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle className="text-lg">Role Profile</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="flex items-center space-x-4">
                                    <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                                        <Shield className="h-8 w-8 text-primary" />
                                    </div>
                                    <div className="flex-1">
                                        <h3 className="text-lg font-semibold">{role.name}</h3>
                                        <p className="text-muted-foreground text-sm">{role.display_name}</p>
                                        <p className="text-muted-foreground text-xs">{role.description}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Timestamps Section */}
                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle className="text-lg">Timestamps</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    <div className="space-y-1">
                                        <p className="text-muted-foreground text-sm font-medium">Created</p>
                                        <p className="flex items-center text-sm">
                                            <Calendar className="text-muted-foreground mr-2 h-4 w-4" />
                                            {new Date(role.created_at).toLocaleDateString()}
                                        </p>
                                    </div>
                                    <div className="space-y-1">
                                        <p className="text-muted-foreground text-sm font-medium">Last Updated</p>
                                        <p className="flex items-center text-sm">
                                            <Calendar className="text-muted-foreground mr-2 h-4 w-4" />
                                            {new Date(role.updated_at).toLocaleDateString()}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Statistics Section */}
                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle className="text-lg">Statistics</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-2 gap-3">
                                    <div className="bg-muted/30 rounded-lg p-3 text-center">
                                        <div className="text-primary text-xl font-bold">{role.permissions_count}</div>
                                        <p className="text-muted-foreground text-xs">Permissions</p>
                                    </div>
                                    <div className="bg-muted/30 rounded-lg p-3 text-center">
                                        <div className="text-primary text-xl font-bold">{role.users_count}</div>
                                        <p className="text-muted-foreground text-xs">Users</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Users Section */}
                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle className="flex items-center text-lg">
                                    <Users className="mr-2 h-5 w-5" />
                                    Assigned Users
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="max-h-[200px] space-y-2 overflow-y-auto">
                                    {role.users.map((user: User) => (
                                        <div key={user.id} className="bg-background flex items-center justify-between rounded-lg border p-2">
                                            <div>
                                                <span className="font-medium text-sm">{user.name}</span>
                                                <p className="text-muted-foreground text-xs">{user.email}</p>
                                            </div>
                                            <Badge variant="outline" className="text-xs">{user.user_type}</Badge>
                                        </div>
                                    ))}
                                    {role.users_count === 0 && (
                                        <p className="text-muted-foreground text-center py-4 text-sm">
                                            No users assigned to this role
                                        </p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Permissions Selection */}
                    <div className="lg:col-span-2">
                        <form onSubmit={handlePermissionSubmit}>
                            <PermissionsSelector
                                permissions={permissions}
                                selectedPermissions={permissionData.permissions}
                                onPermissionChange={handlePermissionChange}
                                onCategoryToggle={handleCategoryToggle}
                                showChangeBadges={true}
                                originalPermissions={role.current_permissions}
                                title="Role Permissions"
                                description="Select the permissions this role should have"
                            />
                        </form>

                        {/* Permission Actions */}
                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Shield className="mr-2 h-4 w-4" />
                                    Permission Summary
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <div className="flex items-center justify-between text-sm">
                                        <span className="text-muted-foreground">Total Selected:</span>
                                        <Badge variant="secondary">{permissionData.permissions.length}</Badge>
                                    </div>
                                    <div className="flex items-center justify-between text-sm">
                                        <span className="text-muted-foreground">Originally:</span>
                                        <Badge variant="outline">{role.current_permissions.length}</Badge>
                                    </div>
                                    <div className="flex items-center justify-between text-sm">
                                        <span className="text-muted-foreground">Changes:</span>
                                        <Badge variant={hasPermissionChanges ? 'destructive' : 'secondary'}>{hasPermissionChanges ? 'Yes' : 'None'}</Badge>
                                    </div>
                                </div>

                                <Separator />

                                <div className="space-y-2">
                                    <Button
                                        type="submit"
                                        className="w-full"
                                        disabled={!hasPermissionChanges || permissionProcessing}
                                        onClick={handlePermissionSubmit}
                                    >
                                        <Save className="mr-2 h-4 w-4" />
                                        {permissionProcessing ? 'Saving...' : 'Save Permissions'}
                                    </Button>
                                    <Button variant="outline" className="w-full" onClick={resetPermissionChanges} disabled={!hasPermissionChanges}>
                                        Reset Changes
                                    </Button>
                                </div>

                                {/* Permission Breakdown */}
                                <div className="border-t pt-4">
                                    <h4 className="text-sm font-medium mb-2">Permission Breakdown</h4>
                                    <div className="space-y-1">
                                        {permissions.map((permissionCategory) => {
                                            const selectedCount = permissionCategory.permissions.filter((p) => permissionData.permissions.includes(p.id)).length;
                                            return (
                                                <div key={permissionCategory.category} className="flex items-center justify-between text-xs">
                                                    <span className="text-muted-foreground">{permissionCategory.category}:</span>
                                                    <span>
                                                        {selectedCount}/{permissionCategory.permissions.length}
                                                    </span>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
