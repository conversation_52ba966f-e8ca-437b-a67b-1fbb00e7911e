/**
 * AG Grid French localization text
 * This file contains French translations for AG Grid components
 *
 * Usage:
 * import { frenchLocaleText } from '@/components/ag-grid/localization';
 *
 * Then add it to your AgGridReact component:
 * <AgGridReact
 *   localeText={frenchLocaleText}
 *   // other props...
 * />
 */

export const frenchLocaleText = {
    // Pagination panel
    page: 'Page',
    more: 'Plus',
    of: 'sur',
    to: 'à',
    next: 'Suivant',
    last: 'Dernier',
    first: 'Premier',
    previous: 'Précédent',
    loadingOoo: 'Chargement...',

    // Pagination panel summary
    totalRows: 'Total des lignes',
    totalAndFilteredRows: 'Lignes',
    rowCount: 'Nombre de lignes',
    filteredRowCount: 'Lignes filtrées',

    // Size dropdown
    pageSize: 'Taille de page',

    // Filter panel
    filters: 'Filtres',
    filterOoo: 'Filtrer...',
    equals: 'Égal',
    notEqual: 'Différent de',
    contains: 'Contient',
    notContains: 'Ne contient pas',
    startsWith: 'Commence par',
    endsWith: 'Finit par',
    blank: 'Vide',
    notBlank: 'Non vide',

    // Other components
    noRowsToShow: 'Aucune donnée à afficher',
    enabled: 'Activé',
    pinColumn: 'Épingler colonne',
    pinLeft: 'Épingler à gauche',
    pinRight: 'Épingler à droite',
    noPin: 'Désépingler',
    autosizeThiscolumn: 'Ajuster cette colonne',
    autosizeAllColumns: 'Ajuster toutes les colonnes',
    resetColumns: 'Réinitialiser les colonnes',
};
