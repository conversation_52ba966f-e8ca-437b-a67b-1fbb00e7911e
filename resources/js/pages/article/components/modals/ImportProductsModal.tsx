import { Button } from '@/components/ui/button';
import {
    ExpandedDialog,
    ExpandedDialogBody,
    ExpandedDialogContent,
    ExpandedDialogFooter,
    ExpandedDialogHeader,
    ExpandedDialogTitle,
} from '@/components/ui/expanded-dialog';
import { Progress } from '@/components/ui/progress';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { importFinishedProducts, parseArticleExcelFile, ParsedArticle } from '@/lib/excel';
import { AlertCircle, AlertTriangle, Check, Info, Loader2, Sheet, Upload, X } from 'lucide-react';
import { useCallback, useRef, useState } from 'react';

interface ImportProductsModalProps {
    isOpen: boolean;
    onClose: () => void;
    onImportComplete?: () => void; // Callback to refresh data after import
}

export function ImportProductsModal({ isOpen, onClose, onImportComplete }: ImportProductsModalProps) {
    const [step, setStep] = useState<1 | 2 | 3>(1);
    const [file, setFile] = useState<File | null>(null);
    const [dragActive, setDragActive] = useState<boolean>(false);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [transitioning, setTransitioning] = useState<boolean>(false);

    // State for parsed data
    const [parsedData, setParsedData] = useState<{
        headers: string[];
        data: ParsedArticle[];
        hasErrors: boolean;
        errors: { row: number; message: string }[];
    } | null>(null);

    // State for import status
    const [importStatus, setImportStatus] = useState<{
        isLoading: boolean;
        success?: boolean;
        message?: string;
        created?: number;
        updated?: number;
        errors?: string[];
        errorRows?: number[];
        progress?: number;
    }>({ isLoading: false });

    // Handle file input change
    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const selectedFile = e.target.files[0];
            setFile(selectedFile);

            try {
                // Parse the Excel file for preview using the article-specific parser
                const result = await parseArticleExcelFile(selectedFile);
                setParsedData(result);
            } catch (error) {
                console.error('Error parsing Excel file:', error);
                setParsedData({
                    headers: [],
                    data: [],
                    hasErrors: true,
                    errors: [{ row: 0, message: 'Erreur lors de la lecture du fichier Excel' }],
                });
            }
        }
    };

    // Drag event handlers
    const handleDrag = useCallback((e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();

        if (e.type === 'dragenter' || e.type === 'dragover') {
            setDragActive(true);
        } else if (e.type === 'dragleave') {
            setDragActive(false);
        }
    }, []);

    // Drop handler
    const handleDrop = useCallback(async (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);

        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            const droppedFile = e.dataTransfer.files[0];
            const fileExtension = droppedFile.name.split('.').pop()?.toLowerCase();

            if (fileExtension === 'xlsx' || fileExtension === 'xls') {
                setFile(droppedFile);

                try {
                    // Parse the Excel file for preview using the article-specific parser
                    const result = await parseArticleExcelFile(droppedFile);
                    setParsedData(result);
                } catch (error) {
                    console.error('Error parsing Excel file:', error);
                    setParsedData({
                        headers: [],
                        data: [],
                        hasErrors: true,
                        errors: [{ row: 0, message: 'Erreur lors de la lecture du fichier Excel' }],
                    });
                }
            } else {
                setParsedData({
                    headers: [],
                    data: [],
                    hasErrors: true,
                    errors: [{ row: 0, message: 'Veuillez sélectionner un fichier Excel (.xlsx, .xls)' }],
                });
            }
        }
    }, []);

    // Button click handler for file input
    const handleButtonClick = (e?: React.MouseEvent) => {
        // If event exists, it means it was triggered by a click
        if (e) {
            // Prevent click from bubbling up to the container
            e.stopPropagation();
        }
        fileInputRef.current?.click();
    };

    // Handle container click
    const handleContainerClick = () => {
        fileInputRef.current?.click();
    };

    // Reset the modal
    const resetModal = () => {
        setStep(1);
        setFile(null);
        setParsedData(null);
        setImportStatus({ isLoading: false });
        onClose();
    };

    // Navigation handlers
    const handleNext = async () => {
        if (step < 3) {
            // Add transition animation by setting transitioning state
            setTransitioning(true);

            // If moving from step 2 to 3, first show the loading UI, then start the import after a delay
            if (step === 2 && file) {
                // First just set loading state with progress at 0
                setImportStatus({ isLoading: true, progress: 0 });

                // Setup progress simulation
                const simulateProgress = () => {
                    setImportStatus((prev) => {
                        if (prev.isLoading && prev.progress !== undefined && prev.progress < 90) {
                            // Increase progress more quickly at the beginning, then slow down
                            const increment = prev.progress < 30 ? 10 : prev.progress < 60 ? 5 : 2;
                            return { ...prev, progress: Math.min(prev.progress + increment, 90) };
                        }
                        return prev;
                    });
                };

                // Start progress simulation
                const progressInterval = setInterval(simulateProgress, 500);

                // Delay the actual import process by 2 seconds to see the animation
                setTimeout(async () => {
                    try {
                        // Import the data
                        const result = await importFinishedProducts(file);

                        // Clear interval and set final status
                        clearInterval(progressInterval);

                        // Complete progress to 100% and show success after a short delay
                        setImportStatus((prev) => ({ ...prev, progress: 100 }));

                        setTimeout(() => {
                            setImportStatus({
                                isLoading: false,
                                success: result.success,
                                message: result.message,
                                created: result.created,
                                updated: result.updated,
                                errors: result.errors,
                                errorRows: result.errorRows,
                                progress: 100,
                            });

                            // Call onImportComplete if provided and import was successful
                            if (result.success && onImportComplete) {
                                onImportComplete();
                            }
                        }, 500);
                    } catch (error) {
                        // Clear interval
                        clearInterval(progressInterval);

                        setImportStatus({
                            isLoading: false,
                            success: false,
                            message: "Une erreur est survenue lors de l'importation",
                            errors: [error instanceof Error ? error.message : 'Erreur inconnue'],
                            progress: 0,
                        });
                    }
                }, 3000); // 3-second delay before starting the actual import
            }

            setTimeout(() => {
                setStep((prev) => (prev === 1 ? 2 : 3) as 1 | 2 | 3);
                setTransitioning(false);
            }, 300);
        } else {
            resetModal();
        }
    };

    const handlePrevious = () => {
        if (step > 1) {
            // Add transition animation by setting transitioning state
            setTransitioning(true);
            setTimeout(() => {
                setStep((prev) => (prev === 3 ? 2 : 1) as 1 | 2 | 3);
                setTransitioning(false);
            }, 300);
        }
    };

    // Render step indicator
    const renderStepIndicator = () => {
        const steps = [
            { num: 1, label: 'Sélectionner', icon: <Upload className="h-4 w-4" /> },
            { num: 2, label: 'Prévisualiser', icon: <Info className="h-4 w-4" /> },
            { num: 3, label: 'Importer', icon: <Check className="h-4 w-4" /> },
        ];

        return (
            <div className="mb-8 pt-4">
                <div className="relative flex items-center justify-between">
                    {/* Connecting lines between steps */}
                    <div className="absolute top-1/2 left-0 h-1 w-full -translate-y-1/2 bg-gray-200">
                        <div
                            className="h-full bg-blue-500 transition-all duration-500 ease-in-out"
                            style={{
                                width: step === 1 ? '0%' : step === 2 ? '50%' : '100%',
                            }}
                        />
                    </div>

                    {/* Step indicators */}
                    {steps.map((stepItem) => (
                        <div key={stepItem.num} className="relative z-10 flex flex-col items-center">
                            <div
                                className={`flex h-12 w-12 items-center justify-center rounded-full border-2 shadow-sm transition-all duration-500 ease-in-out ${
                                    step === stepItem.num
                                        ? 'scale-110 border-blue-600 bg-blue-600 text-white'
                                        : step > stepItem.num
                                          ? 'border-green-500 bg-green-500 text-white'
                                          : 'border-gray-300 bg-white text-gray-400'
                                }`}
                            >
                                {step > stepItem.num ? (
                                    <Check className="h-5 w-5 animate-pulse" />
                                ) : (
                                    <span className={`transition-all duration-300 ${step === stepItem.num ? 'scale-110' : ''}`}>{stepItem.icon}</span>
                                )}
                            </div>

                            <div className="absolute -bottom-6 w-28 text-center">
                                <p
                                    className={`text-xs font-medium transition-colors duration-300 ${
                                        step === stepItem.num ? 'font-semibold text-blue-600' : 'text-gray-500'
                                    }`}
                                >
                                    {stepItem.label}
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
                <div className="h-6"></div>
            </div>
        );
    };

    // Render steps
    const renderStep = () => {
        switch (step) {
            case 1:
                return (
                    <div className="flex flex-col items-center space-y-6 py-6">
                        <div className="relative w-full rounded-md border border-blue-200 bg-blue-50 px-4 py-3 text-blue-800" role="alert">
                            <Info className="-mt-1 mr-2 inline h-5 w-5" />
                            <span className="block sm:inline">Sélectionnez un fichier Excel contenant les articles à importer.</span>
                        </div>

                        <div
                            className={`group flex w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed bg-gray-50 p-10 transition-colors ${dragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:bg-gray-100'}`}
                            onDragEnter={handleDrag}
                            onDragLeave={handleDrag}
                            onDragOver={handleDrag}
                            onDrop={handleDrop}
                            onClick={handleContainerClick}
                        >
                            <Sheet className="mb-4 h-16 w-16 text-gray-400 transition-colors group-hover:text-blue-500" />
                            <p className="mb-2 font-medium text-gray-700 transition-colors group-hover:text-blue-700">
                                Glissez-déposez votre fichier Excel ici
                            </p>
                            <p className="mb-4 text-sm text-gray-500">ou cliquez n'importe où dans cette zone</p>
                            <Button variant="outline" onClick={(e) => handleButtonClick(e)}>
                                <Upload className="mr-2 h-4 w-4" />
                                Parcourir
                            </Button>
                            <input
                                ref={fileInputRef}
                                id="file-upload"
                                type="file"
                                className="hidden"
                                accept=".xlsx,.xls"
                                onChange={handleFileChange}
                            />

                            {file && <div className="mt-4 text-sm font-medium text-green-600">Fichier sélectionné: {file.name}</div>}
                        </div>
                    </div>
                );
            case 2:
                return (
                    <div className="py-6">
                        {parsedData?.hasErrors && (
                            <div className="mb-4 rounded-md border border-orange-200 bg-orange-50 p-3 text-orange-800">
                                <div className="flex items-center">
                                    <AlertTriangle className="mr-2 h-5 w-5 text-orange-600" />
                                    <span className="font-medium">Attention: certaines lignes contiennent des erreurs</span>
                                </div>
                                <ul className="mt-2 ml-7 list-disc text-sm">
                                    {parsedData.errors.map((error, index) => (
                                        <li key={index}>
                                            {error.row > 0 ? `Ligne ${error.row}: ` : ''}
                                            {error.message}
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        )}

                        <p className="mb-4 text-gray-600">Aperçu des données à importer ({parsedData?.data.length || 0} articles)</p>

                        <div className="max-h-[60vh] overflow-hidden overflow-y-auto rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="bg-background sticky top-0 whitespace-nowrap">TCLCOD_0</TableHead>
                                        <TableHead className="bg-background sticky top-0 whitespace-nowrap">ITMREF_0</TableHead>
                                        <TableHead className="bg-background sticky top-0 whitespace-nowrap">CPNITMREF_0</TableHead>
                                        <TableHead className="bg-background sticky top-0 whitespace-nowrap">BOMQTY_0</TableHead>
                                        <TableHead className="bg-background sticky top-0 whitespace-nowrap">BOMUOM_0</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {parsedData && parsedData.data.length > 0 ? (
                                        parsedData.data.map((item, index) => (
                                            <TableRow key={index}>
                                                <TableCell className="whitespace-nowrap">{item.tclcod}</TableCell>
                                                <TableCell className="whitespace-nowrap">{item.itmref}</TableCell>
                                                <TableCell className="whitespace-nowrap">{item.cpnitmref}</TableCell>
                                                <TableCell className="whitespace-nowrap">{item.bomqty}</TableCell>
                                                <TableCell className="whitespace-nowrap">{item.bomuom}</TableCell>
                                            </TableRow>
                                        ))
                                    ) : (
                                        <TableRow>
                                            <TableCell colSpan={5} className="py-4 text-center">
                                                Aucune donnée à afficher
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                );
            case 3:
                if (importStatus.isLoading) {
                    return (
                        <div className="flex flex-col items-center py-10 text-center">
                            <div className="relative mb-6 h-24 w-24">
                                <div className="absolute inset-0 flex items-center justify-center">
                                    <div className="text-lg font-semibold text-blue-600">
                                        {importStatus.progress !== undefined ? `${Math.round(importStatus.progress)}%` : ''}
                                    </div>
                                </div>
                                <svg className="h-24 w-24 -rotate-90 transform" viewBox="0 0 100 100">
                                    <circle
                                        className="text-gray-200"
                                        strokeWidth="8"
                                        stroke="currentColor"
                                        fill="transparent"
                                        r="42"
                                        cx="50"
                                        cy="50"
                                    />
                                    <circle
                                        className="text-blue-500"
                                        strokeWidth="8"
                                        strokeDasharray={264}
                                        strokeDashoffset={264 - ((importStatus.progress || 0) / 100) * 264}
                                        strokeLinecap="round"
                                        stroke="currentColor"
                                        fill="transparent"
                                        r="42"
                                        cx="50"
                                        cy="50"
                                    />
                                </svg>
                            </div>

                            <h2 className="mb-3 text-xl font-semibold">Importation en cours...</h2>
                            <p className="mb-4 text-gray-600">Veuillez patienter pendant que nous traitons votre fichier.</p>

                            <div className="mx-auto mb-2 w-full max-w-md px-4">
                                <Progress value={importStatus.progress} className="h-2 bg-gray-100" />
                            </div>

                            <div className="flex animate-pulse items-center justify-center space-x-2 text-sm text-gray-500">
                                <Loader2 className="h-4 w-4 animate-spin" />
                                <span>Traitement des données...</span>
                            </div>
                        </div>
                    );
                }

                if (importStatus.success) {
                    return (
                        <div className="py-6 text-center">
                            <Check className="mx-auto mb-4 h-16 w-16 text-green-500" />
                            <h2 className="mb-2 text-xl font-semibold">Importation réussie!</h2>
                            <p className="mb-2 text-gray-600">{importStatus.message}</p>
                            <div className="mt-4 flex justify-center gap-4">
                                <div className="rounded border border-green-200 bg-green-50 px-4 py-2 text-center text-green-700">
                                    <span className="block text-2xl font-semibold">{importStatus.created}</span>
                                    <span className="text-sm">Créés</span>
                                </div>
                                <div className="rounded border border-blue-200 bg-blue-50 px-4 py-2 text-center text-blue-700">
                                    <span className="block text-2xl font-semibold">{importStatus.updated}</span>
                                    <span className="text-sm">Mis à jour</span>
                                </div>
                            </div>
                        </div>
                    );
                }

                return (
                    <div className="py-6 text-center">
                        <X className="mx-auto mb-4 h-16 w-16 text-red-500" />
                        <h2 className="mb-2 text-xl font-semibold">Erreur d'importation</h2>
                        <p className="mb-4 text-gray-600">{importStatus.message || "Une erreur est survenue lors de l'importation."}</p>

                        {importStatus.errors && importStatus.errors.length > 0 && (
                            <div className="mx-auto mt-4 max-w-md rounded-md border border-red-200 bg-red-50 p-3 text-left text-red-800">
                                <div className="mb-2 flex items-center">
                                    <AlertCircle className="mr-2 h-5 w-5 text-red-600" />
                                    <span className="font-medium">Erreurs détectées:</span>
                                </div>
                                <ul className="ml-7 list-disc text-sm">
                                    {importStatus.errors.map((error, index) => (
                                        <li key={index}>{error}</li>
                                    ))}
                                </ul>
                            </div>
                        )}
                    </div>
                );
            default:
                return null;
        }
    };

    // Render the footer with disabled state for the next button
    const renderFooter = () => {
        return (
            <div className="flex w-full justify-between">
                <div>
                    {step > 1 && (
                        <Button variant="outline" onClick={handlePrevious} disabled={step === 3 && importStatus.isLoading}>
                            Précédent
                        </Button>
                    )}
                </div>

                <div className="flex space-x-2">
                    <Button variant="ghost" onClick={resetModal} disabled={step === 3 && importStatus.isLoading}>
                        {step === 3 && (importStatus.success || (!importStatus.isLoading && !importStatus.success)) ? 'Fermer' : 'Annuler'}
                    </Button>
                    <Button
                        onClick={handleNext}
                        disabled={
                            (step === 1 && !file) ||
                            (step === 2 && (!parsedData || parsedData.data.length === 0)) ||
                            (step === 3 && importStatus.isLoading)
                        }
                    >
                        {step === 1 ? 'Aperçu' : step === 2 ? 'Importer' : 'Terminer'}
                    </Button>
                </div>
            </div>
        );
    };

    // Return the full component
    return (
        <ExpandedDialog open={isOpen} onOpenChange={onClose}>
            <ExpandedDialogContent className="overflow-hidden p-0">
                <ExpandedDialogHeader className="p-6 pb-0">
                    <ExpandedDialogTitle className="text-lg font-semibold">Importer des articles</ExpandedDialogTitle>
                </ExpandedDialogHeader>

                <ExpandedDialogBody className="max-h-[calc(85vh-12rem)] overflow-x-hidden overflow-y-auto p-6">
                    {renderStepIndicator()}
                    <div className={`transition-opacity duration-300 ${transitioning ? 'opacity-0' : 'opacity-100'}`}>{renderStep()}</div>
                </ExpandedDialogBody>

                <ExpandedDialogFooter className="border-t p-6 pt-0">{renderFooter()}</ExpandedDialogFooter>
            </ExpandedDialogContent>
        </ExpandedDialog>
    );
}
