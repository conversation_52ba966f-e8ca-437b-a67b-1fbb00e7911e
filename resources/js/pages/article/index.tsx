import { frenchLocaleText } from './components/ag-grid/localization';
import { createLoadingOverlayTemplate, createNoRowsTemplate } from './components/ag-grid/templates';
import { ImportProductsModal } from './components/modals/ImportProductsModal';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { PERMISSIONS } from '@/constants/permissions';
import AppLayout from '@/layouts/app-layout';
import { type SharedData } from '@/types';
import { Head, router, usePage } from '@inertiajs/react';
import {
    AllCommunityModule,
    ColDef,
    GridApi,
    GridReadyEvent,
    ICellRendererParams,
    IDatasource,
    IGetRowsParams,
    ModuleRegistry,
    PaginationChangedEvent,
    RowDoubleClickedEvent,
    themeAlpine,
} from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import axios from 'axios';
import { Eye, FileUp, Layers, Pencil, Plus, RefreshCw, Trash2 } from 'lucide-react';
import { useCallback, useRef, useState } from 'react';
import { toast } from 'sonner';

// Register AG Grid Modules
ModuleRegistry.registerModules([AllCommunityModule]);

interface Article {
    id: number;
    article_code: string;
    article_name: string;
    available_stock: number;
    batch_size: number;
    family: {
        id: number;
        family_code: string;
        family_name: string;
    } | null;
    created_at: string;
    updated_at: string;
    finished_product?: {
        id: number;
    };
}

export default function Article() {
    const { auth } = usePage<SharedData>().props;

    // Permission checks
    const canViewArticle = auth.permissions.includes(PERMISSIONS.VIEW_ARTICLE);
    const canCreateArticle = auth.permissions.includes(PERMISSIONS.CREATE_ARTICLE);
    const canEditArticle = auth.permissions.includes(PERMISSIONS.EDIT_ARTICLE);
    const canDeleteArticle = auth.permissions.includes(PERMISSIONS.DELETE_ARTICLE);
    const canImportArticle = auth.permissions.includes(PERMISSIONS.IMPORT_ARTICLE);

    // Grid API reference
    const gridApiRef = useRef<GridApi<Article> | null>(null);

    // Track if grid has been initialized at least once
    const [isGridInitialized, setIsGridInitialized] = useState<boolean>(false);

    // Modal states
    const [showViewModal, setShowViewModal] = useState<boolean>(false);
    const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
    const [showEditModal, setShowEditModal] = useState<boolean>(false);
    const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState<boolean>(false);
    const [showImportModal, setShowImportModal] = useState<boolean>(false);
    const [selectedArticle, setSelectedArticle] = useState<Article | null>(null);
    const [isRefreshing, setIsRefreshing] = useState<boolean>(false);

    // Handle edit action
    const handleEdit = useCallback(() => {
        if (selectedArticle) {
            setShowEditModal(true);
        } else {
            toast.error('Veuillez sélectionner un article à modifier', {
                duration: 4000,
            });
        }
    }, [selectedArticle]);

    // Handle delete action
    const handleDelete = useCallback(() => {
        if (selectedArticle) {
            setShowDeleteConfirmModal(true);
        } else {
            toast.error('Veuillez sélectionner un article à supprimer', {
                duration: 4000,
            });
        }
    }, [selectedArticle]);

    // Handle view action
    const handleView = useCallback(() => {
        if (selectedArticle) {
            setShowViewModal(true);
        } else {
            toast.error('Veuillez sélectionner un article pour voir les détails', {
                duration: 4000,
            });
        }
    }, [selectedArticle]);

    // Handle navigation to product composition
    const handleManageComposition = useCallback(() => {
        if (selectedArticle) {
            // Navigate to product composition page using the article ID
            router.visit(`/product-composition?id=${selectedArticle.id}`);
        }
    }, [selectedArticle]);

    // Check if the selected article is a finished product
    const isFinishedProduct = useCallback(() => {
        return selectedArticle?.family?.family_code === 'PF';
    }, [selectedArticle]);

    // Cell renderer component for composition button
    const CompositionCellRenderer = (props: ICellRendererParams<Article>) => {
        if (!props.data || props.data.family?.family_code !== 'PF') return null;

        return (
            <div className="flex h-full w-full items-center justify-center">
                <Button
                    variant="default"
                    size="icon"
                    onClick={(e) => {
                        e.stopPropagation(); // Prevent row selection
                        if (props.data) {
                            props.context.handleManageComposition(props.data);
                        }
                    }}
                    className="h-8 w-8 bg-blue-600 text-white hover:bg-blue-700"
                    title="Gérer la composition"
                >
                    <Layers className="size-4" />
                </Button>
            </div>
        );
    };

    // Context for cell renderers
    const gridContext = {
        handleManageComposition: (article: Article) => {
            router.visit(`/product-composition?id=${article.id}`);
        },
    };

    // Column Definitions
    const [colDefs] = useState<ColDef<Article>[]>([
        {
            field: 'id',
            headerName: 'ID',
            sortable: true,
            filter: true,
            width: 80,
        },
        {
            field: 'article_code',
            headerName: 'Code Article',
            sortable: true,
            filter: true,
        },
        {
            field: 'article_name',
            headerName: 'Désignation',
            sortable: true,
            filter: true,
        },
        {
            field: 'family.family_name',
            headerName: 'Famille Article',
            sortable: true,
            filter: true,
            valueGetter: (params) => params.data?.family?.family_name || '-',
            colId: 'family.family_name',
        },
        {
            field: 'available_stock',
            headerName: 'Stock Disponible',
            sortable: true,
            filter: true,
            type: 'numericColumn',
        },
        {
            field: 'batch_size',
            headerName: 'Taille Lot',
            sortable: true,
            filter: true,
            type: 'numericColumn',
        },
        {
            field: 'created_at',
            headerName: 'Créé le',
            sortable: true,
            filter: true,
            valueFormatter: (params) => (params.value ? new Date(params.value).toLocaleString('fr-FR') : ''),
        },
        {
            field: 'updated_at',
            headerName: 'Modifié le',
            sortable: true,
            filter: true,
            valueFormatter: (params) => (params.value ? new Date(params.value).toLocaleString('fr-FR') : ''),
        },
        {
            headerName: 'Composition',
            field: 'id',
            width: 110,
            sortable: false,
            filter: false,
            cellRenderer: CompositionCellRenderer,
            flex: 0,
            cellClass: 'ag-cell-actions',
            cellStyle: { padding: 0 },
        },
    ]);

    // Default column definitions applied to all columns
    const defaultColDef = {
        resizable: true,
        minWidth: 100,
        flex: 1,
    };

    // Create data source for infinite scrolling
    const createDatasource = useCallback((): IDatasource => {
        return {
            getRows: (params: IGetRowsParams) => {
                // Get pagination parameters
                const startRow = params.startRow || 0;
                const perPage = gridApiRef.current?.paginationGetPageSize() || 20;

                // Get sorting parameters
                const sortModel = params.sortModel;
                let sortField = 'id';
                let sortOrder = 'asc';

                if (sortModel && sortModel.length > 0) {
                    sortField = sortModel[0].colId;
                    sortOrder = sortModel[0].sort || 'asc';
                }

                // Get filter parameters
                const filterModel = params.filterModel;

                // Prepare request with Laravel paginator parameters
                const requestParams = {
                    startRow,
                    perPage,
                    sortField,
                    sortOrder,
                    filterModel,
                };

                // Make API request
                axios
                    .get(route('articles.data'), { params: requestParams })
                    .then((response) => {
                        const { rows, lastRow } = response.data;

                        // Update grid state based on response
                        if (gridApiRef.current) {
                            gridApiRef.current.setGridOption('loading', false);

                            if (!rows || rows.length === 0) {
                                gridApiRef.current.showNoRowsOverlay();
                            }
                        }

                        // Send data to the grid
                        params.successCallback(rows, lastRow);
                    })
                    .catch(() => {
                        params.failCallback();
                    });
            },
        };
    }, []);

    // Grid ready event handler
    const onGridReady = useCallback(
        (params: GridReadyEvent) => {
            gridApiRef.current = params.api;
            setIsGridInitialized(true);
            params.api.setGridOption('loading', true);

            const dataSource = createDatasource();
            params.api.setGridOption('datasource', dataSource);
        },
        [createDatasource],
    );

    // Handle pagination changes (e.g., page size selection)
    const onPaginationChanged = useCallback((event: PaginationChangedEvent<Article>) => {
        // Check if the grid api is available and if the change was triggered by user action (not initial load)
        if (gridApiRef.current && event.newPageSize) {
            // Refresh the cache to fetch data based on the new page size
            gridApiRef.current.refreshInfiniteCache();
        }
    }, []);

    // Handle refresh action
    const handleRefresh = useCallback(() => {
        if (gridApiRef.current) {
            setIsRefreshing(true);
            gridApiRef.current.refreshInfiniteCache();

            // Reset spinning state after a short delay
            setTimeout(() => {
                setIsRefreshing(false);
            }, 1000);
        }
    }, []);

    // Handle row selection
    const onRowSelectionChanged = useCallback(() => {
        if (!gridApiRef.current) return;

        const selectedRows = gridApiRef.current.getSelectedRows();
        if (selectedRows.length > 0) {
            setSelectedArticle(selectedRows[0]);
        } else {
            setSelectedArticle(null);
        }
    }, []);

    // Handle row double click
    const onRowDoubleClicked = useCallback((event: RowDoubleClickedEvent<Article>) => {
        if (event.data) {
            setSelectedArticle(event.data);
            setShowViewModal(true);
        }
    }, []);

    return (
        <AppLayout>
            <Head title="Gestion des Articles" />
            <div className="flex h-full flex-col gap-4">
                <div className="flex flex-col space-y-3">
                    {/* Action Bar */}
                    <div className="flex items-center justify-start p-2">
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                size="sm"
                                className="flex items-center gap-1"
                                onClick={handleRefresh}
                                disabled={!isGridInitialized || isRefreshing}
                            >
                                <RefreshCw className={`size-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                                <span>{isRefreshing ? 'Actualisation...' : 'Actualiser'}</span>
                            </Button>

                            {canCreateArticle && (
                                <Button
                                    variant="default"
                                    size="sm"
                                    className="flex items-center gap-1 bg-green-100 text-green-600 hover:bg-green-200 hover:text-green-700"
                                    onClick={() => setShowCreateModal(true)}
                                >
                                    <Plus className="size-4" />
                                    <span>Nouvel article</span>
                                </Button>
                            )}

                            {canViewArticle && (
                                <Button variant="outline" size="sm" className="flex items-center gap-1" onClick={handleView} disabled={!selectedArticle}>
                                    <Eye className="size-4" />
                                    <span>Voir les détails</span>
                                </Button>
                            )}

                            {canEditArticle && (
                                <Button variant="outline" size="sm" className="flex items-center gap-1" onClick={handleEdit}>
                                    <Pencil className="size-4" />
                                    <span>Modifier</span>
                                </Button>
                            )}

                            {canDeleteArticle && (
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="flex items-center gap-1 bg-red-100 text-red-600 hover:bg-red-200 hover:text-red-700"
                                    onClick={handleDelete}
                                >
                                    <Trash2 className="size-4" />
                                    <span>Supprimer</span>
                                </Button>
                            )}

                            {/* Import Articles Button */}
                            {canImportArticle && (
                                <Button variant="outline" size="sm" className="flex items-center gap-1" onClick={() => setShowImportModal(true)}>
                                    <FileUp className="size-4" />
                                    <span>Importer des articles</span>
                                </Button>
                            )}

                            {/* Composition Management Button - Only visible when a finished product is selected */}
                            {selectedArticle && isFinishedProduct() && (
                                <Button
                                    variant="default"
                                    size="sm"
                                    className="flex items-center gap-1 bg-blue-600 text-white hover:bg-blue-700"
                                    onClick={handleManageComposition}
                                >
                                    <Layers className="size-4" />
                                    <span>Gérer la composition</span>
                                </Button>
                            )}
                        </div>
                    </div>
                </div>

                {/* AG Grid Component */}
                <div className="flex-1 overflow-hidden">
                    <div className="h-full w-full">
                        <AgGridReact
                            columnDefs={colDefs}
                            defaultColDef={defaultColDef}
                            animateRows={true}
                            rowModelType="infinite"
                            cacheBlockSize={100}
                            cacheOverflowSize={2}
                            maxConcurrentDatasourceRequests={1}
                            infiniteInitialRowCount={100}
                            maxBlocksInCache={10}
                            onGridReady={onGridReady}
                            pagination={true}
                            paginationPageSizeSelector={[10, 20, 50, 100]}
                            paginationPageSize={20}
                            onRowDoubleClicked={onRowDoubleClicked}
                            onSelectionChanged={onRowSelectionChanged}
                            theme={themeAlpine}
                            overlayLoadingTemplate={createLoadingOverlayTemplate('Chargement des articles...')}
                            overlayNoRowsTemplate={createNoRowsTemplate('Aucun article trouvé')}
                            debug={false}
                            rowSelection={{
                                mode: 'singleRow',
                                enableClickSelection: true,
                            }}
                            paginationNumberFormatter={(params) => {
                                return params.value.toLocaleString('fr-FR');
                            }}
                            localeText={frenchLocaleText}
                            onPaginationChanged={onPaginationChanged}
                            context={gridContext}
                        />
                    </div>
                </div>

                {/* View Article Modal */}
                <Dialog open={showViewModal} onOpenChange={setShowViewModal}>
                    <DialogContent className="sm:max-w-3xl">
                        <DialogHeader>
                            <DialogTitle>
                                {selectedArticle?.article_name ? `Détails de l'article - ${selectedArticle.article_name}` : "Détails de l'article"}
                            </DialogTitle>
                        </DialogHeader>
                        {selectedArticle && (
                            <div className="grid grid-cols-2 gap-4 py-4">
                                <div>
                                    <p className="mb-2 text-sm font-medium text-gray-500">ID</p>
                                    <p>{selectedArticle.id}</p>
                                </div>
                                <div>
                                    <p className="mb-2 text-sm font-medium text-gray-500">Code Article</p>
                                    <p>{selectedArticle.article_code}</p>
                                </div>
                                <div>
                                    <p className="mb-2 text-sm font-medium text-gray-500">Désignation</p>
                                    <p>{selectedArticle.article_name}</p>
                                </div>
                                <div>
                                    <p className="mb-2 text-sm font-medium text-gray-500">Famille Article</p>
                                    <p>{selectedArticle.family?.family_name || '-'}</p>
                                </div>
                                <div>
                                    <p className="mb-2 text-sm font-medium text-gray-500">Stock Disponible</p>
                                    <p>{selectedArticle.available_stock}</p>
                                </div>
                                <div>
                                    <p className="mb-2 text-sm font-medium text-gray-500">Taille Lot</p>
                                    <p>{selectedArticle.batch_size}</p>
                                </div>
                                <div>
                                    <p className="mb-2 text-sm font-medium text-gray-500">Créé le</p>
                                    <p>{selectedArticle.created_at ? new Date(selectedArticle.created_at).toLocaleString('fr-FR') : ''}</p>
                                </div>
                                <div>
                                    <p className="mb-2 text-sm font-medium text-gray-500">Modifié le</p>
                                    <p>{selectedArticle.updated_at ? new Date(selectedArticle.updated_at).toLocaleString('fr-FR') : ''}</p>
                                </div>
                            </div>
                        )}
                        <DialogFooter>
                            {/* Show composition button only for finished products (PF) */}
                            {selectedArticle && selectedArticle.family?.family_code === 'PF' && (
                                <Button
                                    variant="default"
                                    className="flex items-center gap-1 bg-blue-600 text-white hover:bg-blue-700"
                                    onClick={() => {
                                        // Close the modal and navigate to product composition page
                                        setShowViewModal(false);
                                        // Use the article ID directly
                                        router.visit(`/product-composition?id=${selectedArticle.id}`);
                                    }}
                                >
                                    <Layers className="size-4" />
                                    <span>Gérer la composition</span>
                                </Button>
                            )}
                            <Button variant="outline" onClick={() => setShowViewModal(false)}>
                                Fermer
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {/* Create Article Modal */}
                <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
                    <DialogContent className="sm:max-w-3xl">
                        <DialogHeader>
                            <DialogTitle>Créer un nouvel article</DialogTitle>
                        </DialogHeader>
                        <div className="py-4">
                            <p className="text-muted-foreground mb-4">Le formulaire sera implémenté ultérieurement.</p>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="flex flex-col space-y-2">
                                    <label className="text-sm font-medium">Famille Article</label>
                                    <div className="text-muted-foreground flex h-12 items-center justify-center rounded-md border border-dashed">
                                        Sélectionner une famille
                                    </div>
                                </div>

                                <div className="flex flex-col space-y-2">
                                    <label className="text-sm font-medium">Code Article</label>
                                    <div className="text-muted-foreground flex h-12 items-center justify-center rounded-md border border-dashed">
                                        Code unique
                                    </div>
                                </div>

                                <div className="flex flex-col space-y-2">
                                    <label className="text-sm font-medium">Désignation</label>
                                    <div className="text-muted-foreground flex h-12 items-center justify-center rounded-md border border-dashed">
                                        Nom descriptif
                                    </div>
                                </div>

                                <div className="flex flex-col space-y-2">
                                    <label className="text-sm font-medium">Stock Disponible</label>
                                    <div className="text-muted-foreground flex h-12 items-center justify-center rounded-md border border-dashed">
                                        Quantité en stock
                                    </div>
                                </div>

                                <div className="flex flex-col space-y-2">
                                    <label className="text-sm font-medium">Taille Lot</label>
                                    <div className="text-muted-foreground flex h-12 items-center justify-center rounded-md border border-dashed">
                                        Taille du lot de production
                                    </div>
                                </div>
                            </div>
                        </div>
                        <DialogFooter className="gap-2">
                            <Button variant="outline" onClick={() => setShowCreateModal(false)}>
                                Annuler
                            </Button>
                            <Button>Créer</Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {/* Edit Article Modal */}
                <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
                    <DialogContent className="sm:max-w-3xl">
                        <DialogHeader>
                            <DialogTitle>
                                {selectedArticle?.article_name ? `Modifier l'article - ${selectedArticle.article_name}` : "Modifier l'article"}
                            </DialogTitle>
                        </DialogHeader>
                        <div className="py-4">
                            <p className="text-muted-foreground mb-4">Le formulaire de modification sera implémenté ultérieurement.</p>
                            {selectedArticle && (
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="flex flex-col space-y-2">
                                        <label className="text-sm font-medium">Famille Article</label>
                                        <div className="text-muted-foreground flex h-12 items-center justify-center rounded-md border border-dashed">
                                            {selectedArticle.family?.family_name || 'Sélectionner une famille'}
                                        </div>
                                    </div>

                                    <div className="flex flex-col space-y-2">
                                        <label className="text-sm font-medium">Code Article</label>
                                        <div className="text-muted-foreground flex h-12 items-center justify-center rounded-md border border-dashed">
                                            {selectedArticle.article_code}
                                        </div>
                                    </div>

                                    <div className="flex flex-col space-y-2">
                                        <label className="text-sm font-medium">Désignation</label>
                                        <div className="text-muted-foreground flex h-12 items-center justify-center rounded-md border border-dashed">
                                            {selectedArticle.article_name}
                                        </div>
                                    </div>

                                    <div className="flex flex-col space-y-2">
                                        <label className="text-sm font-medium">Stock Disponible</label>
                                        <div className="text-muted-foreground flex h-12 items-center justify-center rounded-md border border-dashed">
                                            {selectedArticle.available_stock}
                                        </div>
                                    </div>

                                    <div className="flex flex-col space-y-2">
                                        <label className="text-sm font-medium">Taille Lot</label>
                                        <div className="text-muted-foreground flex h-12 items-center justify-center rounded-md border border-dashed">
                                            {selectedArticle.batch_size}
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                        <DialogFooter className="gap-2">
                            <Button variant="outline" onClick={() => setShowEditModal(false)}>
                                Annuler
                            </Button>
                            <Button>Enregistrer</Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {/* Delete Confirmation Modal */}
                <Dialog open={showDeleteConfirmModal} onOpenChange={setShowDeleteConfirmModal}>
                    <DialogContent className="sm:max-w-md">
                        <DialogHeader>
                            <DialogTitle>Confirmer la suppression</DialogTitle>
                        </DialogHeader>
                        <div className="py-4">
                            <p>Êtes-vous sûr de vouloir supprimer l'article {selectedArticle?.article_name} ? Cette action est irréversible.</p>
                        </div>
                        <DialogFooter className="gap-2">
                            <Button variant="outline" onClick={() => setShowDeleteConfirmModal(false)}>
                                Annuler
                            </Button>
                            <Button
                                variant="destructive"
                                onClick={() => {
                                    console.log("Suppression de l'article", selectedArticle);
                                    setShowDeleteConfirmModal(false);
                                }}
                            >
                                Supprimer
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {/* Import Articles Modal */}
                <ImportProductsModal isOpen={showImportModal} onClose={() => setShowImportModal(false)} />
            </div>
        </AppLayout>
    );
}
