import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import AppLayout from '@/layouts/app-layout';
import { Head, router, usePage } from '@inertiajs/react';
import axios from 'axios';
import { ArrowLeft, Plus } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'sonner';

import type { ProductComposition } from './components';
import { AddMaterialForm, CompositionTable, FinishedProduct, ProductInfoCard } from './components';

export default function ProductComposition() {
    const [finishedProduct, setFinishedProduct] = useState<FinishedProduct | null>(null);
    const [compositions, setCompositions] = useState<ProductComposition[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [showAddMaterialModal, setShowAddMaterialModal] = useState<boolean>(false);
    const [deletingComposition, setDeletingComposition] = useState<boolean>(false);
    const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState<boolean>(false);
    const [selectedCompositionId, setSelectedCompositionId] = useState<number | null>(null);

    // Get page props from Inertia
    const { props } = usePage<{ id: string }>();
    const articleId = props.id;

    // Fetch product compositions
    const fetchCompositions = useCallback(async (productId: number) => {
        try {
            const response = await axios.get(route('product-compositions.index'), {
                params: { finished_product_id: productId },
            });
            setCompositions(response.data);
        } catch (error) {
            console.error('Error fetching compositions:', error);
        }
    }, []);

    // Fetch finished product data
    const fetchFinishedProduct = useCallback(async () => {
        setLoading(true);
        try {
            // Fetch the finished product by article ID
            const response = await axios.get(route('finished-products.by-article', { articleId }));
            console.log('Finished product data:', response.data);
            setFinishedProduct(response.data);

            // Fetch the compositions once we have the finished product
            await fetchCompositions(response.data.id);
        } catch (error) {
            // Type assertion for axios error
            const axiosError = error as { response?: { data?: { message?: string } } };
            console.error('Error fetching finished product:', error);
            toast.error(`Erreur: ${axiosError.response?.data?.message || 'Erreur lors du chargement du produit fini'}`);
            // Navigate back to articles page after a short delay
            setTimeout(() => {
                router.visit('/articles');
            }, 2000);
        } finally {
            setLoading(false);
        }
    }, [articleId, fetchCompositions]);

    useEffect(() => {
        fetchFinishedProduct();
    }, [fetchFinishedProduct]);

    // Handle back to articles page
    const handleBack = () => {
        router.visit('/articles', { preserveScroll: true });
    };

    // Handle initiate delete of a composition
    const handleInitiateDelete = (compositionId: number) => {
        setSelectedCompositionId(compositionId);
        setShowDeleteConfirmModal(true);
    };

    // Perform the actual deletion
    const performDeleteComposition = async () => {
        if (deletingComposition || !selectedCompositionId) return;

        setDeletingComposition(true);

        try {
            await axios.delete(route('product-compositions.destroy', { id: selectedCompositionId }));
            toast.success('Matière première supprimée avec succès');
            if (finishedProduct) {
                await fetchCompositions(finishedProduct.id);
            }
        } catch (error) {
            // Type assertion for axios error
            const axiosError = error as { response?: { data?: { message?: string } } };
            console.error('Error deleting composition:', error);
            toast.error(`Erreur: ${axiosError.response?.data?.message || 'Erreur lors de la suppression de la matière première'}`);
        } finally {
            setDeletingComposition(false);
            setShowDeleteConfirmModal(false);
            setSelectedCompositionId(null);
        }
    };

    return (
        <AppLayout>
            <Head title="Composition du Produit" />
            <div className="flex h-full flex-col gap-4">
                {loading ? (
                    <div className="flex items-center justify-center p-8">
                        <p>Chargement des données...</p>
                    </div>
                ) : finishedProduct ? (
                    <div className="flex flex-col gap-6 p-4">
                        {/* Product Information Card */}
                        <ProductInfoCard finishedProduct={finishedProduct} />

                        {/* Action Buttons */}
                        <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm" onClick={handleBack} className="flex items-center gap-1">
                                <ArrowLeft className="size-4" />
                                <span>Retour aux articles</span>
                            </Button>
                            <Button
                                variant="default"
                                size="sm"
                                className="flex items-center gap-1 bg-green-100 text-green-600 hover:bg-green-200 hover:text-green-700"
                                onClick={() => setShowAddMaterialModal(true)}
                            >
                                <Plus className="size-4" />
                                <span>Ajouter une matière première</span>
                            </Button>
                        </div>

                        {/* Composition Table */}
                        <CompositionTable
                            compositions={compositions}
                            onAddMaterial={() => setShowAddMaterialModal(true)}
                            onDeleteMaterial={handleInitiateDelete}
                        />
                    </div>
                ) : (
                    <div className="flex items-center justify-center p-8">
                        <p>Produit non trouvé</p>
                    </div>
                )}

                {/* Add Material Modal */}
                <Dialog open={showAddMaterialModal} onOpenChange={setShowAddMaterialModal}>
                    <DialogContent className="sm:max-w-[600px]">
                        <DialogHeader>
                            <DialogTitle>Ajouter une matière première</DialogTitle>
                        </DialogHeader>
                        <AddMaterialForm
                            finishedProductId={finishedProduct?.id || 0}
                            batchSize={finishedProduct?.batch_size || 0}
                            onCancel={() => setShowAddMaterialModal(false)}
                            onSuccess={() => {
                                setShowAddMaterialModal(false);
                                if (finishedProduct) {
                                    fetchCompositions(finishedProduct.id);
                                }
                                toast.success('Matière première ajoutée avec succès');
                            }}
                        />
                    </DialogContent>
                </Dialog>

                {/* Delete Confirmation Modal */}
                <Dialog open={showDeleteConfirmModal} onOpenChange={setShowDeleteConfirmModal}>
                    <DialogContent className="sm:max-w-md">
                        <DialogHeader>
                            <DialogTitle>Confirmer la suppression</DialogTitle>
                        </DialogHeader>
                        <div className="py-4">
                            <p>Êtes-vous sûr de vouloir supprimer cette matière première de la composition ? Cette action est irréversible.</p>
                        </div>
                        <DialogFooter className="gap-2">
                            <Button variant="outline" onClick={() => setShowDeleteConfirmModal(false)} disabled={deletingComposition}>
                                Annuler
                            </Button>
                            <Button variant="destructive" onClick={performDeleteComposition} disabled={deletingComposition}>
                                {deletingComposition ? 'Suppression...' : 'Supprimer'}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
}
