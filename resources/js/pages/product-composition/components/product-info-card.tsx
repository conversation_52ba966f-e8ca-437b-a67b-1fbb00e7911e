import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { FinishedProduct } from './types';

interface ProductInfoCardProps {
    finishedProduct: FinishedProduct;
}

export function ProductInfoCard({ finishedProduct }: ProductInfoCardProps) {
    return (
        <Card>
            <CardHeader>
                <CardTitle>Informations du produit fini</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="grid grid-cols-3 gap-4">
                    <div>
                        <p className="text-sm font-medium text-gray-500">Code Article</p>
                        <p className="font-medium">{finishedProduct.article_code}</p>
                    </div>
                    <div>
                        <p className="text-sm font-medium text-gray-500">Désignation</p>
                        <p className="font-medium">{finishedProduct.article_name}</p>
                    </div>
                    <div>
                        <p className="text-sm font-medium text-gray-500">Code Vrac</p>
                        <p className="font-medium">{finishedProduct.bulk_code}</p>
                    </div>
                    <div>
                        <p className="text-sm font-medium text-gray-500">Type de Produit</p>
                        <Badge variant="outline">{finishedProduct.product_type}</Badge>
                    </div>
                    <div>
                        <p className="text-sm font-medium text-gray-500">Taille Lot</p>
                        <p className="font-medium">{finishedProduct.batch_size}</p>
                    </div>
                    <div>
                        <p className="text-sm font-medium text-gray-500">Stock Disponible</p>
                        <p className="font-medium">{finishedProduct.available_stock}</p>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
