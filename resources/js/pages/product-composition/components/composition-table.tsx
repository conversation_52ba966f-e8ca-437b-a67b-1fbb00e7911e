import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Trash2, Plus, Search, ChevronLeft, ChevronRight } from 'lucide-react';
import { ProductComposition } from './types';
import { Input } from '@/components/ui/input';
import { useState, useEffect } from 'react';

interface CompositionTableProps {
    compositions: ProductComposition[];
    onAddMaterial: () => void;
    onDeleteMaterial?: (compositionId: number) => void;
}

export function CompositionTable({ compositions, onAddMaterial, onDeleteMaterial }: CompositionTableProps) {
    const [searchTerm, setSearchTerm] = useState<string>('');
    const [filteredCompositions, setFilteredCompositions] = useState<ProductComposition[]>([]);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const itemsPerPage = 10;
    
    // Filter compositions based on search term
    useEffect(() => {
        if (!searchTerm.trim()) {
            setFilteredCompositions(compositions);
            return;
        }
        
        const lowerCaseSearchTerm = searchTerm.toLowerCase();
        const filtered = compositions.filter(composition => 
            composition.primary_material_article?.article_name.toLowerCase().includes(lowerCaseSearchTerm) ||
            (composition.primary_material_article?.material_type === 'principe_actif' ? 'principe actif' : 'excipient').includes(lowerCaseSearchTerm)
        );
        
        setFilteredCompositions(filtered);
        setCurrentPage(1); // Reset to first page when search changes
    }, [searchTerm, compositions]);
    
    // Calculate pagination values
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = filteredCompositions.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredCompositions.length / itemsPerPage);
    
    // Handle page navigation
    const goToPage = (pageNumber: number) => {
        setCurrentPage(pageNumber);
    };
    
    return (
        <div className="rounded-lg border bg-card p-4 shadow-sm">
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-medium">Composition du produit</h2>
                
                {compositions && compositions.length > 0 && (
                    <div className="relative w-72">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                            placeholder="Rechercher..."
                            className="pl-8"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </div>
                )}
            </div>
            
            {compositions && compositions.length > 0 ? (
                <>
                    <div className="overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Matière Première</TableHead>
                                    <TableHead>Type</TableHead>
                                    <TableHead>Quantité par unité</TableHead>
                                    <TableHead>Quantité par lot</TableHead>
                                    <TableHead>Unité</TableHead>
                                    <TableHead className="w-[80px]">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {currentItems.length > 0 ? (
                                    currentItems.map((composition) => (
                                        <TableRow key={composition.id} className="hover:bg-muted/20 transition-colors">
                                            <TableCell className="font-medium">{composition.primary_material_article?.article_name}</TableCell>
                                            <TableCell>
                                                <Badge variant={composition.primary_material_article?.material_type === 'principe_actif' ? 'default' : 'secondary'}>
                                                    {composition.primary_material_article?.material_type === 'principe_actif' 
                                                        ? 'Principe Actif' 
                                                        : 'Excipient'}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>{composition.quantity_per_unit}</TableCell>
                                            <TableCell>{composition.quantity_per_lot}</TableCell>
                                            <TableCell>{composition.unit_of_measure}</TableCell>
                                            <TableCell>
                                                <Button 
                                                    variant="ghost" 
                                                    size="icon"
                                                    className="text-red-500 hover:text-red-700"
                                                    onClick={() => onDeleteMaterial && onDeleteMaterial(composition.id)}
                                                >
                                                    <Trash2 className="size-4" />
                                                </Button>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                ) : (
                                    <TableRow>
                                        <TableCell colSpan={6} className="h-24 text-center">
                                            Aucun résultat trouvé
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </div>
                    
                    {filteredCompositions.length > 0 && (
                        <div className="mt-4 py-2 flex items-center justify-between">
                            <div className="text-sm text-muted-foreground">
                                Affichage de {indexOfFirstItem + 1} à {Math.min(indexOfLastItem, filteredCompositions.length)} sur {filteredCompositions.length} matières premières
                            </div>
                            <div className="flex items-center gap-1">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => goToPage(currentPage - 1)}
                                    disabled={currentPage === 1}
                                    className="h-8 w-8 p-0"
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                </Button>
                                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                    let pageNum;
                                    if (totalPages <= 5) {
                                        pageNum = i + 1;
                                    } else if (currentPage <= 3) {
                                        pageNum = i + 1;
                                    } else if (currentPage >= totalPages - 2) {
                                        pageNum = totalPages - 4 + i;
                                    } else {
                                        pageNum = currentPage - 2 + i;
                                    }
                                    
                                    return (
                                        <Button
                                            key={pageNum}
                                            variant={currentPage === pageNum ? "default" : "outline"}
                                            size="sm"
                                            onClick={() => goToPage(pageNum)}
                                            className="h-8 w-8 p-0"
                                        >
                                            {pageNum}
                                        </Button>
                                    );
                                })}
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => goToPage(currentPage + 1)}
                                    disabled={currentPage === totalPages || totalPages === 0}
                                    className="h-8 w-8 p-0"
                                >
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    )}
                </>
            ) : (
                <div className="flex flex-col items-center justify-center p-8 text-center">
                    <p className="mb-4 text-muted-foreground">Aucune matière première n'a été ajoutée à ce produit.</p>
                    <Button 
                        variant="outline" 
                        onClick={onAddMaterial}
                        className="flex items-center gap-1"
                    >
                        <Plus className="size-4" />
                        <span>Ajouter une matière première</span>
                    </Button>
                </div>
            )}
        </div>
    );
}
