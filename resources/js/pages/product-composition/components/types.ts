export interface PrimaryMaterial {
    id: number;
    article_code: string;
    article_name: string;
    article_type: string;
    available_stock: number;
    material_type: 'principe_actif' | 'excipient';
    unit_of_measure: string;
    is_active: boolean;
}

export interface PrimaryMaterialSearchResult extends PrimaryMaterial {
    selected?: boolean;
}

export interface ProductComposition {
    id: number;
    finished_product_article_id: number;
    primary_material_article_id: number;
    quantity_per_unit: number;
    quantity_per_lot: number;
    unit_of_measure: string;
    primary_material_article?: Article;
}

export interface Article {
    id: number;
    article_code: string;
    article_name: string;
    article_type: string;
    available_stock: number;
    batch_size: number;
    bulk_code?: string;
    is_complex?: boolean;
    is_sample?: boolean;
    product_type?: string;
    target_coverage_months?: number;
    monthly_budget?: number;
    material_type?: string;
    unit_of_measure?: string;
    is_active: boolean;
    created_at?: string;
    updated_at?: string;
    family?: {
        id: number;
        family_code: string;
        family_name: string;
    } | null;
}

// Alias FinishedProduct to Article for compatibility
export type FinishedProduct = Article;
