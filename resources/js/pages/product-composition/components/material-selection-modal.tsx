import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Search, Loader2, ChevronLeft, ChevronRight } from 'lucide-react';
import { PrimaryMaterialSearchResult } from './types';
import { Button } from '@/components/ui/button';
import axios from 'axios';

interface MaterialSelectionModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSelect: (material: PrimaryMaterialSearchResult) => void;
}

export function MaterialSelectionModal({ isOpen, onClose, onSelect }: MaterialSelectionModalProps) {
    const [searchTerm, setSearchTerm] = useState<string>('');
    const [materials, setMaterials] = useState<PrimaryMaterialSearchResult[]>([]);
    const [filteredMaterials, setFilteredMaterials] = useState<PrimaryMaterialSearchResult[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const itemsPerPage = 10;
    
    useEffect(() => {
        if (isOpen) {
            setIsLoading(true);
            setError(null);
            
            axios.get(route('primary-materials.index'))
                .then((response) => {
                    setMaterials(response.data);
                    setFilteredMaterials(response.data);
                    setIsLoading(false);
                })
                .catch(err => {
                    console.error('Error fetching primary materials:', err);
                    setError('Failed to load primary materials. Please try again.');
                    setIsLoading(false);
                });
        }
    }, [isOpen]);
    
    useEffect(() => {
        if (!searchTerm.trim()) {
            setFilteredMaterials(materials);
            return;
        }
        
        const lowerCaseSearchTerm = searchTerm.toLowerCase();
        const filtered = materials.filter(material => 
            material.article_code.toLowerCase().includes(lowerCaseSearchTerm) ||
            material.article_name.toLowerCase().includes(lowerCaseSearchTerm) ||
            (material.material_type === 'principe_actif' ? 'principe actif' : 'excipient').includes(lowerCaseSearchTerm)
        );
        
        setFilteredMaterials(filtered);
    }, [searchTerm, materials]);
    
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = filteredMaterials.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredMaterials.length / itemsPerPage);
    
    const goToPage = (pageNumber: number) => {
        setCurrentPage(pageNumber);
    };
    
    return (
        <Dialog open={isOpen} onOpenChange={() => onClose()}>
            <DialogContent className="sm:max-w-[800px] sm:max-h-[80vh] overflow-hidden">
                <DialogHeader>
                    <DialogTitle>Liste Articles</DialogTitle>
                </DialogHeader>
                
                <div className="space-y-4">
                    <div className="flex items-center gap-2">
                        <div className="relative flex-1">
                            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                placeholder="Rechercher par code, désignation ou famille..."
                                className="pl-8"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                            />
                        </div>
                    </div>
                    
                    <div className="rounded-md border flex flex-col h-96">
                        {isLoading ? (
                            <div className="flex items-center justify-center h-24">
                                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                                <span className="ml-2">Chargement des données...</span>
                            </div>
                        ) : error ? (
                            <div className="flex items-center justify-center h-24 text-red-500">
                                {error}
                            </div>
                        ) : (
                            <>
                                <div className="flex-1 overflow-auto">
                                    <Table>
                                        <TableHeader className="sticky top-0 bg-background z-10">
                                            <TableRow>
                                                <TableHead className="w-32">Code article</TableHead>
                                                <TableHead>Désignation</TableHead>
                                                <TableHead className="w-40">Famille Article</TableHead>
                                                <TableHead className="w-32">Stock disponible</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {currentItems.length > 0 ? (
                                                currentItems.map((material) => (
                                                    <TableRow 
                                                        key={material.id} 
                                                        className="cursor-pointer hover:bg-muted transition-colors even:bg-muted/30"
                                                        onClick={() => onSelect(material)}
                                                        onDoubleClick={() => onSelect(material)}
                                                    >
                                                        <TableCell className="font-medium">{material.article_code}</TableCell>
                                                        <TableCell>{material.article_name}</TableCell>
                                                        <TableCell>
                                                            {material.material_type === 'principe_actif' ? 'Principe actif' : 'Excipient'}
                                                        </TableCell>
                                                        <TableCell className="text-right">{material.available_stock} {material.unit_of_measure}</TableCell>
                                                    </TableRow>
                                                ))
                                            ) : (
                                                <TableRow>
                                                    <TableCell colSpan={5} className="h-24 text-center">
                                                        Aucun résultat trouvé
                                                    </TableCell>
                                                </TableRow>
                                            )}
                                        </TableBody>
                                    </Table>
                                </div>
                                
                                {filteredMaterials.length > 0 && (
                                    <div className="sticky bottom-0 bg-white border-t py-2 px-4 flex items-center justify-between">
                                        <div className="text-sm text-muted-foreground">
                                            Affichage de {indexOfFirstItem + 1} à {Math.min(indexOfLastItem, filteredMaterials.length)} sur {filteredMaterials.length} articles
                                        </div>
                                        <div className="flex items-center gap-1">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => goToPage(currentPage - 1)}
                                                disabled={currentPage === 1}
                                                className="h-8 w-8 p-0"
                                            >
                                                <ChevronLeft className="h-4 w-4" />
                                            </Button>
                                            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                                let pageNum;
                                                if (totalPages <= 5) {
                                                    pageNum = i + 1;
                                                } else if (currentPage <= 3) {
                                                    pageNum = i + 1;
                                                } else if (currentPage >= totalPages - 2) {
                                                    pageNum = totalPages - 4 + i;
                                                } else {
                                                    pageNum = currentPage - 2 + i;
                                                }
                                                
                                                return (
                                                    <Button
                                                        key={pageNum}
                                                        variant={currentPage === pageNum ? "default" : "outline"}
                                                        size="sm"
                                                        onClick={() => goToPage(pageNum)}
                                                        className="h-8 w-8 p-0"
                                                    >
                                                        {pageNum}
                                                    </Button>
                                                );
                                            })}
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => goToPage(currentPage + 1)}
                                                disabled={currentPage === totalPages}
                                                className="h-8 w-8 p-0"
                                            >
                                                <ChevronRight className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}
