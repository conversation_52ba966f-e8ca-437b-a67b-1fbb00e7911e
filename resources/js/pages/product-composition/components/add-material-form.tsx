import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { DialogFooter } from '@/components/ui/dialog';
import { Search, Info, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { PrimaryMaterialSearchResult } from './types';
import { MaterialSelectionModal } from './material-selection-modal';
import axios from 'axios';

interface AddMaterialFormProps {
    finishedProductId: number;
    batchSize: number;
    onCancel: () => void;
    onSuccess: () => void;
}

export function AddMaterialForm({ finishedProductId, batchSize, onCancel, onSuccess }: AddMaterialFormProps) {
    const [selectedMaterial, setSelectedMaterial] = useState<PrimaryMaterialSearchResult | null>(null);
    const [loading, setLoading] = useState<boolean>(false);
    const [quantityPerUnit, setQuantityPerUnit] = useState<string>('');
    const [unitOfMeasure, setUnitOfMeasure] = useState<string>('g');
    const [activeTab, setActiveTab] = useState<string>('search');
    const [showMaterialSelectionModal, setShowMaterialSelectionModal] = useState<boolean>(false);
    
    // Calculate quantity per lot based on quantity per unit and batch size
    const quantityPerLot = batchSize > 0 && quantityPerUnit
        ? (parseFloat(quantityPerUnit) * batchSize).toFixed(4)
        : '';
    
    // Select a material from search results
    const handleSelectMaterial = (material: PrimaryMaterialSearchResult) => {
        setSelectedMaterial(material);
        setActiveTab('details');
        setShowMaterialSelectionModal(false);
    };
    
    // Handle form submission
    const handleSubmit = async () => {
        if (!selectedMaterial) {
            toast.error('Veuillez sélectionner une matière première');
            return;
        }
        
        if (!quantityPerUnit || parseFloat(quantityPerUnit) <= 0) {
            toast.error('Veuillez entrer une quantité valide');
            return;
        }
        
        setLoading(true);
        
        try {
            await axios.post(route('product-compositions.store'), {
                finished_product_id: finishedProductId,
                primary_material_id: selectedMaterial.id,
                quantity_per_unit: parseFloat(quantityPerUnit),
                unit_of_measure: unitOfMeasure
            });
            
            toast.success('Matière première ajoutée avec succès');
            onSuccess();
        } catch (error: unknown) {
            console.error('Error adding primary material:', error);
            
            // Show appropriate error message
            const axiosError = error as { response?: { data?: { message?: string, errors?: Record<string, string[]> } } };
            const errorMessage = axiosError.response?.data?.message 
                || (axiosError.response?.data?.errors && Object.values(axiosError.response.data.errors)[0]?.[0])
                || 'Erreur lors de l\'ajout de la matière première';
            
            toast.error(errorMessage);
        } finally {
            setLoading(false);
        }
    };
    
    return (
        <div className="space-y-4">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="search">Article</TabsTrigger>
                    <TabsTrigger value="details" disabled={!selectedMaterial}>Quantités</TabsTrigger>
                </TabsList>
                
                <TabsContent value="search" className="space-y-4">
                    <div className="flex items-end gap-2">
                        <div className="flex-1">
                            <Label htmlFor="search">Article</Label>
                            <div className="flex cursor-pointer" onClick={() => setShowMaterialSelectionModal(true)}>
                                <Input
                                    id="search"
                                    placeholder="Sélectionner un article"
                                    className="rounded-r-none cursor-pointer"
                                    value={selectedMaterial ? `${selectedMaterial.article_code} - ${selectedMaterial.article_name}` : ''}
                                    readOnly
                                />
                                <Button 
                                    type="button" 
                                    variant="outline" 
                                    className="rounded-l-none border-l-0"
                                >
                                    <Search className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </div>
                    
                    {/* Material Selection Modal */}
                    <MaterialSelectionModal 
                        isOpen={showMaterialSelectionModal}
                        onClose={() => setShowMaterialSelectionModal(false)}
                        onSelect={handleSelectMaterial}
                    />
                    
                    {selectedMaterial && (
                        <div className="rounded-md border p-4 bg-muted/30">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="text-lg font-medium">{selectedMaterial.article_name}</h3>
                                    <p className="text-sm text-muted-foreground">Code: {selectedMaterial.article_code}</p>
                                </div>
                                <Badge variant={selectedMaterial.material_type === 'principe_actif' ? 'default' : 'secondary'}>
                                    {selectedMaterial.material_type === 'principe_actif' ? 'Principe Actif' : 'Excipient'}
                                </Badge>
                            </div>
                            <div className="mt-2 text-sm">
                                <span className="font-medium">Stock disponible:</span> {selectedMaterial.available_stock} {selectedMaterial.unit_of_measure}
                            </div>
                        </div>
                    )}
                </TabsContent>
                
                <TabsContent value="details" className="space-y-4">
                    {selectedMaterial && (
                        <>
                            <div className="rounded-md border p-4">
                                <div className="mb-4 flex items-center justify-between">
                                    <div>
                                        <h3 className="text-lg font-medium">{selectedMaterial.article_name}</h3>
                                        <p className="text-sm text-muted-foreground">Code: {selectedMaterial.article_code}</p>
                                        <p className="text-sm text-muted-foreground mt-1">Stock disponible: {selectedMaterial.available_stock} {selectedMaterial.unit_of_measure}</p>
                                    </div>
                                    <Badge variant={selectedMaterial.material_type === 'principe_actif' ? 'default' : 'secondary'}>
                                        {selectedMaterial.material_type === 'principe_actif' ? 'Principe Actif' : 'Excipient'}
                                    </Badge>
                                </div>
                                
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="quantity-per-unit">Quantité unitaire</Label>
                                        <div className="mt-1 flex">
                                            <Input
                                                id="quantity-per-unit"
                                                type="number"
                                                step="0.0001"
                                                min="0"
                                                value={quantityPerUnit}
                                                onChange={(e) => setQuantityPerUnit(e.target.value)}
                                                className="rounded-r-none"
                                            />
                                            <Select value={unitOfMeasure} onValueChange={setUnitOfMeasure}>
                                                <SelectTrigger className="w-[80px] rounded-l-none">
                                                    <SelectValue placeholder="Unité" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="g">g</SelectItem>
                                                    <SelectItem value="mg">mg</SelectItem>
                                                    <SelectItem value="kg">kg</SelectItem>
                                                    <SelectItem value="ml">ml</SelectItem>
                                                    <SelectItem value="l">l</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <Label htmlFor="quantity-per-lot">Quantité par lot</Label>
                                        <div className="mt-1 flex">
                                            <Input
                                                id="quantity-per-lot"
                                                type="text"
                                                value={quantityPerLot}
                                                readOnly
                                                className="rounded-r-none bg-muted"
                                            />
                                            <Button 
                                                type="button" 
                                                variant="outline" 
                                                className="rounded-l-none border-l-0 bg-muted"
                                                disabled
                                            >
                                                {unitOfMeasure}
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="mt-4 flex items-center gap-2 rounded-md bg-blue-50 p-3 text-blue-700">
                                <Info className="h-4 w-4" />
                                <p className="text-xs">
                                    La quantité par lot est calculée automatiquement en fonction de la taille du lot ({batchSize} unités)
                                </p>
                            </div>
                        </>
                    )}
                </TabsContent>
            </Tabs>
            
            <DialogFooter className="mt-6">
                <Button variant="outline" onClick={onCancel} disabled={loading}>Annuler</Button>
                <Button onClick={handleSubmit} disabled={loading}>
                    {loading ? (
                        <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Ajout en cours...
                        </>
                    ) : (
                        'Ajouter'
                    )}
                </Button>
            </DialogFooter>
        </div>
    );
}
