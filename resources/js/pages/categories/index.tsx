import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import AppLayout from "@/layouts/app-layout";
import { Head, Link, router, usePage } from "@inertiajs/react";
import { Activity, BarChart3, Edit, Plus, RefreshCw, TrendingUp, Trash2, Users } from "lucide-react";
import { useState, useEffect } from "react";

interface Category {
    id: number;
    name: string;
    description: string | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
}

interface Stats {
    total_categories: number;
    active_categories: number;
    inactive_categories: number;
    recent_categories: number;
}

interface RecentActivity {
    id: number;
    name: string;
    created_at: string;
}

interface Props {
    categories: {
        data: Category[];
        links: any[];
        meta: any;
    };
    stats: Stats;
    recentActivity: RecentActivity[];
    [key: string]: any;
}

export default function CategoriesIndex() {
    const { categories, stats, recentActivity } = usePage<Props>().props;
    const [isLoading, setIsLoading] = useState(false);
    const [loadingStats, setLoadingStats] = useState(false);
    const [loadingActivity, setLoadingActivity] = useState(false);

    const handleDelete = (category: Category) => {
        if (confirm(`Are you sure you want to delete "${category.name}"?`)) {
            setIsLoading(true);
            router.delete(route('categories.destroy', category.id), {
                only: ['categories', 'stats'], // Only reload categories and stats, not recentActivity
                onFinish: () => setIsLoading(false)
            });
        }
    };

    const refreshStats = () => {
        setLoadingStats(true);
        router.reload({
            only: ['stats'], // Only reload stats data
            onFinish: () => setLoadingStats(false)
        });
    };

    const refreshRecentActivity = () => {
        setLoadingActivity(true);
        router.reload({
            only: ['recentActivity'], // Only reload recent activity data
            onFinish: () => setLoadingActivity(false)
        });
    };

    const refreshAll = () => {
        setIsLoading(true);
        setLoadingStats(true);
        setLoadingActivity(true);
        router.reload({
            onFinish: () => {
                setIsLoading(false);
                setLoadingStats(false);
                setLoadingActivity(false);
            }
        });
    };

    return (
        <AppLayout>
            <Head title="Categories" />

            <div className="p-6 space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-semibold">Categories</h1>
                        <p className="text-muted-foreground">Manage your categories</p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm" onClick={refreshStats}>
                            <RefreshCw className="mr-2 h-4 w-4" />
                            Refresh Stats
                        </Button>
                        <Button variant="outline" size="sm" onClick={refreshRecentActivity}>
                            <RefreshCw className="mr-2 h-4 w-4" />
                            Refresh Activity
                        </Button>
                        <Button variant="outline" size="sm" onClick={refreshAll}>
                            <RefreshCw className="mr-2 h-4 w-4" />
                            Refresh All
                        </Button>
                        <Link href={route('categories.create')}>
                            <Button>
                                <Plus className="mr-2 h-4 w-4" />
                                Add Category
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Categories</CardTitle>
                            <BarChart3 className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            {loadingStats ? (
                                <Skeleton className="h-8 w-16" />
                            ) : (
                                <div className="text-2xl font-bold">{stats.total_categories}</div>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Active Categories</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            {loadingStats ? (
                                <Skeleton className="h-8 w-16" />
                            ) : (
                                <div className="text-2xl font-bold text-green-600">{stats.active_categories}</div>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Inactive Categories</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            {loadingStats ? (
                                <Skeleton className="h-8 w-16" />
                            ) : (
                                <div className="text-2xl font-bold text-red-600">{stats.inactive_categories}</div>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Recent (7 days)</CardTitle>
                            <Activity className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            {loadingStats ? (
                                <Skeleton className="h-8 w-16" />
                            ) : (
                                <div className="text-2xl font-bold text-blue-600">{stats.recent_categories}</div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                <div className="grid gap-6 lg:grid-cols-3">
                    {/* Recent Activity */}
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0">
                            <CardTitle>Recent Activity</CardTitle>
                            <Button variant="ghost" size="sm" onClick={refreshRecentActivity}>
                                <RefreshCw className="h-4 w-4" />
                            </Button>
                        </CardHeader>
                        <CardContent>
                            {loadingActivity ? (
                                <div className="space-y-2">
                                    {[...Array(5)].map((_, i) => (
                                        <Skeleton key={i} className="h-4 w-full" />
                                    ))}
                                </div>
                            ) : (
                                <div className="space-y-2">
                                    {recentActivity.map((activity) => (
                                        <div key={activity.id} className="flex items-center justify-between text-sm">
                                            <span className="font-medium">{activity.name}</span>
                                            <span className="text-muted-foreground">
                                                {new Date(activity.created_at).toLocaleDateString()}
                                            </span>
                                        </div>
                                    ))}
                                    {recentActivity.length === 0 && (
                                        <p className="text-muted-foreground text-sm">No recent activity</p>
                                    )}
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Categories Table */}
                    <Card className="lg:col-span-2">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0">
                            <CardTitle>All Categories</CardTitle>
                            {isLoading && (
                                <div className="flex items-center text-sm text-muted-foreground">
                                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                    Loading...
                                </div>
                            )}
                        </CardHeader>
                        <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Name</TableHead>
                                    <TableHead>Description</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Created</TableHead>
                                    <TableHead className="text-right">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {categories.data.map((category) => (
                                    <TableRow key={category.id}>
                                        <TableCell className="font-medium">
                                            {category.name}
                                        </TableCell>
                                        <TableCell>
                                            {category.description || '-'}
                                        </TableCell>
                                        <TableCell>
                                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                                category.is_active
                                                    ? 'bg-green-100 text-green-800'
                                                    : 'bg-red-100 text-red-800'
                                            }`}>
                                                {category.is_active ? 'Active' : 'Inactive'}
                                            </span>
                                        </TableCell>
                                        <TableCell>
                                            {new Date(category.created_at).toLocaleDateString()}
                                        </TableCell>
                                        <TableCell className="text-right">
                                            <div className="flex items-center justify-end gap-2">
                                                <Link href={route('categories.edit', category.id)}>
                                                    <Button variant="outline" size="sm">
                                                        <Edit className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    onClick={() => handleDelete(category)}
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>

                        {categories.data.length === 0 && (
                            <div className="text-center py-8">
                                <p className="text-muted-foreground">No categories found.</p>
                                <Link href={route('categories.create')} className="mt-2 inline-block">
                                    <Button>Create your first category</Button>
                                </Link>
                            </div>
                        )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
