import Heading from '@/components/heading';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';

import AppLayout from '@/layouts/app-layout';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { ArrowLeft, Check, Save, Shield, User, Users, X } from 'lucide-react';
import { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    user_type: 'superuser' | 'admin' | 'client';
    current_roles: string[];
    direct_permissions: string[];
    created_at: string;
    total_permissions: number;
}

interface Role {
    id: number;
    name: string;
    display_name: string;
    description: string;
    permissions_count: number;
    available_for: string[];
    assigned: boolean;
}

interface Props {
    user: User;
    roles: Role[];
}

const getUserTypeColor = (userType: string) => {
    switch (userType) {
        case 'superuser':
            return 'destructive';
        case 'admin':
            return 'default';
        case 'client':
            return 'secondary';
        default:
            return 'outline';
    }
};

export default function UserRoles({ user, roles }: Props) {
    const [selectedRoles, setSelectedRoles] = useState<string[]>(user.current_roles);
    const [hasChanges, setHasChanges] = useState(false);

    const breadcrumbItems = [
        { title: 'Dashboard', href: route('dashboard') },
        { title: 'Users', href: route('users.index') },
        { title: user.name, href: route('users.edit', user.id) },
        { title: 'Roles', href: route('users.roles', user.id) },
    ];

    const { setData, put, processing } = useForm({
        roles: user.current_roles,
    });

    const handleRoleChange = (roleName: string, checked: boolean) => {
        let newRoles: string[];

        if (checked) {
            if (roleName === 'admin' || roleName === 'super-user') {
                // Admin and super-user roles are exclusive - clear all other roles
                newRoles = [roleName];
            } else {
                // Non-admin/non-super-user role selected - remove admin and super-user roles if present
                newRoles = selectedRoles.filter((role) => role !== 'admin' && role !== 'super-user');
                newRoles.push(roleName);
            }
        } else {
            // Unchecking a role
            newRoles = selectedRoles.filter((name) => name !== roleName);
        }

        setSelectedRoles(newRoles);
        setData('roles', newRoles);
        setHasChanges(JSON.stringify(newRoles.sort()) !== JSON.stringify(user.current_roles.sort()));
    };

    const resetChanges = () => {
        setSelectedRoles(user.current_roles);
        setData('roles', user.current_roles);
        setHasChanges(false);
    };

    const handleSubmit = () => {
        put(route('users.roles.update', user.id), {
            onSuccess: () => {
                setHasChanges(false);
                router.flush(route('users.index'));
            },
        });
    };

    const getTotalPermissions = () => {
        return (
            selectedRoles.reduce((total, roleName) => {
                const role = roles.find((r) => r.name === roleName);
                return total + (role?.permissions_count || 0);
            }, 0) + user.direct_permissions.length
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbItems}>
            <div className="space-y-6 p-6">
                <Head title={`Manage Roles: ${user.name}`} />
                <div>
                    <div className="flex items-center justify-between">
                        <div>
                            <Heading title={`Manage Roles: ${user.name}`} description="Assign or remove roles for this user" />
                        </div>
                        <div className="flex space-x-2">
                            <Button variant="outline" asChild>
                                <Link href={route('users.edit', user.id)}>
                                    <User className="mr-2 h-4 w-4" />
                                    Edit Profile
                                </Link>
                            </Button>
                            <Button variant="outline" asChild>
                                <Link href={route('users.index')}>
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    Back to Users
                                </Link>
                            </Button>
                        </div>
                    </div>
                </div>

                <div className="grid gap-6 lg:grid-cols-3">
                    {/* User Information */}
                    <div className="lg:col-span-1">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Users className="mr-2 h-5 w-5" />
                                    User Information
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center space-x-3">
                                    <Avatar>
                                        <AvatarFallback>
                                            {user.name
                                                .split(' ')
                                                .map((n) => n[0])
                                                .join('')}
                                        </AvatarFallback>
                                    </Avatar>
                                    <div>
                                        <p className="font-medium">{user.name}</p>
                                        <p className="text-muted-foreground text-sm">{user.email}</p>
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label className="text-sm font-medium">User Type</Label>
                                    <Badge variant={getUserTypeColor(user.user_type)}>{user.user_type}</Badge>
                                </div>

                                <div className="space-y-2">
                                    <Label className="text-sm font-medium">Member Since</Label>
                                    <p className="text-muted-foreground text-sm">{new Date(user.created_at).toLocaleDateString()}</p>
                                </div>

                                <Separator />

                                <div className="space-y-2">
                                    <Label className="text-sm font-medium">Current Summary</Label>
                                    <div className="space-y-1 text-sm">
                                        <div className="flex justify-between">
                                            <span className="text-muted-foreground">Roles:</span>
                                            <span>{selectedRoles.length}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-muted-foreground">Total Permissions:</span>
                                            <span>{getTotalPermissions()}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-muted-foreground">Direct Permissions:</span>
                                            <span>{user.direct_permissions.length}</span>
                                        </div>
                                    </div>
                                </div>

                                <Separator />

                                <div className="space-y-2">
                                    <Button className="w-full" disabled={!hasChanges || processing} onClick={handleSubmit}>
                                        <Save className="mr-2 h-4 w-4" />
                                        Save Changes
                                    </Button>
                                    <Button variant="outline" className="w-full" onClick={resetChanges} disabled={!hasChanges}>
                                        Reset Changes
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Roles Selection */}
                    <div className="lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Available Roles</CardTitle>
                                <CardDescription>
                                    Select the roles this user should have (filtered by user type: {user.user_type})
                                    <span className="text-muted-foreground mt-2 block text-sm">
                                        Note: System roles (Admin and Super User) cannot be combined with other roles. Selecting a system role will
                                        clear other roles, and selecting other roles will clear system roles.
                                    </span>
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {roles.map((role) => {
                                    const isSelected = selectedRoles.includes(role.name);
                                    const wasOriginallySelected = user.current_roles.includes(role.name);
                                    const hasChanged = isSelected !== wasOriginallySelected;
                                    const isSystemRole = ['super-user', 'admin'].includes(role.name);

                                    return (
                                        <div
                                            key={role.id}
                                            className={`flex cursor-pointer items-start space-x-3 rounded-lg border p-4 transition-colors hover:bg-gray-50 dark:hover:bg-gray-800 ${
                                                isSelected && isSystemRole ? 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950' : ''
                                            }`}
                                            onClick={() => handleRoleChange(role.name, !isSelected)}
                                        >
                                            <Checkbox
                                                id={`role-${role.id}`}
                                                checked={isSelected}
                                                onCheckedChange={(checked) => handleRoleChange(role.name, checked as boolean)}
                                                onClick={(e) => e.stopPropagation()}
                                            />
                                            <div className="flex-1 space-y-2">
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center space-x-2">
                                                        <Label
                                                            htmlFor={`role-${role.id}`}
                                                            className={`cursor-pointer font-medium ${isSystemRole ? 'text-red-600 dark:text-blue-300' : ''}`}
                                                        >
                                                            {role.display_name}
                                                            {isSystemRole && (
                                                                <Badge variant="secondary" className="ml-2 text-xs">
                                                                    System Role
                                                                </Badge>
                                                            )}
                                                        </Label>
                                                        {hasChanged && (
                                                            <Badge variant={isSelected ? 'default' : 'destructive'} className="text-xs">
                                                                {isSelected ? (
                                                                    <>
                                                                        <Check className="mr-1 h-3 w-3" />
                                                                        Added
                                                                    </>
                                                                ) : (
                                                                    <>
                                                                        <X className="mr-1 h-3 w-3" />
                                                                        Removed
                                                                    </>
                                                                )}
                                                            </Badge>
                                                        )}
                                                    </div>
                                                    <div className="flex items-center space-x-2">
                                                        <Badge variant="outline" className="text-xs">
                                                            {role.name}
                                                        </Badge>
                                                        <Badge variant="secondary" className="text-xs">
                                                            <Shield className="mr-1 h-3 w-3" />
                                                            {role.permissions_count}
                                                        </Badge>
                                                    </div>
                                                </div>
                                                <p className="text-muted-foreground text-sm">{role.description}</p>
                                            </div>
                                        </div>
                                    );
                                })}

                                {roles.length === 0 && (
                                    <div className="py-8 text-center">
                                        <p className="text-muted-foreground">No roles available for user type: {user.user_type}</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Direct Permissions Info */}
                        {user.direct_permissions.length > 0 && (
                            <Card className="mt-6">
                                <CardHeader>
                                    <CardTitle className="text-lg">Direct Permissions</CardTitle>
                                    <CardDescription>Permissions assigned directly to this user (not through roles)</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex flex-wrap gap-2">
                                        {user.direct_permissions.map((permission) => (
                                            <Badge key={permission} variant="outline">
                                                {permission}
                                            </Badge>
                                        ))}
                                    </div>
                                    <p className="text-muted-foreground mt-2 text-xs">
                                        Direct permissions are managed separately and are not affected by role changes.
                                    </p>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
