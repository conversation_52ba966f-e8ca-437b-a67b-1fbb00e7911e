import Heading from '@/components/heading';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm, router } from '@inertiajs/react';
import { ArrowLeft, Calendar, Save, Shield, User, UserCheck } from 'lucide-react';
import { FormEvent } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    user_type: string;
    roles: string[];
    direct_permissions: string[];
    permissions_count: number;
    created_at: string;
    updated_at: string;
}

interface Props {
    user: User;
}

const getBreadcrumbItems = (user: User) => [
    { title: 'Dashboard', href: route('dashboard') },
    { title: 'Users', href: route('users.index') },
    { title: user.name, href: route('users.edit', user.id) },
];

const getUserTypeColor = (userType: string) => {
    switch (userType) {
        case 'superuser':
            return 'destructive';
        case 'admin':
            return 'default';
        case 'client':
            return 'secondary';
        default:
            return 'outline';
    }
};

export default function EditUser({ user }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        name: user.name,
        email: user.email,
    });

    const handleSave = (e: FormEvent) => {
        e.preventDefault();
        put(route('users.update', user.id), {
            onSuccess: () => {
                router.flush(route('users.index'));
            }
        });
    };

    return (
        <AppLayout breadcrumbs={getBreadcrumbItems(user)}>
            <div className="space-y-6 p-6">
                <Head title={`Edit User: ${user.name}`} />
                <div>
                    <div className="flex items-center justify-between">
                        <div>
                            <Heading title={`Edit User: ${user.name}`} description="Modify user information and manage their access" />
                        </div>
                        <div className="flex space-x-2">
                            <Button variant="outline" asChild>
                                <Link href={route('users.index')}>
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    Back to Users
                                </Link>
                            </Button>
                            <Button asChild>
                                <Link href={route('users.roles', user.id)}>
                                    <UserCheck className="mr-2 h-4 w-4" />
                                    Manage Roles
                                </Link>
                            </Button>
                        </div>
                    </div>
                </div>

                <Card>
                    <CardHeader>
                        <div>
                            <CardTitle className="flex items-center">
                                <User className="mr-2 h-5 w-5" />
                                User Information
                            </CardTitle>
                            <CardDescription>Manage basic user information (roles are managed separately)</CardDescription>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
                            {/* Left Column - Edit Form */}
                            <div className="space-y-6">
                                <div className="space-y-6">
                                    <div className="flex flex-col space-y-1">
                                        <Label htmlFor="name">Full Name</Label>
                                        <Input
                                            id="name"
                                            placeholder="User's full name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            className={errors.name ? 'border-destructive' : ''}
                                        />
                                        {errors.name && <p className="text-destructive text-xs">{errors.name}</p>}
                                    </div>

                                    <div className="flex flex-col space-y-1">
                                        <Label htmlFor="email">Email Address</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            placeholder="Email address"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            className={errors.email ? 'border-destructive' : ''}
                                        />
                                        {errors.email && <p className="text-destructive text-xs">{errors.email}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label className="text-sm font-medium">User Type</Label>
                                        <div className="flex items-center space-x-2">
                                            <Badge variant={getUserTypeColor(user.user_type)} className="px-3 py-1">
                                                {user.user_type}
                                            </Badge>
                                            <span className="text-muted-foreground text-sm">
                                                (managed via roles)
                                            </span>
                                        </div>
                                        <p className="text-muted-foreground text-xs">
                                            User type is automatically updated based on role assignments. Use "Manage Roles" to change user permissions.
                                        </p>
                                    </div>

                                    <div className="pt-4">
                                        <Button variant="default" onClick={handleSave} disabled={processing}>
                                            <Save className="mr-2 h-4 w-4" />
                                            Save Changes
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            {/* Right Column - User Info */}
                            <div className="space-y-8">
                                {/* User Profile Section */}
                                <div className="flex items-center space-x-6">
                                    <Avatar className="h-20 w-20">
                                        <AvatarFallback className="text-xl font-semibold">{user.name.charAt(0)}</AvatarFallback>
                                    </Avatar>
                                    <div className="flex-1">
                                        <h3 className="text-xl font-semibold">{user.name}</h3>
                                        <p className="text-muted-foreground mt-1 text-base">{user.email}</p>
                                        <div className="mt-3">
                                            <Badge variant={getUserTypeColor(user.user_type)} className="px-3 py-1 text-sm">
                                                {user.user_type}
                                            </Badge>
                                        </div>
                                    </div>
                                </div>

                                {/* Timestamps Section */}
                                <div className="border-t pt-6 md:border-t-0 md:pt-0">
                                    <div className="grid grid-cols-2 gap-8">
                                        <div className="space-y-2">
                                            <p className="text-muted-foreground text-sm font-medium">Created</p>
                                            <p className="flex items-center text-base">
                                                <Calendar className="text-muted-foreground mr-2 h-4 w-4" />
                                                {user.created_at}
                                            </p>
                                        </div>
                                        <div className="space-y-2">
                                            <p className="text-muted-foreground text-sm font-medium">Last Updated</p>
                                            <p className="flex items-center text-base">
                                                <Calendar className="text-muted-foreground mr-2 h-4 w-4" />
                                                {user.updated_at}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/* Statistics Section */}
                                <div className="border-t pt-6">
                                    <h4 className="mb-4 text-lg font-semibold">Statistics</h4>
                                    <div className="grid grid-cols-1 gap-3 sm:grid-cols-3">
                                        <div className="bg-muted/30 rounded-lg p-4 text-center">
                                            <div className="text-primary text-2xl font-bold">{user.permissions_count}</div>
                                            <p className="text-muted-foreground mt-1 text-sm">Total Permissions</p>
                                        </div>
                                        <div className="bg-muted/30 rounded-lg p-4 text-center">
                                            <div className="text-primary text-2xl font-bold">{user.roles.length}</div>
                                            <p className="text-muted-foreground mt-1 text-sm">Assigned Roles</p>
                                        </div>
                                        <div className="bg-muted/30 rounded-lg p-4 text-center">
                                            <div className="text-primary text-2xl font-bold">{user.direct_permissions.length}</div>
                                            <p className="text-muted-foreground mt-1 text-sm">Direct Permissions</p>
                                        </div>
                                    </div>
                                </div>

                                {/* Roles & Permissions Section */}
                                <div className="border-t pt-6">
                                    <div className="mb-4">
                                        <h4 className="flex items-center text-lg font-semibold">
                                            <Shield className="mr-2 h-5 w-5" />
                                            Current Roles & Permissions
                                        </h4>
                                    </div>
                                    <div className="max-h-[300px] space-y-2 overflow-y-auto pr-2">
                                        {user.roles.map((role: string) => (
                                            <div key={role} className="bg-background flex items-center justify-between rounded-lg border p-3">
                                                <span className="font-medium">{role}</span>
                                                <Badge variant="outline">Role</Badge>
                                            </div>
                                        ))}
                                        {user.direct_permissions.map((permission: string) => (
                                            <div key={permission} className="bg-background flex items-center justify-between rounded-lg border p-3">
                                                <span className="font-medium">{permission}</span>
                                                <Badge variant="secondary">Direct</Badge>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
