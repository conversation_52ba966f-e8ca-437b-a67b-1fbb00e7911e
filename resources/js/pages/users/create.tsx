import Heading from '@/components/heading';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { type SharedData } from '@/types';
import { Head, Link, router, useForm, usePage } from '@inertiajs/react';
import { AlertTriangle, ArrowLeft, Save, Shield, User } from 'lucide-react';
import { FormEvent } from 'react';

interface Role {
    id: number;
    name: string;
    permissions_count: number;
    available_for: string[];
}

interface Props {
    roles: Role[];
}

const breadcrumbItems = [
    { title: 'Dashboard', href: route('dashboard') },
    { title: 'Users', href: route('users.index') },
    { title: 'Create User', href: route('users.create') },
];

const getUserTypeColor = (userType: string) => {
    switch (userType) {
        case 'superuser':
            return 'destructive';
        case 'admin':
            return 'default';
        case 'client':
            return 'secondary';
        default:
            return 'outline';
    }
};

const formatRoleName = (name: string): string => {
    return name
        .split('-')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
};

export default function CreateUser({ roles }: Props) {
    const { auth } = usePage<SharedData>().props;
    const currentUserType = auth.user?.user_type || 'client';

    const getAvailableUserTypes = (): string[] => {
        if (currentUserType === 'superuser') {
            return ['superuser', 'admin', 'client'];
        } else if (currentUserType === 'admin') {
            return ['admin', 'client'];
        } else {
            return ['client'];
        }
    };

    const availableUserTypes = getAvailableUserTypes();
    const defaultUserType = availableUserTypes.includes('client') ? 'client' : availableUserTypes[0];

    // Auto-select initial roles based on user type and permissions
    const getInitialRoles = (userType: string): string[] => {
        const availableRoles = roles.filter((role) => role.available_for.includes(userType));

        if (userType === 'superuser') {
            const superuserRole = availableRoles.find((role) => role.name === 'super-user');
            return superuserRole ? ['super-user'] : [];
        } else if (userType === 'admin') {
            const adminRole = availableRoles.find((role) => role.name === 'admin');
            return adminRole ? ['admin'] : [];
        } else if (availableRoles.length === 1) {
            return [availableRoles[0].name];
        }

        return [];
    };

    const initialRoles = getInitialRoles(defaultUserType);

    const { data, setData, post, processing, errors } = useForm({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        user_type: defaultUserType,
        roles: initialRoles,
    });

    const availableRoles = roles.filter((role) => {
        if (data.user_type === 'client') {
            const systemRoles = ['super-user', 'admin'];
            return role.available_for.includes(data.user_type) && !systemRoles.includes(role.name);
        }
        return role.available_for.includes(data.user_type);
    });

    const handleUserTypeChange = (userType: string) => {
        if (!availableUserTypes.includes(userType)) {
            return;
        }

        setData('user_type', userType);

        const newAvailableRoles = roles.filter((role) => {
            if (userType === 'client') {
                const systemRoles = ['super-user', 'admin'];
                return role.available_for.includes(userType) && !systemRoles.includes(role.name);
            }
            return role.available_for.includes(userType);
        });

        let selectedRoles: string[] = [];

        if (userType === 'superuser') {
            const superuserRole = newAvailableRoles.find((role) => role.name === 'super-user');
            if (superuserRole) {
                selectedRoles = ['super-user'];
            }
        } else if (userType === 'admin') {
            const adminRole = newAvailableRoles.find((role) => role.name === 'admin');
            if (adminRole) {
                selectedRoles = ['admin'];
            }
        } else if (newAvailableRoles.length === 1) {
            selectedRoles = [newAvailableRoles[0].name];
        }

        setData('roles', selectedRoles);
    };

    const handleRoleChange = (roleName: string, checked: boolean) => {
        setData('roles', checked ? [...data.roles, roleName] : data.roles.filter((name: string) => name !== roleName));
    };

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();

        if (data.user_type === 'superuser' && !data.roles.includes('super-user')) {
            setData('roles', ['super-user']);
        }

        if (data.user_type === 'admin' && !data.roles.includes('admin')) {
            setData('roles', ['admin']);
        }

        post(route('users.store'), {
            onSuccess: () => {
                router.flush(route('users.index'));
            },
            onError: (errors) => {
                console.error('Form submission errors:', errors);
            },
        });
    };

    const getTotalPermissions = () => {
        return data.roles.reduce((total, roleName) => {
            const role = roles.find((r) => r.name === roleName);
            return total + (role?.permissions_count || 0);
        }, 0);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbItems}>
            <div className="space-y-6 p-6">
                <Head title="Create User" />
                <div>
                    <div className="flex items-center justify-between">
                        <div>
                            <Heading title="Create New User" description="Add a new user to the system with appropriate roles" />
                        </div>
                        <Button variant="outline" asChild>
                            <Link href={route('users.index')}>
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Users
                            </Link>
                        </Button>
                    </div>
                </div>

                <div className="grid gap-6 lg:grid-cols-3">
                    <div className="lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <User className="mr-2 h-5 w-5" />
                                    User Information
                                </CardTitle>
                                <CardDescription>Basic information about the new user</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid gap-4 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="name">Full Name *</Label>
                                        <Input
                                            id="name"
                                            placeholder="Enter user's full name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            className={errors.name ? 'border-destructive' : ''}
                                        />
                                        {errors.name && <p className="text-destructive mt-1 text-sm">{errors.name}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="email">Email Address *</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            placeholder="<EMAIL>"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            className={errors.email ? 'border-destructive' : ''}
                                        />
                                        {errors.email && <p className="text-destructive mt-1 text-sm">{errors.email}</p>}
                                    </div>
                                </div>

                                <div className="grid gap-4 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="password">Password *</Label>
                                        <Input
                                            id="password"
                                            type="password"
                                            placeholder="••••••••"
                                            value={data.password}
                                            onChange={(e) => setData('password', e.target.value)}
                                            className={errors.password ? 'border-destructive' : ''}
                                        />
                                        {errors.password && <p className="text-destructive mt-1 text-sm">{errors.password}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="password_confirmation">Confirm Password *</Label>
                                        <Input
                                            id="password_confirmation"
                                            type="password"
                                            placeholder="••••••••"
                                            value={data.password_confirmation}
                                            onChange={(e) => setData('password_confirmation', e.target.value)}
                                            className={errors.password_confirmation ? 'border-destructive' : ''}
                                        />
                                        {errors.password_confirmation && (
                                            <p className="text-destructive mt-1 text-sm">{errors.password_confirmation}</p>
                                        )}
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="user_type">User Type *</Label>
                                    <Select value={data.user_type} onValueChange={handleUserTypeChange}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select user type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {availableUserTypes.includes('superuser') && <SelectItem value="superuser">Superuser</SelectItem>}
                                            {availableUserTypes.includes('admin') && <SelectItem value="admin">Admin</SelectItem>}
                                            {availableUserTypes.includes('client') && <SelectItem value="client">Client</SelectItem>}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Shield className="mr-2 h-5 w-5" />
                                    Role Assignment
                                </CardTitle>
                                <CardDescription>
                                    {data.user_type === 'superuser'
                                        ? `Super-user role automatically selected for superuser user type`
                                        : data.user_type === 'admin'
                                          ? `Admin role automatically selected for admin user type`
                                          : availableRoles.length === 1
                                            ? `Role automatically assigned for ${data.user_type} user type`
                                            : `Select roles for this user (filtered by user type: ${data.user_type})`}
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                {data.user_type === 'superuser'
                                    ? // For superuser users, only show the super-user role
                                      (() => {
                                          const superuserRole = roles.find((role) => role.name === 'super-user');
                                          if (!superuserRole) return null;

                                          return (
                                              <div className="bg-muted/30 flex items-center space-x-3 rounded-md border p-3">
                                                  <Checkbox id="role-superuser" checked={true} disabled={true} />
                                                  <div className="flex-1">
                                                      <div className="flex items-center justify-between">
                                                          <div className="flex items-center space-x-2">
                                                              <Label htmlFor="role-superuser" className="text-muted-foreground font-medium">
                                                                  Super User
                                                              </Label>
                                                              <Badge variant="destructive" className="text-xs">
                                                                  required
                                                              </Badge>
                                                          </div>
                                                          <Badge variant="outline" className="text-xs">
                                                              {superuserRole.permissions_count} permissions
                                                          </Badge>
                                                      </div>
                                                  </div>
                                              </div>
                                          );
                                      })()
                                    : data.user_type === 'admin'
                                      ? // For admin users, only show the admin role
                                        (() => {
                                            const adminRole = roles.find((role) => role.name === 'admin');
                                            if (!adminRole) return null;

                                            return (
                                                <div className="bg-muted/30 flex items-center space-x-3 rounded-md border p-3">
                                                    <Checkbox id="role-admin" checked={true} disabled={true} />
                                                    <div className="flex-1">
                                                        <div className="flex items-center justify-between">
                                                            <div className="flex items-center space-x-2">
                                                                <Label htmlFor="role-admin" className="text-muted-foreground font-medium">
                                                                    Admin
                                                                </Label>
                                                                <Badge variant="secondary" className="text-xs">
                                                                    required
                                                                </Badge>
                                                            </div>
                                                            <Badge variant="outline" className="text-xs">
                                                                {adminRole.permissions_count} permissions
                                                            </Badge>
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })()
                                    : // For non-admin users, show available roles
                                      availableRoles.map((role) => {
                                          const isAutoSelected = availableRoles.length === 1 && data.roles.includes(role.name);

                                          return (
                                              <div
                                                  key={role.id}
                                                  className={`flex items-center space-x-3 rounded-md border p-3 ${
                                                      isAutoSelected ? 'bg-muted/30' : 'hover:bg-muted/50'
                                                  }`}
                                              >
                                                  <Checkbox
                                                      id={`role-${role.id}`}
                                                      checked={data.roles.includes(role.name)}
                                                      disabled={isAutoSelected}
                                                      onCheckedChange={(checked) => handleRoleChange(role.name, checked as boolean)}
                                                  />
                                                  <div className="flex-1">
                                                      <div className="flex items-center justify-between">
                                                          <div className="flex items-center space-x-2">
                                                              <Label
                                                                  htmlFor={`role-${role.id}`}
                                                                  className={`font-medium ${
                                                                      isAutoSelected ? 'text-muted-foreground' : 'cursor-pointer'
                                                                  }`}
                                                              >
                                                                  {formatRoleName(role.name)}
                                                              </Label>
                                                              {isAutoSelected && (
                                                                  <Badge variant="secondary" className="text-xs">
                                                                      auto-assigned
                                                                  </Badge>
                                                              )}
                                                          </div>
                                                          <Badge variant="outline" className="text-xs">
                                                              {role.permissions_count} permissions
                                                          </Badge>
                                                      </div>
                                                  </div>
                                              </div>
                                          );
                                      })}

                                {availableRoles.length === 0 && !['superuser', 'admin'].includes(data.user_type) && (
                                    <div className="py-8 text-center">
                                        <AlertTriangle className="text-muted-foreground mx-auto mb-2 h-8 w-8" />
                                        <p className="text-muted-foreground font-medium">No roles available</p>
                                        <p className="text-muted-foreground mt-1 text-sm">No roles are available for user type: {data.user_type}</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Summary</CardTitle>
                                <CardDescription>User creation summary and permissions</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label className="text-sm font-medium">User Type</Label>
                                    <Badge variant={getUserTypeColor(data.user_type)}>{data.user_type}</Badge>
                                </div>

                                <div className="space-y-2">
                                    <Label className="text-sm font-medium">Selected Roles</Label>
                                    <div className="text-sm">
                                        {data.roles.length > 0 ? (
                                            <div className="flex flex-wrap gap-1">
                                                {data.roles.map((roleName: string) => {
                                                    const role = roles.find((r) => r.name === roleName);
                                                    const isSystemRole = ['super-user', 'admin'].includes(roleName);
                                                    return (
                                                        <Badge key={roleName} variant={isSystemRole ? 'destructive' : 'outline'} className="text-xs">
                                                            {formatRoleName(roleName)}
                                                            {role && <span className="ml-1 text-xs opacity-70">({role.permissions_count})</span>}
                                                        </Badge>
                                                    );
                                                })}
                                            </div>
                                        ) : (
                                            <span className="text-muted-foreground">No roles selected</span>
                                        )}
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <div className="flex justify-between text-sm">
                                        <span className="text-muted-foreground">Total Permissions:</span>
                                        <span className="font-medium">{getTotalPermissions()}</span>
                                    </div>
                                </div>

                                <Separator />

                                <Button
                                    className="w-full"
                                    onClick={handleSubmit}
                                    disabled={
                                        processing ||
                                        !data.name.trim() ||
                                        !data.email.trim() ||
                                        !data.password.trim() ||
                                        !data.password_confirmation.trim() ||
                                        (data.roles.length === 0 && !['superuser', 'admin'].includes(data.user_type))
                                    }
                                >
                                    <Save className="mr-2 h-4 w-4" />
                                    {processing ? 'Creating User...' : 'Create User'}
                                </Button>

                                {data.roles.length === 0 && !['superuser', 'admin'].includes(data.user_type) && (
                                    <p className="text-muted-foreground text-center text-xs">Please select at least one role to create the user</p>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
