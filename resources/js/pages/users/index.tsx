import Heading from '@/components/heading';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { PERMISSIONS } from '@/constants/permissions';
import AppLayout from '@/layouts/app-layout';
import { type SharedData } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Edit, Plus, Shield, Trash2, UserCheck, User as UserIcon } from 'lucide-react';

interface User {
    id: number;
    name: string;
    email: string;
    user_type: 'superuser' | 'admin' | 'client';
    roles: string[];
    permissions_count: number;
    created_at: string;
    can_edit: boolean;
    can_delete: boolean;
}

interface Stats {
    total_users: number;
    superusers: number;
    admins: number;
    clients: number;
}

interface Props {
    users: User[];
    stats: Stats;
}

const breadcrumbItems = [
    { title: 'Dashboard', href: route('dashboard') },
    { title: 'Users', href: route('users.index') },
];

const getUserTypeColor = (userType: string) => {
    switch (userType) {
        case 'superuser':
            return 'destructive';
        case 'admin':
            return 'default';
        case 'client':
            return 'secondary';
        default:
            return 'outline';
    }
};

export default function UsersIndex({ users, stats }: Props) {
    const { auth } = usePage<SharedData>().props;
    const canCreateUser = auth.permissions.includes(PERMISSIONS.CREATE_USER);
    const isSuperuser = auth.user?.user_type === 'superuser';
    const currentUserId = auth.user?.id;

    // Helper function to determine if roles button should be shown
    const shouldShowRolesButton = (user: User): boolean => {
        if (!user.can_edit) return false;

        if (user.id !== currentUserId) return true;
        // This prevents any user from managing their own roles (self-privilege escalation)
        return false;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbItems}>
            <div className="space-y-6 p-6">
                <Head title="Users Management" />
                <div>
                    <div className="flex items-center justify-between">
                        <div>
                            <Heading title="Users Management" description="Manage users and their role assignments" />
                        </div>
                        {canCreateUser && (
                            <Button asChild>
                                <Link href={route('users.create')}>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Create User
                                </Link>
                            </Button>
                        )}
                    </div>
                </div>

                {/* Summary Stats */}
                <div className={`grid gap-4 ${isSuperuser ? 'md:grid-cols-4' : 'md:grid-cols-3'}`}>
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_users}</div>
                        </CardContent>
                    </Card>
                    {isSuperuser && (
                        <Card>
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium">Superusers</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.superusers}</div>
                            </CardContent>
                        </Card>
                    )}
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Admins</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.admins}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Clients</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.clients}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Users Table */}
                <Card>
                    <CardContent className="p-0">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>User</TableHead>
                                    <TableHead>Email</TableHead>
                                    <TableHead>Type</TableHead>
                                    <TableHead>Roles</TableHead>
                                    <TableHead>Permissions</TableHead>
                                    <TableHead className="w-[120px] text-center">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {users.map((user, index) => {
                                    const isCurrentUser = user.id === currentUserId;
                                    return (
                                        <TableRow
                                            key={user.id}
                                            className={
                                                isCurrentUser
                                                    ? 'bg-blue-50 border-l-4 border-l-blue-500 dark:bg-blue-950/20 dark:border-l-blue-400'
                                                    : index % 2 === 1
                                                    ? 'bg-muted/50'
                                                    : ''
                                            }
                                        >
                                            <TableCell>
                                                <div className="flex items-center space-x-3">
                                                    <Avatar>
                                                        <AvatarFallback>
                                                            {user.name
                                                                .split(' ')
                                                                .map((n) => n[0])
                                                                .join('')}
                                                        </AvatarFallback>
                                                    </Avatar>
                                                    <div>
                                                        <div className="flex items-center space-x-2">
                                                            <span className="font-medium">{user.name}</span>
                                                            {isCurrentUser && (
                                                                <div className="flex items-center space-x-1">
                                                                    <UserIcon className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                                                                    <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">You</span>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                            </TableCell>
                                                <TableCell>
                                                    <div className="text-muted-foreground text-sm">{user.email}</div>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge variant={getUserTypeColor(user.user_type)}>{user.user_type}</Badge>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center space-x-1">
                                                        <Shield className="text-muted-foreground h-3 w-3" />
                                                        <span className="text-muted-foreground text-sm">{user.roles.join(', ')}</span>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="text-muted-foreground text-sm">{user.permissions_count} permissions</div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center justify-center space-x-2">
                                                        {shouldShowRolesButton(user) && (
                                                            <Button variant="outline" size="sm" asChild>
                                                                <Link href={route('users.roles', user.id)}>
                                                                    <UserCheck className="mr-1 h-4 w-4" />
                                                                    Roles
                                                                </Link>
                                                            </Button>
                                                        )}
                                                        {user.can_edit && (
                                                            <Button variant="outline" size="sm" asChild>
                                                                <Link href={route('users.edit', user.id)}>
                                                                    <Edit className="mr-1 h-4 w-4" />
                                                                    Edit
                                                                </Link>
                                                            </Button>
                                                        )}
                                                        {user.can_delete && (
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                className="text-destructive border-destructive hover:bg-destructive hover:text-white"
                                                                onClick={() => {
                                                                    if (confirm(`Are you sure you want to delete ${user.name}?`)) {
                                                                        router.delete(route('users.destroy', user.id), {
                                                                            onSuccess: () => {
                                                                                router.flush(route('users.index'));
                                                                            },
                                                                        });
                                                                    }
                                                                }}
                                                            >
                                                                <Trash2 className="mr-1 h-4 w-4" />
                                                                Delete
                                                            </Button>
                                                        )}

                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                    );
                                })}
                            </TableBody>
                        </Table>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
