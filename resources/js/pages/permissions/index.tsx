import Heading from '@/components/heading';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';
import { Search, Shield, Users } from 'lucide-react';
import { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    user_type: string;
}

interface Role {
    id: number;
    name: string;
    display_name: string;
}

interface Permission {
    id: number;
    name: string;
    display_name: string;
    description: string;
    roles_count: number;
    users_count: number;
    created_at: string;
    roles: Role[];
    users: User[];
}

interface PermissionCategory {
    category: string;
    permissions: Permission[];
}

interface Stats {
    total_permissions: number;
    total_categories: number;
    total_role_assignments: number;
    total_user_assignments: number;
}

interface Props {
    permissions: PermissionCategory[];
    stats: Stats;
}

const breadcrumbItems = [
    { title: 'Dashboard', href: route('dashboard') },
    { title: 'Permissions', href: route('permissions.index') },
];

export default function PermissionsIndex({ permissions, stats }: Props) {
    const [searchTerm, setSearchTerm] = useState('');

    const allPermissions = permissions.flatMap((category) =>
        category.permissions.map((permission) => ({ ...permission, category: category.category })),
    );

    // Filter permissions based on search term
    const filteredPermissions = allPermissions.filter(
        (permission) =>
            permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            permission.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            permission.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
            permission.category.toLowerCase().includes(searchTerm.toLowerCase()),
    );

    // Group filtered permissions back by category for display
    const groupedFilteredPermissions = filteredPermissions.reduce(
        (acc, permission) => {
            if (!acc[permission.category]) {
                acc[permission.category] = [];
            }
            acc[permission.category].push(permission);
            return acc;
        },
        {} as Record<string, (Permission & { category: string })[]>,
    );

    return (
        <AppLayout breadcrumbs={breadcrumbItems}>
            <div className="space-y-6 p-6">
                <Head title="Permissions Management" />
                <div>
                    <Heading
                        title="Permissions Management"
                        description="View system permissions and their usage across roles and users. Permissions are managed by developers through seeders and migrations."
                    />
                </div>

                {/* Search */}
                <Card>
                    <CardContent className="p-4">
                        <div className="relative">
                            <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
                            <Input
                                placeholder="Search permissions by name, description, or category..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="pl-10"
                            />
                        </div>
                    </CardContent>
                </Card>

                {/* Summary Stats */}
                <div className="grid gap-4 md:grid-cols-4">
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Total Permissions</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_permissions}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Categories</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_categories}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Role Assignments</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_role_assignments}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">User Assignments</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_user_assignments}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Permissions Tables */}
                <div className="space-y-6">
                    {Object.entries(groupedFilteredPermissions).map(([category, permissions]) => (
                        <Card key={category}>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Shield className="mr-2 h-5 w-5" />
                                    {category}
                                </CardTitle>
                                <div className="text-muted-foreground text-sm">
                                    {permissions.length} permission{permissions.length !== 1 ? 's' : ''} in this category
                                </div>
                            </CardHeader>
                            <CardContent className="p-0">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Permission</TableHead>
                                            <TableHead>Description</TableHead>
                                            <TableHead>Roles</TableHead>
                                            <TableHead>Users</TableHead>
                                            <TableHead>Created</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {permissions.map((permission, index) => (
                                            <TableRow key={permission.id} className={index % 2 === 0 ? 'bg-muted/50' : ''}>
                                                <TableCell>
                                                    <Badge variant="secondary" className="bg-black text-xs text-white">
                                                        {permission.name}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="text-muted-foreground max-w-xs truncate text-sm">{permission.description}</div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center space-x-2">
                                                        <Shield className="text-muted-foreground h-4 w-4" />
                                                        <Badge variant="secondary">{permission.roles_count}</Badge>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center space-x-2">
                                                        <Users className="text-muted-foreground h-4 w-4" />
                                                        <Badge variant="outline">{permission.users_count}</Badge>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="text-muted-foreground text-sm">
                                                        {new Date(permission.created_at).toLocaleDateString()}
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* No results */}
                {Object.keys(groupedFilteredPermissions).length === 0 && searchTerm && (
                    <Card>
                        <CardContent className="py-8 text-center">
                            <p className="text-muted-foreground">No permissions found matching "{searchTerm}"</p>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
