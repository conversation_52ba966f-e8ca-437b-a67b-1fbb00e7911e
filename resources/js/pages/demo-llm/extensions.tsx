import StarterKit from '@tiptap/starter-kit';
import CharacterCount from '@tiptap/extension-character-count';
import Underline from '@tiptap/extension-underline';
import Highlight from '@tiptap/extension-highlight';
import TextStyle from '@tiptap/extension-text-style';
import Color from '@tiptap/extension-color';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import Youtube from '@tiptap/extension-youtube';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableHeader from '@tiptap/extension-table-header';
import TableCell from '@tiptap/extension-table-cell';
import TextAlign from '@tiptap/extension-text-align';
import FontFamily from '@tiptap/extension-font-family';
import Placeholder from '@tiptap/extension-placeholder';
import TaskList from '@tiptap/extension-task-list';
import TaskItem from '@tiptap/extension-task-item';

export const createTipTapExtensions = (placeholder?: string) => [
    StarterKit,
    CharacterCount,
    // Text Formatting Extensions
    Underline,
    Highlight.configure({ multicolor: true }),
    TextStyle,
    Color.configure({ types: [TextStyle.name] }),
    // Rich Content Extensions
    Image.configure({
        inline: true,
        allowBase64: true,
    }),
    Link.configure({
        openOnClick: false,
        autolink: true,
        defaultProtocol: 'https',
    }),
    Youtube.configure({
        controls: false,
        nocookie: true,
    }),
    Table.configure({
        resizable: true,
    }),
    TableRow,
    TableHeader,
    TableCell,
    // Advanced Features
    TextAlign.configure({
        types: ['heading', 'paragraph'],
    }),
    FontFamily.configure({
        types: ['textStyle'],
    }),
    Placeholder.configure({
        placeholder: placeholder || 'Start typing your content here...',
    }),
    TaskList,
    TaskItem,
];

export const defaultExtensions = createTipTapExtensions();
