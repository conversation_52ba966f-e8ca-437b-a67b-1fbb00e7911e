import { Editor } from '@tiptap/react';

export interface TipTapEditorProps {
    editor?: Editor | null;
    content?: string;
    placeholder?: string;
    className?: string;
    editorClassName?: string;
    showToolbar?: boolean;
    showCharacterCount?: boolean;
    onUpdate?: (content: string) => void;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    extensions?: any[];
}

export interface ToolbarProps {
    editor: Editor | null;
    showCharacterCount?: boolean;
}

export interface HtmlLoaderProps {
    onSplitContent?: (sectionsData: DynamicSectionsResponse) => void;
    className?: string;
}

// Dynamic Sections Types
export interface SectionContent {
    title: string;
    html: string;
}

export interface DynamicSectionsResponse {
    totalSections: number;
    sections: {
        [key: string]: SectionContent; // section1, section2, section3, etc.
    };
}

export interface SectionEditorState {
    id: string;
    title: string;
    content: string;
    editor: Editor | null;
}
