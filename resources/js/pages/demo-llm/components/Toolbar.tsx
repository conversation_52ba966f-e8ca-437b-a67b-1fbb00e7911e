import React from 'react';
import { ToolbarProps } from '../types';

export const Toolbar: React.FC<ToolbarProps> = ({ editor, showCharacterCount = true }) => {
    if (!editor) return null;

    const buttonClass = (isActive: boolean) =>
        `px-3 py-1 rounded text-sm font-medium ${
            isActive
                ? 'bg-blue-500 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-100'
        } border border-gray-300`;

    const handleImageUpload = () => {
        const url = window.prompt('Enter image URL:');
        if (url) {
            editor.chain().focus().setImage({ src: url }).run();
        }
    };

    const handleLinkAdd = () => {
        const url = window.prompt('Enter link URL:');
        if (url) {
            editor.chain().focus().setLink({ href: url }).run();
        }
    };

    const handleYouTubeAdd = () => {
        const url = window.prompt('Enter YouTube URL:');
        if (url) {
            editor.chain().focus().setYoutubeVideo({ src: url }).run();
        }
    };

    return (
        <div className="bg-gray-50 border-b border-gray-300 p-3 flex flex-wrap gap-2">
            {/* Text Formatting */}
            <button
                onClick={() => editor.chain().focus().toggleBold().run()}
                disabled={!editor}
                className={buttonClass(editor.isActive('bold'))}
            >
                Bold
            </button>
            <button
                onClick={() => editor.chain().focus().toggleItalic().run()}
                disabled={!editor}
                className={buttonClass(editor.isActive('italic'))}
            >
                Italic
            </button>
            <button
                onClick={() => editor.chain().focus().toggleStrike().run()}
                disabled={!editor}
                className={buttonClass(editor.isActive('strike'))}
            >
                Strike
            </button>
            <button
                onClick={() => editor.chain().focus().toggleCode().run()}
                disabled={!editor}
                className={buttonClass(editor.isActive('code'))}
            >
                Code
            </button>
            <button
                onClick={() => editor.chain().focus().toggleUnderline().run()}
                disabled={!editor}
                className={buttonClass(editor.isActive('underline'))}
            >
                Underline
            </button>
            <button
                onClick={() => editor.chain().focus().toggleHighlight().run()}
                disabled={!editor}
                className={buttonClass(editor.isActive('highlight'))}
            >
                Highlight
            </button>

            <div className="w-px bg-gray-300 mx-1"></div>

            {/* Headings */}
            <button
                onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
                disabled={!editor}
                className={buttonClass(editor.isActive('heading', { level: 1 }))}
            >
                H1
            </button>
            <button
                onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
                disabled={!editor}
                className={buttonClass(editor.isActive('heading', { level: 2 }))}
            >
                H2
            </button>
            <button
                onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
                disabled={!editor}
                className={buttonClass(editor.isActive('heading', { level: 3 }))}
            >
                H3
            </button>

            {/* Lists */}
            <button
                onClick={() => editor.chain().focus().toggleBulletList().run()}
                disabled={!editor}
                className={buttonClass(editor.isActive('bulletList'))}
            >
                Bullet List
            </button>
            <button
                onClick={() => editor.chain().focus().toggleOrderedList().run()}
                disabled={!editor}
                className={buttonClass(editor.isActive('orderedList'))}
            >
                Ordered List
            </button>
            <button
                onClick={() => editor.chain().focus().toggleBlockquote().run()}
                disabled={!editor}
                className={buttonClass(editor.isActive('blockquote'))}
            >
                Quote
            </button>
            <button
                onClick={() => editor.chain().focus().setHorizontalRule().run()}
                disabled={!editor}
                className="px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100 border border-gray-300"
            >
                Horizontal Rule
            </button>

            <div className="w-px bg-gray-300 mx-1"></div>

            {/* Text Alignment */}
            <button
                onClick={() => editor.chain().focus().setTextAlign('left').run()}
                disabled={!editor}
                className={buttonClass(editor.isActive({ textAlign: 'left' }))}
            >
                Left
            </button>
            <button
                onClick={() => editor.chain().focus().setTextAlign('center').run()}
                disabled={!editor}
                className={buttonClass(editor.isActive({ textAlign: 'center' }))}
            >
                Center
            </button>
            <button
                onClick={() => editor.chain().focus().setTextAlign('right').run()}
                disabled={!editor}
                className={buttonClass(editor.isActive({ textAlign: 'right' }))}
            >
                Right
            </button>

            <div className="w-px bg-gray-300 mx-1"></div>

            {/* Advanced Features */}
            <button
                onClick={() => editor.chain().focus().toggleTaskList().run()}
                disabled={!editor}
                className={buttonClass(editor.isActive('taskList'))}
            >
                Task List
            </button>
            <button
                onClick={handleImageUpload}
                disabled={!editor}
                className="px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100 border border-gray-300"
            >
                Image
            </button>
            <button
                onClick={handleLinkAdd}
                disabled={!editor}
                className={buttonClass(editor.isActive('link'))}
            >
                Link
            </button>
            <button
                onClick={handleYouTubeAdd}
                disabled={!editor}
                className="px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100 border border-gray-300"
            >
                YouTube
            </button>
            <button
                onClick={() => editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()}
                disabled={!editor}
                className="px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100 border border-gray-300"
            >
                Table
            </button>

            {/* Character Count */}
            {showCharacterCount && (
                <div className="ml-auto flex items-center gap-4 text-sm text-gray-500">
                    <span>
                        Characters: {editor.storage.characterCount?.characters() || 0}
                    </span>
                    <span>
                        Words: {editor.storage.characterCount?.words() || 0}
                    </span>
                </div>
            )}
        </div>
    );
};
