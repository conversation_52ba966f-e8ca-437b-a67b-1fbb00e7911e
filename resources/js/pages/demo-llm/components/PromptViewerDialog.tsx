import React from 'react';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import { Eye } from 'lucide-react';

interface PromptViewerDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    temperature: number;
}

export const PromptViewerDialog: React.FC<PromptViewerDialogProps> = ({
    open,
    onOpenChange,
    temperature
}) => {
    // This is the exact prompt template from geminiService.ts (without the actual HTML content)
    const getPromptTemplate = () => {
        return `Analyze the following HTML content and split it into logical sections based on content structure.

ANALYSIS RULES:
1. Look for headings (h1, h2, h3) as natural section breaks
2. Identify ordered lists (<ol>) as potential separate sections
3. Find topic changes in content flow
4. Detect standalone elements (blockquotes, tables, etc.)

SECTION CREATION RULES:
- Create 1-8 sections based on content complexity
- Each section should have meaningful, substantial content
- Avoid creating sections with just 1-2 sentences
- Preserve HTML structure and relationships

RESPONSE FORMAT:
Return ONLY a JSON object with this exact structure:
{
  "totalSections": number,
  "sections": {
    "section1": {
      "title": "descriptive title",
      "html": "HTML content for this section"
    },
    "section2": {
      "title": "descriptive title",
      "html": "HTML content for this section"
    }
    // ... more sections as needed
  }
}

EXAMPLES OF GOOD SPLITS:
- Introduction + Main Content + Conclusion
- Overview + Step-by-step List + Additional Notes
- Problem + Solution + Implementation + Results

HTML Content to Analyze:
[The actual HTML content from your uploaded document will be inserted here]`;
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <Eye className="h-5 w-5" />
                        AI Prompt Template
                    </DialogTitle>
                    <DialogDescription>
                        This is the exact prompt template sent to AdvantryX AI for document analysis and content splitting.
                        The actual HTML content from your uploaded document is appended at the end.
                    </DialogDescription>
                </DialogHeader>

                <div className="flex-1 overflow-hidden flex flex-col">
                    {/* AI Model Info */}
                    <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <div className="flex items-center justify-between text-sm">
                            <div>
                                <span className="font-medium text-blue-700">Model:</span>
                                <span className="ml-2 text-blue-600">gemini-2.5-flash-preview-05-20</span>
                            </div>
                            <div>
                                <span className="font-medium text-blue-700">Temperature:</span>
                                <span className="ml-2 text-blue-600">{temperature.toFixed(1)}</span>
                            </div>
                        </div>
                    </div>

                    {/* Prompt Content */}
                    <div className="flex-1 overflow-auto">
                        <pre className="bg-gray-50 border rounded-md p-4 text-sm font-mono whitespace-pre-wrap overflow-auto">
                            {getPromptTemplate()}
                        </pre>
                    </div>

                    {/* Additional Info */}
                    <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                        <p className="text-sm text-yellow-700">
                            <strong>Note:</strong> The actual HTML content from your uploaded document is appended
                            to this prompt when sent to the AI. This template shows the instructions and format
                            requirements without exposing your document content.
                        </p>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};
