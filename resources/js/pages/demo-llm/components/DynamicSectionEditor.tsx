import React from 'react';
import { EditorContent, useEditor } from '@tiptap/react';
import { Toolbar } from './Toolbar';
import { SectionEditorState } from '../types';
import { defaultExtensions } from '../extensions';

interface DynamicSectionEditorProps {
    sections: SectionEditorState[];
    onUpdateSection?: (sectionId: string, content: string) => void;
    className?: string;
}

interface SectionCardProps {
    section: SectionEditorState;
    sectionNumber: number;
    onUpdate?: (content: string) => void;
}

const SectionCard: React.FC<SectionCardProps> = ({
    section,
    onUpdate
}) => {
    // Create editor for this section
    const editor = useEditor({
        extensions: defaultExtensions,
        content: section.content,
        editorProps: {
            attributes: {
                class: 'focus:outline-none min-h-[300px] p-4',
            },
        },
        onUpdate: ({ editor }) => {
            if (onUpdate) {
                onUpdate(editor.getHTML());
            }
        },
    });

    return (
        <div className="border border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm">
            {/* Section Header */}
            <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-800">
                        {section.title}
                    </h3>
                    <button
                        onClick={() => alert('Coming Soon Feature')}
                        className="px-3 py-1 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors font-medium shadow-sm"
                    >
                        Confirm & Save
                    </button>
                </div>
            </div>

            {/* Toolbar */}
            <Toolbar editor={editor} showCharacterCount={true} />

            {/* Editor Content */}
            <div className="relative">
                <div className="tiptap-editor">
                    <EditorContent editor={editor} />
                </div>
            </div>
        </div>
    );
};

export const DynamicSectionEditor: React.FC<DynamicSectionEditorProps> = ({
    sections,
    onUpdateSection,
    className = "space-y-6"
}) => {
    if (sections.length === 0) {
        return (
            <div className="text-center py-8 text-gray-500">
                <p>No sections loaded. Use the "Analyze & Split Content" button to create sections.</p>
            </div>
        );
    }

    return (
        <div className={className}>
            <div className="mb-4 flex items-center justify-between">
                <h2 className="text-xl font-bold text-gray-800">
                    Content Sections ({sections.length})
                </h2>
                <div className="text-sm text-gray-600">
                    Each section can be edited independently
                </div>
            </div>

            {/* Responsive Grid Layout */}
            <div className="grid gap-6 lg:grid-cols-2">
                {sections.map((section, index) => (
                    <div key={section.id} className="min-h-[400px]">
                        <SectionCard
                            section={section}
                            sectionNumber={index + 1}
                            onUpdate={(content) => onUpdateSection?.(section.id, content)}
                        />
                    </div>
                ))}
            </div>

            {/* Summary Info */}
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-2">Section Summary</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                    {sections.map((section, index) => (
                        <div key={section.id} className="text-blue-700">
                            <span className="font-medium">Section {index + 1}:</span> {section.title}
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};
