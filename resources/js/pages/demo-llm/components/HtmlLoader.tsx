import React, { useState, useRef } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Slider } from '@/components/ui/slider';
import { HtmlLoaderProps } from '../types';
import { geminiService } from '../utils/geminiService';
import { Upload, FileText, X, Settings, Eye } from 'lucide-react';
import axios from 'axios';
import { PromptViewerDialog } from './PromptViewerDialog';

export const HtmlLoader: React.FC<HtmlLoaderProps> = ({
    onSplitContent,
    className = "mb-6 p-4 bg-gray-50 rounded-lg border"
}) => {
    const [htmlContent, setHtmlContent] = useState('');
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [isUploading, setIsUploading] = useState(false);
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [uploadSuccess, setUploadSuccess] = useState(false);
    const [temperature, setTemperature] = useState([0.5]);
    const [promptDialogOpen, setPromptDialogOpen] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setSelectedFile(file);
            setError(null);
            setUploadSuccess(false);
            setHtmlContent('');
        }
    };

    const handleFileUpload = async () => {
        if (!selectedFile) {
            setError('Please select a DOCX file');
            return;
        }

        setIsUploading(true);
        setError(null);

        const formData = new FormData();
        formData.append('document', selectedFile);

        try {
            const response = await axios.post(route('convert.raw-html'), formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            if (response.data.html) {
                setHtmlContent(response.data.html);
                setUploadSuccess(true);
                setError(null);
            } else {
                setError('Conversion completed but no HTML was returned.');
            }
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to upload and convert document';
            setError(errorMessage);
        } finally {
            setIsUploading(false);
        }
    };

    const handleAnalyzeAndSplit = async () => {
        if (!htmlContent.trim()) {
            setError('Please upload a document first');
            return;
        }

        if (!onSplitContent) {
            setError('Split content functionality is not available');
            return;
        }

        setIsAnalyzing(true);
        setError(null);

        try {
            const sectionsData = await geminiService.analyzeAndSplitContent(htmlContent, temperature[0]);
            onSplitContent(sectionsData);
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to analyze and split content';
            setError(errorMessage);
        } finally {
            setIsAnalyzing(false);
        }
    };

    const handleClearFile = () => {
        setSelectedFile(null);
        setHtmlContent('');
        setUploadSuccess(false);
        setError(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    return (
        <div className={className}>
            <h2 className="text-lg font-semibold mb-3">Document Upload & Analysis</h2>

            {/* 2-Column Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Left Column - File Upload */}
                <div className="space-y-4">
                    <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-700">
                            Upload DOCX Document
                        </label>
                        <div className="flex items-center gap-2">
                            <input
                                type="file"
                                ref={fileInputRef}
                                onChange={handleFileChange}
                                accept=".docx"
                                className="hidden"
                                disabled={isUploading}
                            />
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => fileInputRef.current?.click()}
                                disabled={isUploading}
                                className="flex items-center gap-2"
                            >
                                <Upload className="h-4 w-4" />
                                Select DOCX File
                            </Button>

                        {selectedFile && (
                            <div className="flex items-center gap-2">
                                <FileText className="h-4 w-4 text-blue-600" />
                                <span className="text-sm text-gray-600">{selectedFile.name}</span>
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={handleClearFile}
                                    disabled={isUploading}
                                >
                                    <X className="h-4 w-4" />
                                </Button>
                            </div>
                        )}
                        </div>
                    </div>

                    {/* Upload Button */}
                    {selectedFile && !uploadSuccess && (
                        <Button
                            onClick={handleFileUpload}
                            disabled={isUploading}
                            className="bg-green-600 hover:bg-green-700"
                        >
                            {isUploading ? 'Converting...' : 'Upload & Convert to HTML'}
                        </Button>
                    )}

                    {/* Upload Loading Skeleton */}
                    {isUploading && (
                        <div className="space-y-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                            <div className="flex items-center gap-2">
                                <Skeleton className="h-4 w-4 rounded-full animate-pulse" />
                                <span className="text-sm text-blue-700 font-medium">Converting document to HTML...</span>
                            </div>
                            <div className="space-y-2">
                                <Skeleton className="h-3 w-full" />
                                <Skeleton className="h-3 w-4/5" />
                                <Skeleton className="h-3 w-3/4" />
                            </div>
                            <div className="flex items-center gap-2 text-xs text-blue-600">
                                <Skeleton className="h-3 w-3 rounded-full" />
                                <span>Processing DOCX file with Pandoc...</span>
                            </div>
                        </div>
                    )}

                    {/* Success Message */}
                    {uploadSuccess && (
                        <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                            <p className="text-sm text-green-600">
                                Document converted successfully! You can now analyze and split the content.
                            </p>
                        </div>
                    )}

                    {/* Analysis Button */}
                    {uploadSuccess && !isAnalyzing && (
                        <div className="flex gap-2 flex-wrap">
                            <Button
                                onClick={handleAnalyzeAndSplit}
                                disabled={!htmlContent.trim() || isAnalyzing}
                                className="bg-blue-600 hover:bg-blue-700"
                            >
                                {isAnalyzing ? 'Analyzing...' : 'Analyze & Split Content with AI'}
                            </Button>

                            <Button
                                variant="outline"
                                onClick={handleClearFile}
                                disabled={isAnalyzing}
                            >
                                Clear & Upload New Document
                            </Button>
                        </div>
                    )}
                </div>

                {/* Right Column - AI Settings */}
                <div className="space-y-4">
                    <div className="space-y-3">
                        <div className="flex items-center gap-2">
                            <Settings className="h-4 w-4 text-gray-600" />
                            <label className="text-sm font-medium text-gray-700">
                                AI Temperature Control
                            </label>
                        </div>

                        <div className="space-y-2">
                            <div className="flex justify-between items-center">
                                <span className="text-xs text-gray-500">More Focused</span>
                                <span className="text-xs text-gray-500">More Creative</span>
                            </div>

                            <Slider
                                value={temperature}
                                onValueChange={setTemperature}
                                min={0}
                                max={2}
                                step={0.1}
                                className="w-full"
                            />

                            <div className="text-center">
                                <span className="text-sm font-medium text-blue-600">
                                    Temperature: {temperature[0].toFixed(1)}
                                </span>
                            </div>

                            <div className="text-xs text-gray-500 space-y-1">
                                <p><strong>0.0:</strong> Most deterministic, consistent results</p>
                                <p><strong>0.5:</strong> Balanced creativity and consistency</p>
                                <p><strong>1.0+:</strong> More creative and varied responses</p>
                            </div>
                        </div>
                    </div>

                    {/* AI Status Info */}
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <div className="flex items-center gap-2 mb-2">
                            <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                            <span className="text-sm font-medium text-blue-700">AdvantryX AI Ready</span>
                        </div>
                        <p className="text-xs text-blue-600 mb-3">
                            Using AdvantryX AI for document analysis and content splitting.
                        </p>
                        <Button
                            onClick={() => setPromptDialogOpen(true)}
                            variant="outline"
                            size="sm"
                            className="w-full"
                        >
                            <Eye className="h-4 w-4 mr-2" />
                            View AI Prompt Template
                        </Button>
                    </div>
                </div>
            </div>

            {/* Error Message */}
            {error && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-sm text-red-600">{error}</p>
                </div>
            )}

            {/* Analysis Loading Skeleton */}
            {isAnalyzing && (
                <div className="mt-4 space-y-6 p-4 bg-purple-50 border border-purple-200 rounded-md">
                    <div className="flex items-center gap-2 mb-4">
                        <Skeleton className="h-4 w-4 rounded-full animate-pulse" />
                        <span className="text-sm text-purple-700 font-medium">Analyzing document with AI...</span>
                    </div>

                    {/* Header Section */}
                    <div className="flex justify-between items-center">
                        <Skeleton className="h-6 w-48" /> {/* "Dynamic Content Sections" */}
                        <Skeleton className="h-8 w-32 rounded-md" /> {/* "Clear All Sections" button */}
                    </div>

                    <div className="flex items-center gap-2">
                        <Skeleton className="h-5 w-40" /> {/* "Content Sections (3)" */}
                        <Skeleton className="h-4 w-64" /> {/* "Each section can be edited independently" */}
                    </div>

                    {/* 2-Column Grid for Sections */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Section 1 Skeleton */}
                        <div className="border border-gray-200 rounded-lg bg-white">
                            <div className="flex items-center justify-between p-3 border-b border-gray-200">
                                <Skeleton className="h-5 w-32" /> {/* "Section 1: Introduction" */}
                                <Skeleton className="h-6 w-16 rounded" /> {/* "section1" tag */}
                            </div>

                            {/* Toolbar skeleton */}
                            <div className="flex flex-wrap gap-1 p-2 border-b border-gray-200 bg-gray-50">
                                <Skeleton className="h-7 w-8 rounded" /> {/* Bold */}
                                <Skeleton className="h-7 w-8 rounded" /> {/* Italic */}
                                <Skeleton className="h-7 w-10 rounded" /> {/* Strike */}
                                <Skeleton className="h-7 w-8 rounded" /> {/* Code */}
                                <Skeleton className="h-7 w-12 rounded" /> {/* Underline */}
                                <Skeleton className="h-7 w-12 rounded" /> {/* Highlight */}
                                <Skeleton className="h-7 w-6 rounded" /> {/* H1 */}
                                <Skeleton className="h-7 w-6 rounded" /> {/* H2 */}
                                <Skeleton className="h-7 w-6 rounded" /> {/* H3 */}
                                <Skeleton className="h-7 w-16 rounded" /> {/* Bullet List */}
                            </div>

                            {/* Content area */}
                            <div className="p-4 space-y-2">
                                <Skeleton className="h-5 w-24" /> {/* "Introduction" */}
                                <Skeleton className="h-4 w-full" />
                                <Skeleton className="h-5 w-32" /> {/* "Background Information" */}
                                <Skeleton className="h-4 w-3/4" />
                                <Skeleton className="h-4 w-5/6" />
                            </div>

                            {/* Footer stats */}
                            <div className="flex justify-between p-2 text-xs text-gray-500 border-t border-gray-200">
                                <Skeleton className="h-3 w-20" /> {/* "Characters: 76" */}
                                <Skeleton className="h-3 w-16" /> {/* "Words: 12" */}
                            </div>
                        </div>

                        {/* Section 2 Skeleton */}
                        <div className="border border-gray-200 rounded-lg bg-white">
                            <div className="flex items-center justify-between p-3 border-b border-gray-200">
                                <Skeleton className="h-5 w-36" /> {/* "Section 2: Methodology" */}
                                <Skeleton className="h-6 w-16 rounded" /> {/* "section2" tag */}
                            </div>

                            {/* Toolbar skeleton */}
                            <div className="flex flex-wrap gap-1 p-2 border-b border-gray-200 bg-gray-50">
                                <Skeleton className="h-7 w-8 rounded" />
                                <Skeleton className="h-7 w-8 rounded" />
                                <Skeleton className="h-7 w-10 rounded" />
                                <Skeleton className="h-7 w-8 rounded" />
                                <Skeleton className="h-7 w-12 rounded" />
                                <Skeleton className="h-7 w-12 rounded" />
                                <Skeleton className="h-7 w-6 rounded" />
                                <Skeleton className="h-7 w-6 rounded" />
                                <Skeleton className="h-7 w-6 rounded" />
                                <Skeleton className="h-7 w-16 rounded" />
                            </div>

                            {/* Content area */}
                            <div className="p-4 space-y-2">
                                <Skeleton className="h-5 w-28" /> {/* "Methodology" */}
                                <Skeleton className="h-4 w-full" />
                                <Skeleton className="h-5 w-32" /> {/* "Research Design" */}
                                <Skeleton className="h-4 w-4/5" />
                                <Skeleton className="h-5 w-28" /> {/* "Data Collection" */}
                                <Skeleton className="h-4 w-3/4" />
                            </div>

                            {/* Footer stats */}
                            <div className="flex justify-between p-2 text-xs text-gray-500 border-t border-gray-200">
                                <Skeleton className="h-3 w-20" />
                                <Skeleton className="h-3 w-16" />
                            </div>
                        </div>
                    </div>

                    <div className="text-center pt-2">
                        <div className="flex items-center justify-center gap-2">
                            <Skeleton className="h-3 w-3 rounded-full animate-pulse" />
                            <span className="text-xs text-purple-600">Processing with AdvantryX AI...</span>
                        </div>
                    </div>
                </div>
            )}

            {/* HTML Preview (Optional - for debugging) */}
            {htmlContent && (
                <details className="mt-4">
                    <summary className="text-sm font-medium text-gray-700 cursor-pointer">
                        View Converted HTML (Debug)
                    </summary>
                    <Textarea
                        value={htmlContent}
                        readOnly
                        className="mt-2 min-h-24 font-mono text-xs"
                    />
                </details>
            )}

            {/* Prompt Viewer Dialog */}
            <PromptViewerDialog
                open={promptDialogOpen}
                onOpenChange={setPromptDialogOpen}
                temperature={temperature[0]}
            />
        </div>
    );
};
