import React from 'react';
import { EditorContent } from '@tiptap/react';
import { useTipTapEditor } from '../hooks/useTipTapEditor';
import { Toolbar } from './Toolbar';
import { TipTapEditorProps } from '../types';

export const TipTapEditor: React.FC<TipTapEditorProps> = ({
    editor: externalEditor,
    content,
    placeholder,
    className = "border border-gray-300 rounded-lg overflow-hidden",
    editorClassName,
    showToolbar = true,
    showCharacterCount = true,
    onUpdate,
    extensions,
}) => {
    const { editor: internalEditor } = useTipTapEditor({
        content,
        placeholder,
        extensions,
        onUpdate,
        editorClassName,
    });

    // Use external editor if provided, otherwise use internal editor
    const editor = externalEditor || internalEditor;

    return (
        <div className={className}>
            {/* Toolbar */}
            {showToolbar && (
                <Toolbar editor={editor} showCharacterCount={showCharacterCount} />
            )}

            {/* Editor Content */}
            <div className="relative">
                <div className="tiptap-editor">
                    <EditorContent editor={editor} />
                </div>
            </div>
        </div>
    );
};
