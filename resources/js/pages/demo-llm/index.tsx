import AppLayout from '@/layouts/app-layout';
import { HtmlLoader } from './components/HtmlLoader';
import { DynamicSectionEditor } from './components/DynamicSectionEditor';
import { useDynamicSectionEditors } from './hooks/useDynamicSectionEditors';
import { DynamicSectionsResponse } from './types';
import './tiptap-editor.css';

export default function Tiptap() {
    const {
        sections,
        loadSections,
        updateSection,
        clearAllSections,
        hasSections
    } = useDynamicSectionEditors();

    const handleSplitContent = (sectionsData: DynamicSectionsResponse) => {
        loadSections(sectionsData);
    };

    const handleClearSections = () => {
        clearAllSections();
    };

    return (
        <AppLayout>
            <div className="w-full p-6">
                <h1 className="mb-6 text-3xl font-bold">AI Editor Powered By AdvantryX</h1>

                {/* HTML Input Section */}
                <HtmlLoader onSplitContent={handleSplitContent} />

                {/* Conditional Rendering: Show sections only when available */}
                {hasSections ? (
                    <div className="space-y-6">
                        {/* Clear Sections Button */}
                        <div className="flex justify-between items-center">
                            <h2 className="text-xl font-semibold">Dynamic Content Sections</h2>
                            <button
                                onClick={handleClearSections}
                                className="px-4 py-2 text-sm bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
                            >
                                Clear All Sections
                            </button>
                        </div>

                        {/* Dynamic Section Editors */}
                        <DynamicSectionEditor
                            sections={sections}
                            onUpdateSection={updateSection}
                        />
                    </div>
                ) : (
                    <div className="text-center py-8 text-gray-500">
                        <p className="text-lg">Ready to analyze your document!</p>
                        <p className="text-sm mt-2">
                            Upload a DOCX document above and click "Analyze & Split Content with AI" to create dynamic sections.
                        </p>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
