export const defaultContent = `
<h1>Welcome to TipTap Editor!</h1>
<p>This is a <strong>rich text editor</strong> built with TipTap and includes <em>many powerful extensions</em>.</p>

<h2>Text Formatting</h2>
<p>You can make text <strong>bold</strong>, <em>italic</em>, <u>underlined</u>, <s>strikethrough</s>, <mark>highlighted</mark>, or <code>inline code</code>.</p>

<h3>Lists and Tasks</h3>
<ul>
  <li>Regular bullet lists</li>
  <li>With multiple items</li>
</ul>

<ol>
  <li>Numbered lists</li>
  <li>Are also supported</li>
</ol>

<ul data-type="taskList">
  <li data-type="taskItem" data-checked="true">Completed task</li>
  <li data-type="taskItem" data-checked="false">Pending task</li>
  <li data-type="taskItem" data-checked="false">Another task to do</li>
</ul>

<blockquote>
  <p>This is a blockquote. Perfect for highlighting important information or quotes.</p>
</blockquote>

<p style="text-align: center">This text is centered</p>
<p style="text-align: right">This text is right-aligned</p>

<p>Try selecting text to see the bubble menu, or click in an empty area to see the floating menu!</p>
`;
