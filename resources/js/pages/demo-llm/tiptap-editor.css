/* TipTap Editor Styles - Demo LLM Specific */
.tiptap-editor .tiptap {
    outline: none;
}

.tiptap-editor .tiptap h1 {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 1rem;
    margin-top: 1.5rem;
}

.tiptap-editor .tiptap h1:first-child {
    margin-top: 0;
}

.tiptap-editor .tiptap h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    margin-top: 1.25rem;
}

.tiptap-editor .tiptap h2:first-child {
    margin-top: 0;
}

.tiptap-editor .tiptap h3 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    margin-top: 1rem;
}

.tiptap-editor .tiptap h3:first-child {
    margin-top: 0;
}

.tiptap-editor .tiptap h4 {
    font-size: 1.125rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    margin-top: 1rem;
}

.tiptap-editor .tiptap h4:first-child {
    margin-top: 0;
}

.tiptap-editor .tiptap h5 {
    font-size: 1rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    margin-top: 0.75rem;
}

.tiptap-editor .tiptap h5:first-child {
    margin-top: 0;
}

.tiptap-editor .tiptap h6 {
    font-size: 0.875rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    margin-top: 0.75rem;
}

.tiptap-editor .tiptap h6:first-child {
    margin-top: 0;
}

.tiptap-editor .tiptap p {
    margin-bottom: 1rem;
    line-height: 1.625;
}

.tiptap-editor .tiptap strong {
    font-weight: 700;
}

.tiptap-editor .tiptap em {
    font-style: italic;
}

.tiptap-editor .tiptap s {
    text-decoration: line-through;
}

.tiptap-editor .tiptap code {
    background-color: #f3f4f6;
    color: #1f2937;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.tiptap-editor .tiptap pre {
    background-color: #111827;
    color: #f9fafb;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    overflow-x: auto;
}

.tiptap-editor .tiptap pre code {
    background-color: transparent;
    color: #f9fafb;
    padding: 0;
}

.tiptap-editor .tiptap blockquote {
    border-left: 4px solid #d1d5db;
    padding-left: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
    font-style: italic;
    color: #4b5563;
    background-color: #f9fafb;
}

.tiptap-editor .tiptap ul {
    list-style-type: disc;
    list-style-position: inside;
    margin-bottom: 1rem;
}

.tiptap-editor .tiptap ul > li + li {
    margin-top: 0.25rem;
}

.tiptap-editor .tiptap ol {
    list-style-type: decimal;
    list-style-position: inside;
    margin-bottom: 1rem;
}

.tiptap-editor .tiptap ol > li + li {
    margin-top: 0.25rem;
}

.tiptap-editor .tiptap li {
    line-height: 1.625;
}

.tiptap-editor .tiptap li p {
    margin-bottom: 0;
    display: inline;
}

.tiptap-editor .tiptap hr {
    border: 0;
    border-top: 1px solid #d1d5db;
    margin: 1.5rem 0;
}

/* Dark mode styles */
.dark .tiptap-editor .tiptap code {
    background-color: #1f2937;
    color: #e5e7eb;
}

.dark .tiptap-editor .tiptap pre {
    background-color: #1f2937;
    color: #e5e7eb;
}

.dark .tiptap-editor .tiptap pre code {
    background-color: transparent;
    color: #e5e7eb;
}

.dark .tiptap-editor .tiptap blockquote {
    border-left-color: #4b5563;
    color: #d1d5db;
    background-color: #1f2937;
}

.dark .tiptap-editor .tiptap hr {
    border-top-color: #4b5563;
}

/* New Extensions Styles */

/* Underline */
.tiptap-editor .tiptap u {
    text-decoration: underline;
}

/* Highlight */
.tiptap-editor .tiptap mark {
    background-color: #fef3c7;
    padding: 0 0.25rem;
    border-radius: 0.25rem;
}

.dark .tiptap-editor .tiptap mark {
    background-color: #d97706;
    color: #000000;
}

/* Text Alignment */
.tiptap-editor .tiptap[style*="text-align: left"] {
    text-align: left;
}

.tiptap-editor .tiptap[style*="text-align: center"] {
    text-align: center;
}

.tiptap-editor .tiptap[style*="text-align: right"] {
    text-align: right;
}

.tiptap-editor .tiptap[style*="text-align: justify"] {
    text-align: justify;
}

/* Task Lists */
.tiptap-editor .tiptap ul[data-type="taskList"] {
    list-style: none;
    padding-left: 0;
}

.tiptap-editor .tiptap li[data-type="taskItem"] {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.tiptap-editor .tiptap li[data-type="taskItem"] > label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.tiptap-editor .tiptap li[data-type="taskItem"] > label > input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    border-radius: 0.25rem;
    border: 1px solid #d1d5db;
    color: #2563eb;
}

.tiptap-editor .tiptap li[data-type="taskItem"] > label > input[type="checkbox"]:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.tiptap-editor .tiptap li[data-type="taskItem"][data-checked="true"] > div > p {
    text-decoration: line-through;
    color: #6b7280;
}

/* Images */
.tiptap-editor .tiptap img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* Links */
.tiptap-editor .tiptap a {
    color: #2563eb;
    text-decoration: underline;
}

.tiptap-editor .tiptap a:hover {
    color: #1e40af;
}

.dark .tiptap-editor .tiptap a {
    color: #60a5fa;
}

.dark .tiptap-editor .tiptap a:hover {
    color: #93c5fd;
}

/* YouTube Videos */
.tiptap-editor .tiptap iframe {
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    max-width: 100%;
}

/* Tables */
.tiptap-editor .tiptap table {
    border-collapse: collapse;
    border: 1px solid #d1d5db;
    width: 100%;
    margin-bottom: 1rem;
    min-width: 600px; /* Ensure table has minimum width for scrolling */
}

/* Table container for horizontal scrolling */
.tiptap-editor .tiptap .tableWrapper {
    overflow-x: auto;
    margin-bottom: 1rem;
}

.tiptap-editor .tiptap .tableWrapper table {
    margin-bottom: 0; /* Remove margin from table when inside wrapper */
}

.tiptap-editor .tiptap th,
.tiptap-editor .tiptap td {
    border: 1px solid #d1d5db;
    padding: 0.75rem;
    text-align: left;
}

.tiptap-editor .tiptap th {
    background-color: #f3f4f6;
    font-weight: 600;
}

.dark .tiptap-editor .tiptap table {
    border-color: #4b5563;
}

.dark .tiptap-editor .tiptap th,
.dark .tiptap-editor .tiptap td {
    border-color: #4b5563;
}

.dark .tiptap-editor .tiptap th {
    background-color: #374151;
}

/* Placeholder */
.tiptap-editor .tiptap .is-editor-empty:first-child::before {
    color: #9ca3af;
    float: left;
    height: 0;
    pointer-events: none;
    content: attr(data-placeholder);
}
