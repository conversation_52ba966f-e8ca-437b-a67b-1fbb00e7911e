import { useEditor } from '@tiptap/react';
import { defaultExtensions } from '../extensions';
import { defaultContent } from '../utils/defaultContent';

interface UseTipTapEditorProps {
    content?: string;
    placeholder?: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    extensions?: any[];
    onUpdate?: (content: string) => void;
    editorClassName?: string;
}

export const useTipTapEditor = ({
    content = defaultContent,
    placeholder,
    extensions = defaultExtensions,
    onUpdate,
    editorClassName = 'focus:outline-none min-h-[400px] p-4'
}: UseTipTapEditorProps = {}) => {
    // Create extensions with placeholder if provided
    const finalExtensions = placeholder ?
        extensions.map(ext => ext.name === 'placeholder' ? ext.configure({ placeholder }) : ext) :
        extensions;

    const editor = useEditor({
        extensions: finalExtensions,
        content,
        editorProps: {
            attributes: {
                class: editorClassName,
            },
        },
        onUpdate: ({ editor }) => {
            if (onUpdate) {
                onUpdate(editor.getHTML());
            }
        },
    });

    const loadContent = (htmlContent: string) => {
        if (editor && htmlContent.trim()) {
            editor.commands.setContent(htmlContent);
        }
    };

    const getContent = () => {
        return editor?.getHTML() || '';
    };

    const getTextContent = () => {
        return editor?.getText() || '';
    };

    const clearContent = () => {
        editor?.commands.clearContent();
    };

    return {
        editor,
        loadContent,
        getContent,
        getTextContent,
        clearContent,
    };
};
