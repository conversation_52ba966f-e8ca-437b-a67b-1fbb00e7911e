import { useState, useCallback } from 'react';
import { SectionEditorState, DynamicSectionsResponse } from '../types';

interface UseDynamicSectionEditorsReturn {
    sections: SectionEditorState[];
    loadSections: (sectionsData: DynamicSectionsResponse) => void;
    updateSection: (sectionId: string, content: string) => void;
    getAllContent: () => string;
    clearAllSections: () => void;
    hasSections: boolean;
}

export const useDynamicSectionEditors = (): UseDynamicSectionEditorsReturn => {
    const [sections, setSections] = useState<SectionEditorState[]>([]);

    // Load sections from AI response
    const loadSections = useCallback((sectionsData: DynamicSectionsResponse) => {
        const newSections: SectionEditorState[] = [];

        // Create sections based on the response
        Object.entries(sectionsData.sections).forEach(([sectionKey, sectionData]) => {
            newSections.push({
                id: sectionKey,
                title: sectionData.title,
                content: sectionData.html,
                editor: null, // Will be created in the component
            });
        });

        setSections(newSections);
    }, []);

    // Update content of a specific section
    const updateSection = useCallback((sectionId: string, content: string) => {
        setSections(prevSections =>
            prevSections.map(section =>
                section.id === sectionId
                    ? { ...section, content }
                    : section
            )
        );
    }, []);

    // Get combined HTML content from all sections
    const getAllContent = useCallback(() => {
        return sections
            .map(section => section.editor?.getHTML() || section.content)
            .join('\n\n');
    }, [sections]);

    // Clear all sections
    const clearAllSections = useCallback(() => {
        setSections([]);
    }, []);

    return {
        sections,
        loadSections,
        updateSection,
        getAllContent,
        clearAllSections,
        hasSections: sections.length > 0,
    };
};
