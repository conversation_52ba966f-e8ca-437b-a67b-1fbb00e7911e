import { Editor } from '@tiptap/react';
import { useState, useEffect, useRef } from 'react';
import {
    TableIcon,
    RowsIcon,
    ColumnsIcon,
    Trash2Icon,
    SplitIcon,
    MergeIcon,
    AlignJustifyIcon,
    ChevronDownIcon
} from 'lucide-react';

interface TableToolbarProps {
    editor: Editor;
}

const TableToolbar = ({ editor }: TableToolbarProps) => {
    const [showTableOptions, setShowTableOptions] = useState(false);
    const tableMenuRef = useRef<HTMLDivElement>(null);
    
    // Close table dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (tableMenuRef.current && !tableMenuRef.current.contains(event.target as Node)) {
                setShowTableOptions(false);
            }
        };
        
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);
    
    const toolbarButtonClass = (isActive: boolean) =>
        `p-2 text-sm font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
            isActive ? 'bg-slate-900 text-slate-50 hover:bg-slate-900/90' : 'bg-slate-100 text-slate-900 hover:bg-slate-100/80'
        }`;

    return (
        <div className="relative" ref={tableMenuRef}>
            <button
                type="button"
                title="Table Options"
                onClick={() => setShowTableOptions(!showTableOptions)}
                className={toolbarButtonClass(editor.isActive('table'))}
            >
                <div className="flex items-center">
                    <TableIcon size={18} />
                    <ChevronDownIcon size={14} className="ml-1" />
                </div>
            </button>
            
            {showTableOptions && (
                <div className="absolute top-full left-0 mt-1 bg-white shadow-lg rounded-md border border-gray-200 p-2 z-10 w-56">
                    <div className="grid grid-cols-2 gap-1 mb-2">
                        <button
                            type="button"
                            title="Insert Table"
                            onClick={() => {
                                editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
                                setShowTableOptions(false);
                            }}
                            className="flex items-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100"
                        >
                            <TableIcon size={16} className="mr-1" />
                            <span>Insert Table</span>
                        </button>
                        <button
                            type="button"
                            title="Delete Table"
                            disabled={!editor.isActive('table')}
                            onClick={() => {
                                editor.chain().focus().deleteTable().run();
                                setShowTableOptions(false);
                            }}
                            className="flex items-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <Trash2Icon size={16} className="mr-1" />
                            <span>Delete Table</span>
                        </button>
                    </div>
                    
                    <div className="border-t border-gray-200 my-1 pt-1">
                        <div className="text-xs font-semibold text-gray-500 mb-1 px-1">ROWS</div>
                        <div className="grid grid-cols-2 gap-1 mb-2">
                            <button
                                type="button"
                                title="Add Row Before"
                                disabled={!editor.isActive('table')}
                                onClick={() => {
                                    editor.chain().focus().addRowBefore().run();
                                }}
                                className="flex items-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <RowsIcon size={16} className="mr-1" />
                                <span>Add Before</span>
                            </button>
                            <button
                                type="button"
                                title="Add Row After"
                                disabled={!editor.isActive('table')}
                                onClick={() => {
                                    editor.chain().focus().addRowAfter().run();
                                }}
                                className="flex items-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <RowsIcon size={16} className="mr-1" />
                                <span>Add After</span>
                            </button>
                            <button
                                type="button"
                                title="Delete Row"
                                disabled={!editor.isActive('table')}
                                onClick={() => {
                                    editor.chain().focus().deleteRow().run();
                                }}
                                className="flex items-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <Trash2Icon size={16} className="mr-1" />
                                <span>Delete Row</span>
                            </button>
                        </div>
                    </div>
                    
                    <div className="border-t border-gray-200 my-1 pt-1">
                        <div className="text-xs font-semibold text-gray-500 mb-1 px-1">COLUMNS</div>
                        <div className="grid grid-cols-2 gap-1 mb-2">
                            <button
                                type="button"
                                title="Add Column Before"
                                disabled={!editor.isActive('table')}
                                onClick={() => {
                                    editor.chain().focus().addColumnBefore().run();
                                }}
                                className="flex items-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <ColumnsIcon size={16} className="mr-1" />
                                <span>Add Before</span>
                            </button>
                            <button
                                type="button"
                                title="Add Column After"
                                disabled={!editor.isActive('table')}
                                onClick={() => {
                                    editor.chain().focus().addColumnAfter().run();
                                }}
                                className="flex items-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <ColumnsIcon size={16} className="mr-1" />
                                <span>Add After</span>
                            </button>
                            <button
                                type="button"
                                title="Delete Column"
                                disabled={!editor.isActive('table')}
                                onClick={() => {
                                    editor.chain().focus().deleteColumn().run();
                                }}
                                className="flex items-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <Trash2Icon size={16} className="mr-1" />
                                <span>Delete Column</span>
                            </button>
                        </div>
                    </div>
                    
                    <div className="border-t border-gray-200 my-1 pt-1">
                        <div className="text-xs font-semibold text-gray-500 mb-1 px-1">CELLS</div>
                        <div className="grid grid-cols-2 gap-1 mb-1">
                            <button
                                type="button"
                                title="Merge Cells"
                                disabled={!editor.isActive('table')}
                                onClick={() => {
                                    editor.chain().focus().mergeCells().run();
                                }}
                                className="flex items-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <MergeIcon size={16} className="mr-1" />
                                <span>Merge Cells</span>
                            </button>
                            <button
                                type="button"
                                title="Split Cell"
                                disabled={!editor.isActive('table')}
                                onClick={() => {
                                    editor.chain().focus().splitCell().run();
                                }}
                                className="flex items-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <SplitIcon size={16} className="mr-1" />
                                <span>Split Cell</span>
                            </button>
                        </div>
                    </div>
                    
                    <div className="border-t border-gray-200 my-1 pt-1">
                        <div className="text-xs font-semibold text-gray-500 mb-1 px-1">HEADER</div>
                        <div className="grid grid-cols-2 gap-1">
                            <button
                                type="button"
                                title="Toggle Header Row"
                                disabled={!editor.isActive('table')}
                                onClick={() => {
                                    editor.chain().focus().toggleHeaderRow().run();
                                }}
                                className="flex items-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <AlignJustifyIcon size={16} className="mr-1" />
                                <span>Header Row</span>
                            </button>
                            <button
                                type="button"
                                title="Toggle Header Column"
                                disabled={!editor.isActive('table')}
                                onClick={() => {
                                    editor.chain().focus().toggleHeaderColumn().run();
                                }}
                                className="flex items-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <AlignJustifyIcon size={16} className="mr-1 transform rotate-90" />
                                <span>Header Column</span>
                            </button>
                            <button
                                type="button"
                                title="Toggle Header Cell"
                                disabled={!editor.isActive('table')}
                                onClick={() => {
                                    editor.chain().focus().toggleHeaderCell().run();
                                }}
                                className="flex items-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <AlignJustifyIcon size={16} className="mr-1" />
                                <span>Header Cell</span>
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default TableToolbar;
