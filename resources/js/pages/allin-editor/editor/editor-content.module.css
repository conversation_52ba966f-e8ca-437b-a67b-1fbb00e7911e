/* This class will be applied to the Tiptap editor's direct DOM element */
/* We can keep Tailwind utilities for border, padding, margin etc. here if not handled by a wrapper */

/* Required ProseMirror base styles */
.editorContent :global(.ProseMirror) {
  white-space: pre-wrap;
  word-wrap: break-word;
  outline: none;
}

/* Target global HTML elements *within* the .editorContent scope */
.editorContent :global(h1) {
  font-size: 2em;
  font-weight: bold;
  margin-top: 0.67em;
  margin-bottom: 0.67em;
  line-height: 1.2;
}

.editorContent :global(h2) {
  font-size: 1.5em;
  font-weight: bold;
  margin-top: 0.83em;
  margin-bottom: 0.83em;
  line-height: 1.2;
}

.editorContent :global(h3) {
  font-size: 1.17em;
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 1em;
  line-height: 1.2;
}

.editorContent :global(ul) {
  list-style-type: disc;
  padding-left: 2.5rem; /* Tailwind's pl-10, often a good default */
  margin-top: 1em;
  margin-bottom: 1em;
}

/* Standard ordered list styling */
.editorContent :global(ol) {
  padding-left: 2.5rem;
  margin-top: 1em;
  margin-bottom: 1em;
  list-style-type: decimal;
}

.editorContent :global(ol[start]) {
  /* This ensures the browser respects the start attribute */
  counter-reset: list-item calc(attr(start) - 1);
}

.editorContent :global(ol > li) {
  position: relative;
}

.editorContent :global(li) {
  margin-top: 0.25em;
  margin-bottom: 0.25em;
}

/* Handle paragraphs inside list items, common reset by prose */
.editorContent :global(li > p) {
    margin-top: 0.25em;
    margin-bottom: 0.25em;
}

/* Blockquote styles */
.editorContent :global(blockquote) {
  border-left: 4px solid #ccc;
  margin-left: 0;
  margin-right: 0;
  padding-left: 1em;
  font-style: italic;
  color: #555;
  margin-top: 1em;
  margin-bottom: 1em;
}

/* CodeBlock (pre) styles */
.editorContent :global(pre) {
  background: #f4f4f4;
  border: 1px solid #ddd;
  border-radius: 4px;
  color: #333;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  padding: 0.75em 1em;
  margin-top: 1em;
  margin-bottom: 1em;
  overflow-x: auto; /* Allow horizontal scrolling for long code lines */
}

.editorContent :global(pre code) {
  font-family: inherit;
  background: none;
  color: inherit;
  padding: 0;
  border-radius: 0;
}

/* Horizontal Rule styles */
.editorContent :global(hr) {
  border: none;
  border-top: 1px solid #ccc;
  margin-top: 2em;
  margin-bottom: 2em;
}

/* Highlight styles */
.editorContent :global(mark) {
  background-color: #fef08a; /* Light yellow */
  border-radius: 2px;
  padding: 0 2px;
}

/* Link styles */
.editorContent :global(a) {
  color: #2563eb; /* Tailwind blue-600 */
  text-decoration: none;
  border-bottom: 1px solid currentColor;
  transition: color 0.2s ease, border-color 0.2s ease;
}

.editorContent :global(a:hover) {
  color: #1d4ed8; /* Tailwind blue-700 */
  border-bottom-width: 2px;
  cursor: pointer;
}

/* Subscript styles */
.editorContent :global(sub) {
  font-size: 0.75em;
  vertical-align: sub;
  line-height: 1;
}

/* Superscript styles */
.editorContent :global(sup) {
  font-size: 0.75em;
  vertical-align: super;
  line-height: 1;
}

/* Underline styles */
.editorContent :global(u) {
  text-decoration: underline;
  text-decoration-thickness: from-font;
  text-underline-offset: 2px;
}

/* Table styles */
.editorContent :global(table) {
  border-collapse: collapse;
  margin: 1.5rem 0;
  overflow: hidden;
  table-layout: fixed;
  width: 100%;
}

.editorContent :global(td),
.editorContent :global(th) {
  border: 1px solid #e2e8f0; /* Tailwind gray-300 */
  box-sizing: border-box;
  min-width: 1em;
  padding: 0.5rem;
  position: relative;
  vertical-align: top;
}

.editorContent :global(th) {
  background-color: #e0f2fe; /* Tailwind blue-100 */
  font-weight: bold;
  text-align: left;
  color: black; /* Tailwind blue-700 */
}

.editorContent :global(.selectedCell:after) {
  background: rgba(200, 200, 200, 0.4);
  content: "";
  left: 0; right: 0; top: 0; bottom: 0;
  pointer-events: none;
  position: absolute;
  z-index: 2;
}

.editorContent :global(.column-resize-handle) {
  background-color: #4f46e5; /* Tailwind indigo-600 */
  bottom: -2px;
  pointer-events: auto;
  position: absolute;
  right: -2px;
  top: 0;
  width: 4px;
  cursor: col-resize;
}

.editorContent :global(.tableWrapper) {
  overflow-x: auto;
}

/* For table resize cursor */
.editorContent:global(.resize-cursor) {
  cursor: ew-resize;
  cursor: col-resize;
}

/* Text alignment styles */
.editorContent :global([data-text-align='left']) {
  text-align: left;
}

.editorContent :global([data-text-align='center']) {
  text-align: center;
}

.editorContent :global([data-text-align='right']) {
  text-align: right;
}

.editorContent :global([data-text-align='justify']) {
  text-align: justify;
}



/* Image styles */
.editorContent :global(img.editor-image) {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  cursor: pointer;
  margin: 1rem 0;
  transition: filter 0.2s ease-in-out;
}

.editorContent :global(img.editor-image:hover) {
  filter: brightness(0.95);
}

/* Image alignment styles */
.editorContent :global(img.editor-image[data-align="left"]) {
  float: left;
  margin-right: 1.5rem;
  margin-bottom: 1rem;
  margin-top: 0.5rem;
  max-width: 50%;
}

.editorContent :global(img.editor-image[data-align="center"]) {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.editorContent :global(img.editor-image[data-align="right"]) {
  float: right;
  margin-left: 1.5rem;
  margin-bottom: 1rem;
  margin-top: 0.5rem;
  max-width: 50%;
}

/* Image caption styles */
.editorContent :global(figure) {
  margin: 1.5rem 0;
  display: inline-block;
}

.editorContent :global(figure img.editor-image) {
  margin: 0;
}

.editorContent :global(figcaption) {
  text-align: center;
  font-size: 0.875rem;
  color: #4b5563;
  margin-top: 0.5rem;
  font-style: italic;
}

/* Image with caption alignment */
.editorContent :global(figure[data-align="left"]) {
  float: left;
  margin-right: 1.5rem;
  margin-bottom: 1rem;
  margin-top: 0.5rem;
  max-width: 50%;
}

.editorContent :global(figure[data-align="center"]) {
  margin-left: auto;
  margin-right: auto;
  display: table;
}

.editorContent :global(figure[data-align="right"]) {
  float: right;
  margin-left: 1.5rem;
  margin-bottom: 1rem;
  margin-top: 0.5rem;
  max-width: 50%;
}
