import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Editor } from '@tiptap/react';
import {
    BoldIcon,
    BookmarkIcon,
    FileTextIcon,
    Heading1Icon,
    Heading2Icon,
    Heading3Icon,
    HelpCircle,
    HighlighterIcon,
    ItalicIcon,
    LayoutIcon,
    LinkIcon,
    ListIcon,
    ListIcon as ListsIcon,
    MinusIcon,
    PaletteIcon,
    QuoteIcon,
    Redo2Icon,
    SquareCodeIcon,
    StrikethroughIcon,
    Subscript as SubscriptIcon,
    Superscript as SuperscriptIcon,
    TypeIcon,
    UnderlineIcon,
    Undo2Icon,
    WrapTextIcon,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { SectionSelectionDialog } from '../components/SectionSelectionDialog';
import { SectionSubsectionSelectionDialog } from '../components/SubsectionSelectionDialog';
import { useDocumentContext } from '../context/DocumentContext';
import { createQuestionFromSelection } from '../services/questionService';
import {
    createSectionFromSelection,
    createSubsectionForCurrentSection,
    createSubsectionForSection,
    fetchAvailableSections,
    Section,
} from '../services/sectionService';
import FormatToolbar from './FormatToolbar';
import ImageToolbar from './ImageToolbar';
import TableToolbar from './TableToolbar';
import OrderedListMenu from './components/OrderedListMenu';

interface SubsectionOption {
    id: number;
    title: string;
    section_id: number;
    section_title: string;
    subsection_number: number;
    section_number: number;
}

interface EditorToolbarProps {
    editor: Editor;
    currentSectionId?: number | null;
    currentSubsectionId?: number | null;
}

const EditorToolbar = ({ editor, currentSectionId, currentSubsectionId }: EditorToolbarProps) => {
    const { currentDocumentId, fetchDocumentSections } = useDocumentContext();
    const [showSectionDialog, setShowSectionDialog] = useState(false);
    const [showQuestionDialog, setShowQuestionDialog] = useState(false);
    const [sections, setSections] = useState<Section[]>([]);
    const [subsections, setSubsections] = useState<SubsectionOption[]>([]);
    const [selectedText, setSelectedText] = useState('');
    const [hasTextSelection, setHasTextSelection] = useState(false);

    useEffect(() => {
        if (!editor) return;

        const updateSelection = () => {
            const { from, to } = editor.state.selection;
            const hasSelection = from !== to && editor.state.doc.textBetween(from, to, ' ').trim().length > 0;
            setHasTextSelection(prev => prev !== hasSelection ? hasSelection : prev);
        };

        updateSelection();
        editor.on('selectionUpdate', updateSelection);

        return () => {
            editor.off('selectionUpdate', updateSelection);
        };
    }, [editor]);

    const toolbarButtonClass = (isActive: boolean) =>
        `p-2 text-sm font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
            isActive ? 'bg-slate-900 text-slate-50 hover:bg-slate-900/90' : 'bg-slate-100 text-slate-900 hover:bg-slate-100/80'
        }`;

    const handleCreateSectionFromSelection = async () => {
        if (!currentDocumentId) return;
        await createSectionFromSelection(editor, currentDocumentId, fetchDocumentSections);
    };

    const handleCreateSubsectionFromSelection = async () => {
        if (!editor) return;

        const { from, to } = editor.state.selection;

        if (from === to) {
            toast.error('Please select some text first');
            return;
        }

        const text = editor.state.doc.textBetween(from, to, ' ');

        if (!text.trim()) {
            toast.error('Selected text is empty');
            return;
        }

        // If already editing a section, use that directly
        if (currentSectionId) {
            await createSubsectionForCurrentSection(editor, currentSectionId, currentDocumentId || null, fetchDocumentSections);
            return;
        }

        // If document is open, fetch sections and show dialog
        if (currentDocumentId) {
            const availableSections = await fetchAvailableSections(currentDocumentId);

            if (!availableSections || availableSections.length === 0) {
                toast.error('Please create a section first before adding subsections');
                return;
            }

            setSections(availableSections);
            setSelectedText(text);
            setShowSectionDialog(true);
        } else {
            toast.error('No document is currently open');
        }
    };

    const handleCreateQuestionFromSelection = async () => {
        if (!editor) return;

        const { from, to } = editor.state.selection;

        if (from === to) {
            toast.error('Please select some text first');
            return;
        }

        const text = editor.state.doc.textBetween(from, to, ' ');

        if (!text.trim()) {
            toast.error('Selected text is empty');
            return;
        }

        if (currentSubsectionId) {
            await createQuestionFromSelection(currentSubsectionId, 'subsection', text, currentDocumentId, fetchDocumentSections);
            return;
        }

        if (currentSectionId) {
            await createQuestionFromSelection(currentSectionId, 'section', text, currentDocumentId, fetchDocumentSections);
            return;
        }

        if (currentDocumentId) {
            try {
                const sectionsResponse = await fetchAvailableSections(currentDocumentId);

                if (!sectionsResponse || sectionsResponse.length === 0) {
                    toast.error('Please create a section first');
                    return;
                }

                const availableSections = sectionsResponse;

                const allSubsections: SubsectionOption[] = [];
                sectionsResponse.forEach((section) => {
                    if (section.subsections && section.subsections.length > 0) {
                        section.subsections.forEach((subsection) => {
                            allSubsections.push({
                                id: subsection.id,
                                title: subsection.title,
                                section_id: section.id,
                                section_title: section.title,
                                subsection_number: subsection.subsection_number,
                                section_number: section.section_number,
                            });
                        });
                    }
                });

                setSections(availableSections);
                setSubsections(allSubsections);
                setSelectedText(text);
                setShowQuestionDialog(true);
            } catch (error) {
                console.error('Error fetching sections:', error);
                toast.error('Failed to fetch sections');
            }
        } else {
            toast.error('No document is currently open');
        }
    };

    const handleSectionSelect = async (sectionId: number, subsectionNumber: number) => {
        await createSubsectionForSection(sectionId, selectedText, subsectionNumber, currentDocumentId, fetchDocumentSections);
    };

    const handleQuestionTargetSelect = async (targetId: number, targetType: 'section' | 'subsection') => {
        await createQuestionFromSelection(targetId, targetType, selectedText, currentDocumentId, fetchDocumentSections);
        setShowQuestionDialog(false);
    };

    return (
        <>
            <div className="flex flex-wrap items-center gap-4 rounded-md border-b border-gray-200 p-3">
                <div className="mr-2 flex flex-col items-center rounded-lg border-2 border-emerald-300 bg-gradient-to-b from-emerald-50 to-emerald-100 px-2 py-1 shadow-md">
                    <div className="mb-1 flex items-center gap-1">
                        <BookmarkIcon size={16} className="text-emerald-700" />
                        <span className="text-xs font-bold text-emerald-800">Document Structure</span>
                    </div>
                    <div className="flex items-center gap-1">
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={handleCreateSectionFromSelection}
                                    disabled={!hasTextSelection}
                                    className={`rounded-md p-2 text-sm font-medium shadow-sm transition-all ${
                                        hasTextSelection
                                            ? 'bg-emerald-600 text-white hover:bg-emerald-700 hover:shadow-md'
                                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                    }`}
                                >
                                    <FileTextIcon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>{hasTextSelection ? 'Create Section' : 'Select text to create section'}</p>
                            </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={handleCreateSubsectionFromSelection}
                                    disabled={!hasTextSelection}
                                    className={`rounded-md p-2 text-sm font-medium shadow-sm transition-all ${
                                        hasTextSelection
                                            ? 'bg-emerald-600 text-white hover:bg-emerald-700 hover:shadow-md'
                                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                    }`}
                                >
                                    <BookmarkIcon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>{hasTextSelection ? 'Create Subsection' : 'Select text to create subsection'}</p>
                            </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={handleCreateQuestionFromSelection}
                                    disabled={!hasTextSelection}
                                    className={`rounded-md p-2 text-sm font-medium shadow-sm transition-all ${
                                        hasTextSelection
                                            ? 'bg-emerald-600 text-white hover:bg-emerald-700 hover:shadow-md'
                                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                    }`}
                                >
                                    <HelpCircle size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>{hasTextSelection ? 'Create Question' : 'Select text to create question'}</p>
                            </TooltipContent>
                        </Tooltip>
                    </div>
                </div>

                <div className="mr-2 flex flex-col items-center">
                    <div className="mb-1 flex items-center gap-1">
                        <ListsIcon size={16} />
                        <span className="text-xs font-medium">Lists</span>
                    </div>
                    <div className="flex items-center gap-1">
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => editor.chain().focus().toggleBulletList().run()}
                                    className={toolbarButtonClass(editor.isActive('bulletList'))}
                                >
                                    <ListIcon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Bullet List</p>
                            </TooltipContent>
                        </Tooltip>
                        <OrderedListMenu editor={editor} />
                    </div>
                </div>

                <div className="mr-2 flex flex-col items-center">
                    <div className="mb-1 flex items-center gap-1">
                        <span className="text-xs font-medium">Table</span>
                    </div>
                    <div className="flex items-center">
                        <TableToolbar editor={editor} />
                    </div>
                </div>

                <div className="mr-2 flex flex-col items-center">
                    <div className="mb-1 flex items-center gap-1">
                        <span className="text-xs font-medium">History</span>
                    </div>
                    <div className="flex items-center gap-1">
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => editor.chain().focus().undo().run()}
                                    disabled={!editor.can().chain().focus().undo().run()}
                                    className={toolbarButtonClass(false)}
                                >
                                    <Undo2Icon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Undo</p>
                            </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => editor.chain().focus().redo().run()}
                                    disabled={!editor.can().chain().focus().redo().run()}
                                    className={toolbarButtonClass(false)}
                                >
                                    <Redo2Icon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Redo</p>
                            </TooltipContent>
                        </Tooltip>
                    </div>
                </div>

                <div className="mr-2 flex flex-col items-center">
                    <div className="mb-1 flex items-center gap-1">
                        <Heading1Icon size={16} />
                        <span className="text-xs font-medium">Headings</span>
                    </div>
                    <div className="flex items-center gap-1">
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
                                    className={toolbarButtonClass(editor.isActive('heading', { level: 1 }))}
                                >
                                    <Heading1Icon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Heading 1</p>
                            </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
                                    className={toolbarButtonClass(editor.isActive('heading', { level: 2 }))}
                                >
                                    <Heading2Icon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Heading 2</p>
                            </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
                                    className={toolbarButtonClass(editor.isActive('heading', { level: 3 }))}
                                >
                                    <Heading3Icon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Heading 3</p>
                            </TooltipContent>
                        </Tooltip>
                    </div>
                </div>

                <div className="mr-2 flex flex-col items-center">
                    <div className="mb-1 flex items-center gap-1">
                        <TypeIcon size={16} />
                        <span className="text-xs font-medium">Formatting</span>
                    </div>
                    <div className="flex items-center gap-1">
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => editor.chain().focus().toggleBold().run()}
                                    disabled={!editor.can().chain().focus().toggleBold().run()}
                                    className={toolbarButtonClass(editor.isActive('bold'))}
                                >
                                    <BoldIcon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Bold</p>
                            </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => editor.chain().focus().toggleItalic().run()}
                                    disabled={!editor.can().chain().focus().toggleItalic().run()}
                                    className={toolbarButtonClass(editor.isActive('italic'))}
                                >
                                    <ItalicIcon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Italic</p>
                            </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => editor.chain().focus().toggleStrike().run()}
                                    disabled={!editor.can().chain().focus().toggleStrike().run()}
                                    className={toolbarButtonClass(editor.isActive('strike'))}
                                >
                                    <StrikethroughIcon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Strikethrough</p>
                            </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => editor.chain().focus().toggleUnderline().run()}
                                    className={toolbarButtonClass(editor.isActive('underline'))}
                                >
                                    <UnderlineIcon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Underline</p>
                            </TooltipContent>
                        </Tooltip>
                    </div>
                </div>

                <div className="mr-2 flex flex-col items-center">
                    <div className="mb-1 flex items-center gap-1">
                        <LayoutIcon size={16} />
                        <span className="text-xs font-medium">Blocks</span>
                    </div>
                    <div className="flex items-center gap-1">
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => editor.chain().focus().toggleBlockquote().run()}
                                    className={toolbarButtonClass(editor.isActive('blockquote'))}
                                >
                                    <QuoteIcon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Blockquote</p>
                            </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => editor.chain().focus().toggleCodeBlock().run()}
                                    className={toolbarButtonClass(editor.isActive('codeBlock'))}
                                >
                                    <SquareCodeIcon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Code Block</p>
                            </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => editor.chain().focus().setHorizontalRule().run()}
                                    disabled={!editor.can().chain().focus().setHorizontalRule().run()}
                                    className={toolbarButtonClass(false)}
                                >
                                    <MinusIcon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Horizontal Rule</p>
                            </TooltipContent>
                        </Tooltip>
                    </div>
                </div>

                <div className="mr-2 flex flex-col items-center">
                    <div className="mb-1 flex items-center gap-1">
                        <span className="text-xs font-medium">Alignment</span>
                    </div>
                    <div className="flex items-center gap-1">
                        <FormatToolbar editor={editor} />
                    </div>
                </div>

                <div className="mr-2 flex flex-col items-center">
                    <div className="mb-1 flex items-center gap-1">
                        <span className="text-xs font-medium">Text Styles</span>
                    </div>
                    <div className="flex items-center gap-1">
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => editor.chain().focus().toggleHighlight().blur().run()}
                                    className={toolbarButtonClass(editor.isActive('highlight'))}
                                >
                                    <HighlighterIcon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Highlight</p>
                            </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => editor.chain().focus().toggleSubscript().run()}
                                    className={toolbarButtonClass(editor.isActive('subscript'))}
                                >
                                    <SubscriptIcon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Subscript</p>
                            </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => editor.chain().focus().toggleSuperscript().run()}
                                    className={toolbarButtonClass(editor.isActive('superscript'))}
                                >
                                    <SuperscriptIcon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Superscript</p>
                            </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => {
                                        const color = window.prompt('Enter color (e.g., #ff0000, red)');
                                        if (color) {
                                            editor.chain().focus().setColor(color).blur().run();
                                        } else if (editor.isActive('textStyle')) {
                                            editor.chain().focus().unsetMark('textStyle').blur().run();
                                        }
                                    }}
                                    className={toolbarButtonClass(editor.isActive('textStyle'))}
                                >
                                    <PaletteIcon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Text Color</p>
                            </TooltipContent>
                        </Tooltip>
                    </div>
                </div>

                <div className="mr-2 flex flex-col items-center">
                    <div className="mb-1 flex items-center gap-1">
                        <span className="text-xs font-medium">Others</span>
                    </div>
                    <div className="flex items-center gap-1">
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => {
                                        const url = window.prompt('Enter URL');
                                        if (url) {
                                            editor.chain().focus().setLink({ href: url }).run();
                                        } else if (editor.isActive('link')) {
                                            editor.chain().focus().unsetLink().run();
                                        }
                                    }}
                                    className={toolbarButtonClass(editor.isActive('link'))}
                                >
                                    <LinkIcon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Link</p>
                            </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <button
                                    type="button"
                                    onClick={() => editor.chain().focus().setHardBreak().run()}
                                    className={toolbarButtonClass(false)}
                                >
                                    <WrapTextIcon size={18} />
                                </button>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Hard Break</p>
                            </TooltipContent>
                        </Tooltip>
                        <ImageToolbar editor={editor} />
                    </div>
                </div>
            </div>

            <SectionSelectionDialog
                isOpen={showSectionDialog}
                onClose={() => setShowSectionDialog(false)}
                onSelect={handleSectionSelect}
                sections={sections}
                title={selectedText}
            />

            {showQuestionDialog && (
                <SectionSubsectionSelectionDialog
                    isOpen={showQuestionDialog}
                    onClose={() => setShowQuestionDialog(false)}
                    onSelect={handleQuestionTargetSelect}
                    sections={sections}
                    subsections={subsections}
                    title={selectedText}
                />
            )}
        </>
    );
};

export default EditorToolbar;
