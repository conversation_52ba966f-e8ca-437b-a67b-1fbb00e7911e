import TableDataDialog from './components/table-data/TableDataDialog';
import { Button } from '@/components/ui/button';
import Blockquote from '@tiptap/extension-blockquote';
import Bold from '@tiptap/extension-bold';
import BulletList from '@tiptap/extension-bullet-list';
import CodeBlock from '@tiptap/extension-code-block';
import Color from '@tiptap/extension-color';
import Document from '@tiptap/extension-document';
import Dropcursor from '@tiptap/extension-dropcursor';
import FontFamily from '@tiptap/extension-font-family';
import Gapcursor from '@tiptap/extension-gapcursor';
import HardBreak from '@tiptap/extension-hard-break';
import Heading from '@tiptap/extension-heading';
import Highlight from '@tiptap/extension-highlight';
import History from '@tiptap/extension-history';
import HorizontalRule from '@tiptap/extension-horizontal-rule';
import Image from '@tiptap/extension-image';
import Italic from '@tiptap/extension-italic';
import Link from '@tiptap/extension-link';
import ListItem from '@tiptap/extension-list-item';
import Paragraph from '@tiptap/extension-paragraph';
import Strike from '@tiptap/extension-strike';
import Subscript from '@tiptap/extension-subscript';
import Superscript from '@tiptap/extension-superscript';
import Table from '@tiptap/extension-table';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import TableRow from '@tiptap/extension-table-row';
import Text from '@tiptap/extension-text';
import TextAlign from '@tiptap/extension-text-align';
import TextStyle from '@tiptap/extension-text-style';
import Underline from '@tiptap/extension-underline';
import { Node as ProseMirrorNode } from '@tiptap/pm/model';
import { Editor, EditorContent, useEditor } from '@tiptap/react';
import axios from 'axios';
import { ArrowUpIcon, Edit2, FileDown, FileText, List, Save, X } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';
import { SectionsListDialog } from '../components/SectionsListDialog';
import { useDocumentContext } from '../context/DocumentContext';
import styles from './editor-content.module.css';
import EditorToolbar from './EditorToolbar';
import CustomOrderedList from './extensions/CustomOrderedList';
import TableBubbleMenus from './table-bubble-menus/TableBubbleMenus';
import TableStructureViewer from './table-bubble-menus/TableStructureViewer';

interface TiptapEditorProps {
    content?: string | object;
    onEditorReady?: (editor: Editor) => void;
    initialContent?: string;
    showBubbleMenus?: boolean;
    viewTableDataCallback?: () => void;
    useTableDataDialog?: boolean;
    onUpdate?: () => void;
    currentSectionId?: number | null;
    currentSubsectionId?: number | null;
    onSave?: (content: { html: string; json: object }) => void;
}

export default function TiptapEditor({
    content,
    onEditorReady,
    initialContent,
    showBubbleMenus = true,
    viewTableDataCallback,
    useTableDataDialog = false,
    onUpdate,
    currentSectionId,
    currentSubsectionId,
    onSave,
}: TiptapEditorProps) {
    const { parsedSections, handleOpenSection, handleOpenSubsection, currentDocumentId } = useDocumentContext();
    // UI state
    const [showBackToTop, setShowBackToTop] = useState(false);
    const [isExporting, setIsExporting] = useState(false);
    const [documentName, setDocumentName] = useState('Document name');
    const [isEditingName, setIsEditingName] = useState(false);
    const [tempDocumentName, setTempDocumentName] = useState('');

    // Table related state
    const [showTableMenu, setShowTableMenu] = useState(false);
    const [tablePosition, setTablePosition] = useState({ top: 0, left: 0 });
    const [isTableDataDialogOpen, setIsTableDataDialogOpen] = useState(false);
    const [isTableStructureDialogOpen, setIsTableStructureDialogOpen] = useState(false);
    const [selectedTableNode, setSelectedTableNode] = useState<ProseMirrorNode | null>(null);

    // Sections dialog state
    const [isSectionsDialogOpen, setIsSectionsDialogOpen] = useState(false);

    // Content tracking state
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
    const contentRef = useRef<string | object | null>(null);
    const isInitializing = useRef(true);

    // Refs
    const editorContainerRef = useRef<HTMLDivElement>(null);

    const editor = useEditor({
        extensions: [
            Document,
            Paragraph,
            Text,
            Bold,
            Italic,
            Strike,
            Heading.configure({ levels: [1, 2, 3] }),
            BulletList,
            ListItem,
            Blockquote,
            CodeBlock,
            HardBreak,
            HorizontalRule,
            CustomOrderedList,
            Table.configure({
                resizable: true,
            }),
            TableRow,
            TableHeader,
            TableCell,
            Image.configure({
                HTMLAttributes: {
                    class: 'editor-image',
                },
                allowBase64: true,
            }),
            FontFamily,
            TextAlign.configure({
                types: ['heading', 'paragraph'],
                alignments: ['left', 'center', 'right', 'justify'],
                defaultAlignment: 'left',
            }),
            Dropcursor,
            Gapcursor,
            History,
            Highlight,
            Link.configure({
                openOnClick: true,
                linkOnPaste: true,
            }),
            Subscript,
            Superscript,
            TextStyle,
            Color,
            Underline,
        ],
        content:
            initialContent ||
            (content
                ? typeof content === 'string'
                    ? content
                    : ''
                : '<ol type="1"><li><p>test 1</p></li></ol><p>content test 1</p><ol start="2" type="1"><li><p>test 2</p><ol type="a"><li><p>test 2.1</p></li></ol><ul><li><p>content test 2.1</p></li></ul><ol start="2" type="a"><li><p>test 2.2</p></li></ol><ul><li><p>content test 2.2</p></li></ul></li><li><p>test 3</p></li></ol><p>content test 3</p>'),
        injectCSS: false,
        onSelectionUpdate: ({ editor }) => {
            setShowTableMenu(editor.isActive('table'));
        },
        onFocus: ({ editor }) => {
            setShowTableMenu(editor.isActive('table'));
        },
        onUpdate: () => {
            if (!isInitializing.current) {
                if (contentRef.current && editor) {
                    contentRef.current = editor.getJSON();
                }
                setHasUnsavedChanges(true);
                if (onUpdate) onUpdate();
            }
        },
        editorProps: {
            attributes: {
                class: `m-5 focus:outline-none border border-gray-300 p-4 rounded-md min-h-[200px]`,
            },
            handleKeyDown: (view, event): boolean => {
                if (event.key === 'Tab') {
                    event.preventDefault();

                    const { state } = view;
                    const editorInstance = editor;
                    const inOrderedList = editorInstance?.isActive('orderedList');
                    const inBulletList = editorInstance?.isActive('bulletList');

                    if (inOrderedList || inBulletList) {
                        if (!event.shiftKey) {
                            return editorInstance?.chain().focus().sinkListItem('listItem').run() || true;
                        } else {
                            return editorInstance?.chain().focus().liftListItem('listItem').run() || true;
                        }
                    } else {
                        const { dispatch } = view;
                        const text = '\t';
                        dispatch(state.tr.insertText(text));
                        return true;
                    }
                }
                return false;
            },
        },
    });

    // Handle scroll and back to top button
    useEffect(() => {
        const handleScroll = () => {
            setShowBackToTop(window.scrollY > 400);
        };

        window.addEventListener('scroll', handleScroll);
        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);

    // Handle table menu positioning
    useEffect(() => {
        if (editor && showTableMenu) {
            const updateTableMenuPosition = () => {
                const { from } = editor.state.selection;
                let activeTable: HTMLElement | null = null;

                const pos = editor.view.domAtPos(from);

                let domNode: Element | null = pos.node as Element;
                while (domNode && !activeTable) {
                    if (domNode.nodeName === 'TABLE') {
                        activeTable = domNode as HTMLElement;
                    }
                    domNode = domNode.parentElement;
                }

                if (activeTable) {
                    const rect = activeTable.getBoundingClientRect();
                    setTablePosition({
                        top: rect.top - 50,
                        left: rect.left,
                    });
                }
            };

            editor.on('selectionUpdate', updateTableMenuPosition);
            updateTableMenuPosition();

            window.addEventListener('scroll', updateTableMenuPosition);
            window.addEventListener('resize', updateTableMenuPosition);

            return () => {
                editor.off('selectionUpdate', updateTableMenuPosition);
                window.removeEventListener('scroll', updateTableMenuPosition);
                window.removeEventListener('resize', updateTableMenuPosition);
            };
        }
    }, [showTableMenu, editor]);

    // Handle editor ready and content initialization
    useEffect(() => {
        if (editor && onEditorReady) {
            onEditorReady(editor);
        }
    }, [editor, onEditorReady]);

    // Single unified effect for content updates
    useEffect(() => {
        if (!editor || !content) return;

        // Skip if this is the same content reference we last tracked
        if (contentRef.current === content) return;

        const currentContent = editor?.getJSON();
        const newContent = typeof content === 'string' ? JSON.parse(content) : content;

        // Update our content ref to track this version
        contentRef.current = content;

        // Only update if content is actually different
        if (JSON.stringify(currentContent) !== JSON.stringify(newContent)) {
            isInitializing.current = true;

            // Set the new content
            editor?.commands.setContent(newContent, false);
            setHasUnsavedChanges(false);

            // Reset the initializing flag after content is processed
            setTimeout(() => {
                isInitializing.current = false;
            }, 100);
        }
    }, [content, editor]);

    // Helper functions
    const scrollToTop = () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    // Table operations
    const addColumnBefore = () => editor?.chain().focus().addColumnBefore().run();
    const addColumnAfter = () => editor?.chain().focus().addColumnAfter().run();
    const deleteColumn = () => editor?.chain().focus().deleteColumn().run();
    const addRowBefore = () => editor?.chain().focus().addRowBefore().run();
    const addRowAfter = () => editor?.chain().focus().addRowAfter().run();
    const deleteRow = () => editor?.chain().focus().deleteRow().run();

    const deleteTable = () => {
        editor?.chain().focus().deleteTable().run();
        setShowTableMenu(false);
    };

    const addNestedTable = () => {
        if (editor && editor.isActive('tableCell')) {
            editor.chain().focus().insertTable({ rows: 2, cols: 2, withHeaderRow: true }).run();
        } else {
            alert('Please place cursor inside a table cell first');
        }
    };

    const viewTableData = () => {
        if (!editor) return;

        const { from } = editor.state.selection;
        let tableNode: ProseMirrorNode | null = null;

        editor.state.doc.nodesBetween(from, from, (node) => {
            if (node.type.name === 'table' && !tableNode) {
                tableNode = node;
                return false;
            }
            return true;
        });

        if (tableNode) {
            setShowTableMenu(false);
            setSelectedTableNode(tableNode);
            setIsTableDataDialogOpen(true);
        }
    };

    const viewTableStructure = () => {
        if (!editor) return;

        const { from } = editor.state.selection;
        let tableNode: ProseMirrorNode | null = null;

        editor.state.doc.nodesBetween(from, from, (node) => {
            if (node.type.name === 'table' && !tableNode) {
                tableNode = node;
                return false;
            }
            return true;
        });

        if (tableNode) {
            setShowTableMenu(false);
            setSelectedTableNode(tableNode);
            setIsTableStructureDialogOpen(true);
        }
    };

    // Save content function
    const saveContent = () => {
        if (!editor || !onSave) return;

        const json = editor.getJSON();
        const html = editor.getHTML();

        // Update our content tracking reference
        contentRef.current = json;

        onSave({
            html,
            json,
        });

        setHasUnsavedChanges(false);
    };

    // Export as DOCX
    const handleExport = async () => {
        if (!editor) return;

        setIsExporting(true);
        try {
            const html = editor.getHTML();

            const response = await axios.post(
                route('convert.html-to-docx'),
                {
                    html: html,
                    filename: documentName,
                },
                {
                    responseType: 'blob',
                },
            );

            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', `${documentName}.docx`);
            document.body.appendChild(link);
            link.click();
            link.remove();

            toast.success('Template exported as DOCX!');
        } catch (error) {
            console.error('Error exporting template:', error);
            toast.error('Failed to export template');
        } finally {
            setIsExporting(false);
        }
    };

    if (!editor) {
        return null;
    }

    return (
        <div className="relative w-full">
            <div className="sticky top-0 z-40 bg-white shadow-sm">
                <div className="flex items-center justify-start border-b border-gray-200 px-4 py-3">
                    <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                            <div className="flex items-center gap-2">
                                <FileText className="h-5 w-5 text-blue-500" />
                                {!isEditingName ? (
                                    <div className="group flex items-center gap-2">
                                        <span className="text-sm font-medium">{documentName || 'Document name'}</span>
                                        <button
                                            onClick={() => {
                                                setIsEditingName(true);
                                                setTempDocumentName(documentName);
                                            }}
                                            className="rounded-full p-1 text-gray-500 transition-colors hover:bg-gray-100 hover:text-gray-700"
                                            title="Rename document"
                                        >
                                            <Edit2 className="h-3.5 w-3.5" />
                                        </button>
                                    </div>
                                ) : (
                                    <div className="flex items-center space-x-1">
                                        <input
                                            id="documentName"
                                            type="text"
                                            value={tempDocumentName}
                                            onChange={(e) => setTempDocumentName(e.target.value)}
                                            className="w-64 rounded border border-gray-300 px-2 py-1 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
                                            placeholder="Document name"
                                            autoFocus
                                            onKeyDown={(e) => {
                                                if (e.key === 'Enter') {
                                                    if (tempDocumentName.trim()) {
                                                        setDocumentName(tempDocumentName);
                                                        setIsEditingName(false);
                                                    }
                                                } else if (e.key === 'Escape') {
                                                    setIsEditingName(false);
                                                }
                                            }}
                                        />
                                        <button
                                            onClick={() => {
                                                if (tempDocumentName.trim()) {
                                                    setDocumentName(tempDocumentName);
                                                    setIsEditingName(false);
                                                }
                                            }}
                                            className="rounded-full p-1.5 text-green-500 transition-colors hover:bg-green-50 hover:text-green-700"
                                            title="Save name"
                                            disabled={!tempDocumentName.trim()}
                                        >
                                            <Save className="h-3.5 w-3.5" />
                                        </button>
                                        <button
                                            onClick={() => setIsEditingName(false)}
                                            className="rounded-full p-1.5 text-gray-500 transition-colors hover:bg-gray-100 hover:text-gray-700"
                                            title="Cancel"
                                        >
                                            <X className="h-3.5 w-3.5" />
                                        </button>
                                    </div>
                                )}
                            </div>
                        </div>
                        <Button onClick={handleExport} disabled={isExporting || !editor} variant="default" className="flex h-9 items-center gap-2">
                            <FileDown className="h-4 w-4" />
                            {isExporting ? 'Exporting...' : 'Export as DOCX'}
                        </Button>
                        <Button
                            onClick={() => console.log(editor.getHTML())}
                            disabled={!editor}
                            variant="outline"
                            className="ml-2 flex h-9 items-center gap-2"
                        >
                            Log HTML
                        </Button>
                    </div>
                </div>
                <EditorToolbar editor={editor} currentSectionId={currentSectionId} currentSubsectionId={currentSubsectionId} />
            </div>

            <div className="pt-4">
                <div ref={editorContainerRef} className={styles.editorContent}>
                    <EditorContent editor={editor} />
                </div>

                {showBackToTop && (
                    <button
                        type="button"
                        onClick={scrollToTop}
                        className="fixed right-6 bottom-6 z-50 rounded-full bg-slate-800 p-3 text-white shadow-lg transition-all hover:bg-slate-700"
                        title="Back to Top"
                    >
                        <ArrowUpIcon size={20} />
                    </button>
                )}

                {editor && (
                    <>
                        <button
                            type="button"
                            onClick={() => setIsSectionsDialogOpen(true)}
                            className="fixed right-6 bottom-20 z-50 rounded-full bg-blue-600 p-3 text-white shadow-lg transition-all hover:bg-blue-700"
                            title="View Sections"
                        >
                            <List size={20} />
                        </button>

                        <SectionsListDialog
                            isOpen={isSectionsDialogOpen}
                            onClose={() => setIsSectionsDialogOpen(false)}
                            sections={parsedSections || []}
                            onOpenSection={handleOpenSection}
                            onOpenSubsection={handleOpenSubsection}
                        />
                    </>
                )}

                {showBubbleMenus && editor && (
                    <TableBubbleMenus
                        editor={editor}
                        showTableMenu={showTableMenu}
                        tablePosition={tablePosition}
                        addColumnBefore={addColumnBefore}
                        addColumnAfter={addColumnAfter}
                        deleteColumn={deleteColumn}
                        addRowBefore={addRowBefore}
                        addRowAfter={addRowAfter}
                        deleteRow={deleteRow}
                        deleteTable={deleteTable}
                        addNestedTable={addNestedTable}
                        isEditable={true}
                        viewTableData={useTableDataDialog ? viewTableData : viewTableDataCallback}
                        viewTableStructure={viewTableStructure}
                    />
                )}

                {useTableDataDialog && isTableDataDialogOpen && (
                    <TableDataDialog
                        isOpen={isTableDataDialogOpen}
                        onClose={() => setIsTableDataDialogOpen(false)}
                        tableNode={selectedTableNode}
                        editor={editor}
                    />
                )}

                {isTableStructureDialogOpen && (
                    <TableStructureViewer
                        isOpen={isTableStructureDialogOpen}
                        onClose={() => setIsTableStructureDialogOpen(false)}
                        tableNode={selectedTableNode}
                        documentId={currentDocumentId}
                    />
                )}

                {hasUnsavedChanges && onSave && (
                    <div className="animate-in slide-in-from-bottom fixed right-0 bottom-8 left-0 z-50 flex justify-center duration-300">
                        <Button
                            onClick={saveContent}
                            className="flex items-center gap-2 rounded-full bg-green-600 px-6 py-3 text-lg font-medium shadow-lg hover:bg-green-700"
                        >
                            <Save className="h-5 w-5" />
                            Enregistrer les modifications
                        </Button>
                    </div>
                )}
            </div>
        </div>
    );
}
