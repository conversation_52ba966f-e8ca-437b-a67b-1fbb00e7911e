import { Editor } from '@tiptap/react';
import { useState, useRef, useEffect } from 'react';
import {
    ImageIcon,
    AlignLeftIcon,
    AlignCenterIcon,
    AlignRightIcon,
    ChevronDownIcon,
    UploadIcon,
    LinkIcon,
    Type as CaptionIcon
} from 'lucide-react';

interface ImageToolbarProps {
    editor: Editor;
}

const ImageToolbar = ({ editor }: ImageToolbarProps) => {
    const [showImageOptions, setShowImageOptions] = useState(false);
    const imageMenuRef = useRef<HTMLDivElement>(null);
    
    // Close image dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (imageMenuRef.current && !imageMenuRef.current.contains(event.target as Node)) {
                setShowImageOptions(false);
            }
        };
        
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);
    
    const toolbarButtonClass = (isActive: boolean) =>
        `p-2 text-sm font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
            isActive ? 'bg-slate-900 text-slate-50 hover:bg-slate-900/90' : 'bg-slate-100 text-slate-900 hover:bg-slate-100/80'
        }`;

    const insertImage = (url: string, alt: string = '') => {
        if (url) {
            editor.chain().focus().setImage({ 
                src: url,
                alt,
                title: alt
            }).run();
        }
    };

    const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;
        
        // In a real application, you would upload this file to your server or a CDN
        // For demo purposes, we'll create a local object URL
        const objectUrl = URL.createObjectURL(file);
        insertImage(objectUrl, file.name);
        setShowImageOptions(false);
        
        // Reset the input value to allow uploading the same file again
        event.target.value = '';
    };
    
    const handleImageUrl = () => {
        const url = window.prompt('Enter image URL');
        if (url) {
            const alt = window.prompt('Enter image description (alt text)') || '';
            insertImage(url, alt);
            setShowImageOptions(false);
        }
    };

    // Check if current selection is an image
    const isImageSelected = () => editor.isActive('image');
    
    // Image alignment functions
    const setImageAlignment = (alignment: 'left' | 'center' | 'right') => {
        // Just update the attributes if an image is selected
        editor.chain().focus().updateAttributes('image', {
            'data-align': alignment
        }).run();
    };
    
    // Get current image alignment
    const getImageAlignment = (): 'left' | 'center' | 'right' => {
        if (!isImageSelected()) return 'center'; // Default
        
        const attrs = editor.getAttributes('image');
        return (attrs['data-align'] as 'left' | 'center' | 'right') || 'center';
    };

    // Add caption to image
    const addCaption = () => {
        if (!isImageSelected()) return;
        
        const caption = window.prompt('Enter image caption');
        if (caption !== null) {
            editor.chain().focus().updateAttributes('image', {
                'data-caption': caption
            }).run();
        }
    };

    return (
        <div className="relative" ref={imageMenuRef}>
            <button
                type="button"
                title="Image Options"
                onClick={() => setShowImageOptions(!showImageOptions)}
                className={toolbarButtonClass(editor.isActive('image'))}
            >
                <div className="flex items-center">
                    <ImageIcon size={18} />
                    <ChevronDownIcon size={14} className="ml-1" />
                </div>
            </button>
            
            {showImageOptions && (
                <div className="absolute top-full left-0 mt-1 bg-white shadow-lg rounded-md border border-gray-200 p-2 z-10 w-56">
                    <div className="grid grid-cols-2 gap-1 mb-2">
                        <label
                            className="flex items-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100 cursor-pointer"
                        >
                            <UploadIcon size={16} className="mr-1" />
                            <span>Upload Image</span>
                            <input 
                                type="file"
                                accept="image/*"
                                className="hidden"
                                onChange={handleImageUpload}
                            />
                        </label>
                        <button
                            type="button"
                            title="Image URL"
                            onClick={handleImageUrl}
                            className="flex items-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100"
                        >
                            <LinkIcon size={16} className="mr-1" />
                            <span>Image URL</span>
                        </button>
                    </div>
                    
                    {isImageSelected() && (
                        <>
                            <div className="border-t border-gray-200 my-1 pt-1">
                                <div className="text-xs font-semibold text-gray-500 mb-1 px-1">ALIGNMENT</div>
                                <div className="grid grid-cols-3 gap-1 mb-2">
                                    <button
                                        type="button"
                                        title="Align Left"
                                        onClick={() => setImageAlignment('left')}
                                        className={`flex items-center justify-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100 ${
                                            getImageAlignment() === 'left' ? 'bg-gray-200' : ''
                                        }`}
                                    >
                                        <AlignLeftIcon size={16} />
                                    </button>
                                    <button
                                        type="button"
                                        title="Align Center"
                                        onClick={() => setImageAlignment('center')}
                                        className={`flex items-center justify-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100 ${
                                            getImageAlignment() === 'center' ? 'bg-gray-200' : ''
                                        }`}
                                    >
                                        <AlignCenterIcon size={16} />
                                    </button>
                                    <button
                                        type="button"
                                        title="Align Right"
                                        onClick={() => setImageAlignment('right')}
                                        className={`flex items-center justify-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100 ${
                                            getImageAlignment() === 'right' ? 'bg-gray-200' : ''
                                        }`}
                                    >
                                        <AlignRightIcon size={16} />
                                    </button>
                                </div>
                            </div>
                            
                            <div className="border-t border-gray-200 my-1 pt-1">
                                <div className="text-xs font-semibold text-gray-500 mb-1 px-1">OPTIONS</div>
                                <div className="grid grid-cols-1 gap-1 mb-1">
                                    <button
                                        type="button"
                                        title="Add Caption"
                                        onClick={addCaption}
                                        className="flex items-center p-1.5 text-sm font-medium rounded-md transition-colors hover:bg-gray-100"
                                    >
                                        <CaptionIcon size={16} className="mr-1" />
                                        <span>Add Caption</span>
                                    </button>
                                </div>
                            </div>
                        </>
                    )}
                </div>
            )}
        </div>
    );
};

export default ImageToolbar;
