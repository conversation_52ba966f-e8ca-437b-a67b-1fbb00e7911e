import OrderedList from '@tiptap/extension-ordered-list'
import { mergeAttributes, Command } from '@tiptap/core'

// Extend the default OrderedList extension to add custom functionality
const CustomOrderedList = OrderedList.extend({
  addAttributes() {
    return {
      // Keep the original start attribute
      start: {
        default: 1,
        parseHTML: element => {
          return element.getAttribute('start') ? parseInt(element.getAttribute('start') || '1', 10) : 1
        },
        renderHTML: attributes => {
          if (attributes.start === 1) {
            return {}
          }
          
          return { start: attributes.start }
        },
      },
    }
  },
  
  addCommands() {
    return {
      // Add the original commands
      ...this.parent?.(),
      
      // Add a new command to set the start attribute
      setOrderedListStart: (start: number): Command => ({ commands }) => {
        return commands.updateAttributes('orderedList', { start })
      },
    }
  },
  
  // Override the default renderHTML to ensure the start attribute is properly rendered
  renderHTML({ HTMLAttributes }) {
    return ['ol', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]
  },
})

export default CustomOrderedList
