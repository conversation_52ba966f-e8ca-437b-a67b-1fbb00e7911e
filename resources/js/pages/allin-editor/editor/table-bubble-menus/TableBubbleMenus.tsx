import { Editor } from '@tiptap/react';
import React, { useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import styles from './TableBubbleMenus.module.css';

interface TableBubbleMenusProps {
    editor: Editor | null;
    showTableMenu: boolean;
    tablePosition: { top: number; left: number };
    addColumnBefore: () => void;
    addColumnAfter: () => void;
    deleteColumn: () => void;
    addRowBefore: () => void;
    addRowAfter: () => void;
    deleteRow: () => void;
    deleteTable: () => void;
    addNestedTable: () => void;
    isEditable: boolean;
    viewTableData?: () => void;
    viewTableStructure?: () => void;
}

const TableBubbleMenus: React.FC<TableBubbleMenusProps> = ({
    editor,
    showTableMenu,
    tablePosition,
    addColumnBefore,
    addColumnAfter,
    deleteColumn,
    addRowBefore,
    addRowAfter,
    deleteRow,
    deleteTable,
    addNestedTable,
    isEditable,
    viewTableData,
    viewTableStructure,
}) => {
    const [showTextMenu, setShowTextMenu] = useState(false);
    const [textMenuPosition, setTextMenuPosition] = useState({ top: 0, left: 0 });

    const textMenuRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (!editor || !isEditable) {
            setShowTextMenu(false);
            return;
        }

        const updateMenuState = () => {
            const isTable = editor.isActive('table');
            const hasSelection = !editor.view.state.selection.empty;

            setShowTextMenu(!isTable && hasSelection);

            if (!isTable && hasSelection) {
                updateTextMenuPosition();
            }
        };

        const updateTextMenuPosition = () => {
            const { view } = editor;
            const { state } = view;
            const { from, to } = state.selection;

            const start = view.coordsAtPos(from);
            const end = view.coordsAtPos(to);

            if (start && end) {
                const menuWidth = textMenuRef.current?.offsetWidth || 0;
                const selectionCenter = (start.left + end.left) / 2;

                setTextMenuPosition({
                    top: start.top - (textMenuRef.current?.offsetHeight || 0) - 10,
                    left: selectionCenter - menuWidth / 2,
                });
            }
        };

        editor.on('selectionUpdate', updateMenuState);

        updateMenuState();

        return () => {
            editor.off('selectionUpdate', updateMenuState);
        };
    }, [editor, isEditable]);

    if (!editor) {
        return null;
    }

    return (
        <>
            {showTableMenu &&
                ReactDOM.createPortal(
                    <div
                        className={`${styles.tableMenu} ${styles.floating}`}
                        style={{
                            position: 'fixed',
                            top: `${tablePosition.top}px`,
                            left: `${tablePosition.left}px`,
                            zIndex: 100,
                        }}
                    >
                        {viewTableData && (
                            <div className={`${styles.tableMenuGroup} ${styles.viewDataGroup}`}>
                                <button onClick={viewTableData} title="View Table Data" className={styles.viewDataButton}>
                                    <span className={styles.viewDataIcon}>📋</span>
                                    <span className={styles.viewDataText}>Manage Table</span>
                                </button>
                                <button onClick={viewTableStructure} title="View Table Structure" className={styles.viewTableButton}>
                                    <span className={styles.viewTableIcon}>🔍</span>
                                    <span className={styles.viewTableText}>View Table</span>
                                </button>
                            </div>
                        )}
                        <div className={styles.tableMenuGroup}>
                            <button onClick={addColumnBefore} title="Add Column Before" disabled={!isEditable}>
                                ◀️ Col
                            </button>
                            <button onClick={addColumnAfter} title="Add Column After" disabled={!isEditable}>
                                Col ▶️
                            </button>
                            <button onClick={deleteColumn} title="Delete Column" disabled={!isEditable}>
                                ❌ Col
                            </button>
                        </div>
                        <div className={styles.tableMenuGroup}>
                            <button onClick={addRowBefore} title="Add Row Before" disabled={!isEditable}>
                                ⬆️ Row
                            </button>
                            <button onClick={addRowAfter} title="Add Row After" disabled={!isEditable}>
                                Row ⬇️
                            </button>
                            <button onClick={deleteRow} title="Delete Row" disabled={!isEditable}>
                                ❌ Row
                            </button>
                        </div>
                        <div className={styles.tableMenuGroup}>
                            <button onClick={addNestedTable} title="Insert a table inside the current cell" disabled={!isEditable}>
                                Nest Table
                            </button>
                            <button onClick={deleteTable} title="Delete Table" disabled={!isEditable}>
                                Delete Table
                            </button>
                        </div>
                    </div>,
                    document.body,
                )}

            {isEditable &&
                showTextMenu &&
                ReactDOM.createPortal(
                    <div
                        ref={textMenuRef}
                        className={styles.bubbleMenu}
                        style={{
                            position: 'fixed',
                            zIndex: 50,
                            top: `${textMenuPosition.top}px`,
                            left: `${textMenuPosition.left}px`,
                        }}
                    >
                        <button onClick={() => editor.chain().focus().toggleBold().run()}>B</button>
                        <button onClick={() => editor.chain().focus().toggleItalic().run()}>I</button>
                        <button onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}>H1</button>
                        <button onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}>H2</button>
                        <button onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}>H3</button>
                    </div>,
                    document.body,
                )}
        </>
    );
};

export default TableBubbleMenus;
