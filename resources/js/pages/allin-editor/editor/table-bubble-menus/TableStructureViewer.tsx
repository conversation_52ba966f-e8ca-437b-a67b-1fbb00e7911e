import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Trash2, Edit } from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Question } from '../../services/questionService';
import { Parameter, createParameter, updateParameter as updateParameterAPI, deleteParameter as deleteParameterAPI, getParametersByQuestion } from '../../services/parameterService';
import QuestionSelectionDialog from './QuestionSelectionDialog';

interface CellData {
    content: string;
    attrs: Record<string, unknown>;
    colspan: number;
    rowspan: number;
    type: string;
}

interface TableData {
    rows: CellData[][];
    rowCount: number;
    colCount: number;
}

interface HeaderColumn {
    columnIndex: number;
    headers: string[];
}

interface ProseMirrorNodeLike {
    type: { name: string };
    attrs?: Record<string, unknown>;
    content?: unknown;
    forEach: (callback: (node: ProseMirrorNodeLike, index: number) => void) => void;
    textContent?: string;
    childCount?: number;
}

interface TableStructureViewerProps {
    isOpen: boolean;
    onClose: () => void;
    tableNode: ProseMirrorNodeLike | null;
    documentId: number | null;
}

const TableStructureViewer: React.FC<TableStructureViewerProps> = ({ isOpen, onClose, tableNode, documentId }) => {
    const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null);
    const [headerOrientation, setHeaderOrientation] = useState<'horizontal' | 'vertical'>('vertical');
    const [isSaving, setIsSaving] = useState(false);
    const [showQuestionDialog, setShowQuestionDialog] = useState(false);
    const [existingParameters, setExistingParameters] = useState<Parameter[]>([]);
    const [isLoadingParameters, setIsLoadingParameters] = useState(false);
    const [editingParameter, setEditingParameter] = useState<Parameter | null>(null);
    const [showExistingParameters, setShowExistingParameters] = useState(false);

    const extractTableData = (): TableData => {
        if (!tableNode) {
            return { rows: [], rowCount: 0, colCount: 0 };
        }

        const tableData: TableData = {
            rows: [],
            rowCount: 0,
            colCount: 0,
        };

        tableNode.forEach((rowNode: ProseMirrorNodeLike, rowIndex: number) => {
            const row: CellData[] = [];
            tableData.rowCount++;

            rowNode.forEach((cellNode: ProseMirrorNodeLike) => {
                let cellContent = '';
                cellNode.forEach((contentNode: ProseMirrorNodeLike) => {
                    cellContent += contentNode.textContent || '';
                });

                const cellAttrs = cellNode.attrs || {};
                const cellType = cellNode.type.name;

                row.push({
                    content: cellContent,
                    attrs: cellAttrs,
                    colspan: typeof cellAttrs.colspan === 'number' ? cellAttrs.colspan : 1,
                    rowspan: typeof cellAttrs.rowspan === 'number' ? cellAttrs.rowspan : 1,
                    type: cellType,
                });
            });

            if (rowIndex === 0) {
                tableData.colCount = rowNode.childCount || 0;
            }
            tableData.rows.push(row);
        });

        return tableData;
    };

    const tableData = extractTableData();

    // Fetch existing parameters for the selected question
    const fetchExistingParameters = async (questionId: number) => {
        setIsLoadingParameters(true);
        try {
            const parameters = await getParametersByQuestion(questionId);
            setExistingParameters(parameters);
        } catch (error) {
            console.error('Error fetching parameters:', error);
            toast.error('Failed to load existing parameters');
        } finally {
            setIsLoadingParameters(false);
        }
    };

    // Load existing parameters when question is selected
    useEffect(() => {
        if (selectedQuestion?.id) {
            fetchExistingParameters(selectedQuestion.id);
        } else {
            setExistingParameters([]);
        }
    }, [selectedQuestion]);

    // Delete a parameter
    const deleteParameter = async (parameterId: number) => {
        try {
            await deleteParameterAPI(parameterId);
            toast.success('Parameter deleted successfully');
            if (selectedQuestion?.id) {
                fetchExistingParameters(selectedQuestion.id);
            }
        } catch (error) {
            console.error('Error deleting parameter:', error);
            toast.error('Failed to delete parameter');
        }
    };

    // Load parameter for editing
    const loadParameterForEditing = (parameter: Parameter) => {
        setEditingParameter(parameter);
        setHeaderOrientation(parameter.header_orientation);
        setShowExistingParameters(false);
    };

    const saveTableData = async () => {
        if (!selectedQuestion) {
            toast.error('Please select a question');
            return;
        }

        setIsSaving(true);

        try {
            const allData = tableData.rows.map((row) => row.map((cell) => cell.content));

            let headers: string[] | HeaderColumn[] = [];
            let values: Array<Array<string>> = [];

            if (headerOrientation === 'horizontal' && allData.length > 0) {
                headers = allData[0];
                values = allData.slice(1);
            } else if (headerOrientation === 'vertical') {
                const detectedHeaders: HeaderColumn[] = [];
                const columnCount = allData[0]?.length || 0;

                for (let colIndex = 0; colIndex < columnCount; colIndex++) {
                    const columnValues = allData.map((row) => row[colIndex]);

                    const isLikelyHeader =
                        columnValues.some((val) => val?.trim()) &&
                        colIndex + 1 < columnCount &&
                        allData.map((row) => row[colIndex + 1]).filter((val) => val?.trim()).length <= 1;

                    if (isLikelyHeader) {
                        detectedHeaders.push({
                            columnIndex: colIndex,
                            headers: columnValues.filter((h) => h?.trim()),
                        });
                    }
                }

                if (detectedHeaders.length > 0) {
                    headers = detectedHeaders;
                    values = allData;
                } else {
                    headers = allData.map((row) => row[0] || '');
                    values = allData;
                }
            }

            const requestData = {
                question_id: selectedQuestion.id,
                header_orientation: headerOrientation,
                headers: JSON.stringify(headers),
                values: JSON.stringify(values),
            };

            if (editingParameter) {
                // Update existing parameter
                await updateParameterAPI(editingParameter.id, {
                    header_orientation: headerOrientation,
                    headers: JSON.stringify(headers),
                    values: JSON.stringify(values),
                });
                toast.success('Parameter updated successfully');
            } else {
                // Create new parameter
                await createParameter(requestData);
                toast.success('Table saved successfully');
            }

            // Refresh the parameters list
            if (selectedQuestion?.id) {
                fetchExistingParameters(selectedQuestion.id);
            }

            // Reset editing state
            setEditingParameter(null);
            onClose();
        } catch (error) {
            console.error('Error saving parameter:', error);
            toast.error(editingParameter ? 'Failed to update parameter' : 'Failed to save table');
        } finally {
            setIsSaving(false);
        }
    };
    return (
        <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
                <DialogHeader>
                    <DialogTitle>
                        {editingParameter ? 'Edit Parameter' : 'Table Structure'}
                        {editingParameter && (
                            <Badge variant="secondary" className="ml-2">
                                Editing Parameter #{editingParameter.id}
                            </Badge>
                        )}
                    </DialogTitle>
                </DialogHeader>

                <div className="max-h-[50vh] overflow-auto">
                    <table className="w-full border-collapse">
                        <tbody>
                            {tableData.rows.map((row, rowIndex) => (
                                <tr key={rowIndex}>
                                    {row.map((cell, cellIndex) => {
                                        const isHeader = cell.type === 'tableHeader';
                                        const Tag = isHeader ? 'th' : 'td';

                                        return (
                                            <Tag
                                                key={cellIndex}
                                                colSpan={cell.colspan}
                                                rowSpan={cell.rowspan}
                                                className={`border p-2 ${isHeader ? 'bg-gray-100 font-semibold' : 'bg-white'}`}
                                            >
                                                {cell.content || <span className="text-gray-300">Empty cell</span>}
                                            </Tag>
                                        );
                                    })}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                <div className="mt-4 text-sm text-gray-500">
                    <p>
                        Table dimensions: {tableData.rowCount} rows × {tableData.colCount} columns
                    </p>
                </div>

                <div className="grid gap-6 py-4">
                    <div className="grid grid-cols-1 gap-4">
                        <div className="space-y-2">
                            <Label className="text-sm font-medium">Question</Label>
                            <div className="flex items-center gap-2">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => setShowQuestionDialog(true)}
                                    className="flex-1 justify-start text-left min-h-[80px] p-4 h-auto"
                                >
                                    {selectedQuestion ? (
                                        <div className="flex w-full flex-col items-start gap-2">
                                            <span className="w-full font-medium text-sm leading-relaxed whitespace-normal break-words">
                                                {selectedQuestion.question_text}
                                            </span>
                                            <span className="w-full text-xs text-gray-500 leading-relaxed whitespace-normal break-words">
                                                {selectedQuestion.subsection?.section && `${selectedQuestion.subsection.section.title} > `}
                                                {selectedQuestion.subsection?.title}
                                            </span>
                                        </div>
                                    ) : (
                                        <span className="text-gray-500">Click to select a question</span>
                                    )}
                                </Button>
                                {selectedQuestion && (
                                    <Button type="button" variant="ghost" size="sm" onClick={() => setSelectedQuestion(null)}>
                                        Clear
                                    </Button>
                                )}
                            </div>
                        </div>
                    </div>
                    <div className="grid grid-cols-1 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="headerOrientation" className="text-sm font-medium">
                                Header Orientation
                            </Label>
                            <Select value={headerOrientation} onValueChange={(value: 'horizontal' | 'vertical') => setHeaderOrientation(value)}>
                                <SelectTrigger className="w-full">
                                    <SelectValue placeholder="Select header orientation" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="horizontal">Horizontal (headers at top)</SelectItem>
                                    <SelectItem value="vertical">Vertical (headers on sides)</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* Existing Parameters Section */}
                    {selectedQuestion && existingParameters.length > 0 && (
                        <div className="space-y-2">
                            <div className="flex items-center justify-between">
                                <Label className="text-sm font-medium">Existing Parameters</Label>
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setShowExistingParameters(!showExistingParameters)}
                                >
                                    {showExistingParameters ? 'Hide' : 'Show'} ({existingParameters.length})
                                </Button>
                            </div>

                            {showExistingParameters && (
                                <div className="space-y-2 max-h-40 overflow-auto border rounded-md p-3 bg-gray-50">
                                    {isLoadingParameters ? (
                                        <div className="space-y-2">
                                            <Skeleton className="h-4 w-full" />
                                            <Skeleton className="h-4 w-3/4" />
                                        </div>
                                    ) : (
                                        existingParameters.map((parameter) => (
                                            <div
                                                key={parameter.id}
                                                className="flex items-center justify-between p-2 bg-white rounded border"
                                            >
                                                <div className="flex-1">
                                                    <div className="text-sm font-medium">
                                                        Parameter #{parameter.id}
                                                    </div>
                                                    <div className="text-xs text-gray-500">
                                                        {parameter.header_orientation} • {parameter.headers.length} headers • {parameter.values.length} rows
                                                    </div>
                                                </div>
                                                <div className="flex items-center gap-1">
                                                    <Button
                                                        type="button"
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => loadParameterForEditing(parameter)}
                                                        title="Edit parameter"
                                                    >
                                                        <Edit className="h-3 w-3" />
                                                    </Button>
                                                    <Button
                                                        type="button"
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => deleteParameter(parameter.id)}
                                                        title="Delete parameter"
                                                        className="text-red-600 hover:text-red-700"
                                                    >
                                                        <Trash2 className="h-3 w-3" />
                                                    </Button>
                                                </div>
                                            </div>
                                        ))
                                    )}
                                </div>
                            )}
                        </div>
                    )}
                </div>

                <DialogFooter>
                    {editingParameter && (
                        <Button
                            onClick={() => {
                                setEditingParameter(null);
                                setHeaderOrientation('vertical');
                            }}
                            variant="ghost"
                        >
                            Cancel Edit
                        </Button>
                    )}
                    <Button onClick={onClose} variant="outline">
                        Cancel
                    </Button>
                    <Button onClick={saveTableData} disabled={isSaving} className="bg-green-600 hover:bg-green-700">
                        {isSaving ? 'Saving...' : editingParameter ? 'Update Parameter' : 'Save Table'}
                    </Button>
                </DialogFooter>
            </DialogContent>

            <QuestionSelectionDialog
                isOpen={showQuestionDialog}
                onClose={() => setShowQuestionDialog(false)}
                onSelect={setSelectedQuestion}
                documentId={documentId}
            />
        </Dialog>
    );
};

export default TableStructureViewer;
