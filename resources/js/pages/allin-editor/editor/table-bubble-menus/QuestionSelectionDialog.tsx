import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title } from '@/components/ui/dialog';
import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { fetchQuestionsForDocument, Question } from '../../services/questionService';

interface QuestionSelectionDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onSelect: (question: Question) => void;
    documentId: number | null;
}

const QuestionSelectionDialog: React.FC<QuestionSelectionDialogProps> = ({ isOpen, onClose, onSelect, documentId }) => {
    const [questions, setQuestions] = useState<Question[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        const loadQuestions = async () => {
            if (!documentId) return;

            setIsLoading(true);
            try {
                const fetchedQuestions = await fetchQuestionsForDocument(documentId);
                setQuestions(fetchedQuestions || []);
            } catch (error) {
                console.error('Error loading questions:', error);
                toast.error('Failed to load questions');
            } finally {
                setIsLoading(false);
            }
        };

        if (isOpen && documentId) {
            loadQuestions();
        }
    }, [isOpen, documentId]);

    const handleSelect = (question: Question) => {
        onSelect(question);
        onClose();
    };

    return (
        <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <DialogContent className="max-h-[85vh] max-w-5xl">
                <DialogHeader>
                    <DialogTitle>Select Question</DialogTitle>
                </DialogHeader>

                <div className="max-h-[65vh] overflow-auto">
                    {isLoading ? (
                        <div className="flex items-center justify-center py-12">
                            <div className="text-sm text-gray-500">Loading questions...</div>
                        </div>
                    ) : questions.length === 0 ? (
                        <div className="flex items-center justify-center py-12">
                            <div className="text-sm text-gray-500">No questions found for this document</div>
                        </div>
                    ) : (
                        <div className="space-y-3">
                            {questions.map((question) => (
                                <div
                                    key={question.id}
                                    className="cursor-pointer rounded-lg border p-4 transition-colors hover:bg-gray-50 hover:border-gray-300"
                                    onClick={() => handleSelect(question)}
                                >
                                    <div className="mb-3 text-sm font-medium text-gray-900 leading-relaxed">{question.question_text}</div>

                                    <div className="flex flex-col gap-2 text-xs text-gray-600">
                                        {question.section && (
                                            <div className="flex items-start gap-2">
                                                <span className="font-medium min-w-[60px]">Section:</span>
                                                <span className="flex-1">
                                                    {question.section.section_number && `${question.section.section_number}. `}
                                                    {question.section.title}
                                                </span>
                                            </div>
                                        )}

                                        {question.subsection?.section && !question.section && (
                                            <div className="flex items-start gap-2">
                                                <span className="font-medium min-w-[60px]">Section:</span>
                                                <span className="flex-1">
                                                    {question.subsection.section.section_number && `${question.subsection.section.section_number}. `}
                                                    {question.subsection.section.title}
                                                </span>
                                            </div>
                                        )}

                                        {question.subsection && (
                                            <div className="flex items-start gap-2">
                                                <span className="font-medium min-w-[60px]">Subsection:</span>
                                                <span className="flex-1">
                                                    {question.subsection.section?.section_number &&
                                                        question.subsection.subsection_number &&
                                                        `${question.subsection.section.section_number}.${question.subsection.subsection_number}. `}
                                                    {question.subsection.title}
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                <DialogFooter>
                    <Button onClick={onClose} variant="outline">
                        Cancel
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export default QuestionSelectionDialog;
