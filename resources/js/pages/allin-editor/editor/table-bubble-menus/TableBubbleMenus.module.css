/* Table menu styles */
.tableMenu {
    position: absolute;
    z-index: 10;
    top: -40px;
    left: 0;
    display: flex;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 5px;
}

.tableMenu button {
    margin: 0 2px;
    padding: 5px 8px;
    background: none;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.tableMenu button:hover {
    background-color: #f0f0f0;
}

.tableMenuGroup {
    display: flex;
    border-right: 1px solid #eee;
    padding-right: 5px;
    margin-right: 5px;
}

.tableMenuGroup:last-child {
    border-right: none;
}

/* Floating table menu */
.floating {
    position: absolute;
    transition:
        top 0.2s ease,
        left 0.2s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    background-color: #ffffff;
    animation: fadeIn 0.2s ease;
}

/* Add a subtle animation when the menu appears */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Make buttons more touch-friendly */
.floating button {
    min-width: 40px;
    min-height: 36px;
    padding: 8px 12px;
    font-size: 14px;
}

/* Add tooltips for improved UX */
.floating button:hover::after {
    content: attr(title);
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    padding: 4px 8px;
    background: #333;
    color: white;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
}

/* Bubble menu */
.bubbleMenu {
    display: flex;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 5px;
}

.bubbleMenu button {
    margin: 0 2px;
    padding: 2px 5px;
    background: none;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.bubbleMenu button:hover {
    background-color: #f0f0f0;
}

/* Cell Bubble Menu */
.cellBubbleMenu {
    display: flex;
    background-color: #343a40;
    border-radius: 6px;
    padding: 5px;
    align-items: center;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
}

.cellFormatButtons,
.cellTableButtons {
    display: flex;
    gap: 4px;
}

.cellDivider {
    width: 1px;
    height: 20px;
    background-color: #6c757d;
    margin: 0 6px;
}

.cellBubbleMenu button {
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: none;
    border: none;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    font-size: 16px;
    padding: 0;
}

.cellBubbleMenu button:hover {
    background-color: #495057;
}

.cellBubbleMenu button.isActive {
    background-color: #1e88e5;
}

/* View Data button */
.viewDataGroup {
    margin-left: 5px;
    border-left: 1px solid #eee;
    padding-left: 5px;
    animation: pulseHighlight 2s infinite;
}

.viewDataButton {
    display: flex !important;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #6e48aa, #9d50bb) !important;
    color: white !important;
    font-weight: bold !important;
    padding: 8px 15px !important;
    border: none !important;
    border-radius: 20px !important;
    box-shadow: 0 4px 8px rgba(157, 80, 187, 0.3) !important;
    transition: all 0.3s ease !important;
    min-width: 120px !important;
}

.viewDataButton:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(157, 80, 187, 0.4) !important;
    background: linear-gradient(135deg, #7d54be, #ae5dd0) !important;
}

.viewDataIcon {
    font-size: 18px;
    margin-right: 8px;
    animation: floatIcon 1.5s ease-in-out infinite;
    display: inline-block;
}

.viewDataText {
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

@keyframes floatIcon {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
}

@keyframes pulseHighlight {
    0%, 100% { box-shadow: 0 0 0 rgba(157, 80, 187, 0); }
    50% { box-shadow: 0 0 8px rgba(157, 80, 187, 0.5); }
}

/* View Table button */
.viewTableButton {
    display: flex !important;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #2196F3, #00BCD4) !important; /* Blue gradient */
    color: white !important;
    font-weight: bold !important;
    padding: 8px 15px !important;
    border: none !important;
    border-radius: 20px !important;
    box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3) !important;
    transition: all 0.3s ease !important;
    min-width: 120px !important;
}

.viewTableButton:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(33, 150, 243, 0.4) !important;
    background: linear-gradient(135deg, #42A5F5, #4DD0E1) !important;
}

.viewTableIcon {
    font-size: 18px;
    margin-right: 8px;
    animation: floatIcon 1.5s ease-in-out infinite;
    display: inline-block;
}

.viewTableText {
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}