import { Editor } from '@tiptap/react';
import '@tiptap/extension-font-family';
import { useState, useRef, useEffect } from 'react';
import {
    AlignLeftIcon,
    AlignCenterIcon,
    AlignRightIcon,
    AlignJustifyIcon,
    TypeIcon,
    ChevronDownIcon
} from 'lucide-react';

interface FormatToolbarProps {
    editor: Editor;
}

const FormatToolbar = ({ editor }: FormatToolbarProps) => {
    const [showFontOptions, setShowFontOptions] = useState(false);
    const fontMenuRef = useRef<HTMLDivElement>(null);
    
    // Font families to choose from
    const fontFamilies = [
        { name: 'System Default', value: '' },
        { name: 'Arial', value: 'Arial, sans-serif' },
        { name: 'Times New Roman', value: 'Times New Roman, serif' },
        { name: 'Courier New', value: 'Courier New, monospace' },
        { name: 'Georgia', value: 'Georgia, serif' },
        { name: '<PERSON><PERSON><PERSON>', value: 'Verdana, sans-serif' },
        { name: 'Helvetica', value: 'Helvetica, sans-serif' },
    ];
    
    // Close font dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (fontMenuRef.current && !fontMenuRef.current.contains(event.target as Node)) {
                setShowFontOptions(false);
            }
        };
        
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);
    
    const toolbarButtonClass = (isActive: boolean) =>
        `p-2 text-sm font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
            isActive ? 'bg-slate-900 text-slate-50 hover:bg-slate-900/90' : 'bg-slate-100 text-slate-900 hover:bg-slate-100/80'
        }`;

    return (
        <div className="flex items-center gap-2 mr-4">
            {/* Text alignment */}
            <button
                type="button"
                title="Align Left"
                onClick={() => editor.chain().focus().setTextAlign('left').run()}
                className={toolbarButtonClass(editor.isActive({ textAlign: 'left' }))}
            >
                <AlignLeftIcon size={18} />
            </button>
            <button
                type="button"
                title="Align Center"
                onClick={() => editor.chain().focus().setTextAlign('center').run()}
                className={toolbarButtonClass(editor.isActive({ textAlign: 'center' }))}
            >
                <AlignCenterIcon size={18} />
            </button>
            <button
                type="button"
                title="Align Right"
                onClick={() => editor.chain().focus().setTextAlign('right').run()}
                className={toolbarButtonClass(editor.isActive({ textAlign: 'right' }))}
            >
                <AlignRightIcon size={18} />
            </button>
            <button
                type="button"
                title="Justify"
                onClick={() => editor.chain().focus().setTextAlign('justify').run()}
                className={toolbarButtonClass(editor.isActive({ textAlign: 'justify' }))}
            >
                <AlignJustifyIcon size={18} />
            </button>
            
            {/* Font family dropdown */}
            <div className="relative" ref={fontMenuRef}>
                <button
                    type="button"
                    title="Font Family"
                    onClick={() => setShowFontOptions(!showFontOptions)}
                    className={toolbarButtonClass(false)}
                >
                    <div className="flex items-center">
                        <TypeIcon size={18} />
                        <ChevronDownIcon size={14} className="ml-1" />
                    </div>
                </button>
                
                {showFontOptions && (
                    <div className="absolute top-full left-0 mt-1 bg-white shadow-lg rounded-md border border-gray-200 p-2 z-10 w-48">
                        {fontFamilies.map((font, index) => (
                            <button
                                key={index}
                                type="button"
                                onClick={() => {
                                    if (font.value) {
                                        editor.chain().focus().setFontFamily(font.value).run();
                                    } else {
                                        editor.chain().focus().unsetFontFamily().run();
                                    }
                                    setShowFontOptions(false);
                                }}
                                className="w-full text-left px-2 py-1.5 text-sm rounded-md hover:bg-gray-100 transition-colors"
                                style={{ fontFamily: font.value }}
                            >
                                {font.name}
                            </button>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default FormatToolbar;
