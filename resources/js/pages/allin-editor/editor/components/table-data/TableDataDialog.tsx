import { Button } from '@/components/ui/button';
import { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Node as ProseMirrorNode } from '@tiptap/pm/model';
import { Editor } from '@tiptap/react';
import { Save, ScanLine } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';
import ScanDialog from './ScanDialog';

interface TableDataDialogProps {
    isOpen: boolean;
    onClose: () => void;
    tableNode: ProseMirrorNode | null;
    editor?: Editor | null;
}

const TableDataDialog: React.FC<TableDataDialogProps> = ({ isOpen, onClose, tableNode, editor }) => {
    const [tableData, setTableData] = useState<Array<Array<string>>>([]);
    const [headers, setHeaders] = useState<Array<string>>([]);
    const [isSecondaryDialogOpen, setIsSecondaryDialogOpen] = useState(false);

    useEffect(() => {
        if (tableNode) {
            extractTableData(tableNode);
        }
    }, [tableNode]);

    const extractTableData = (node: ProseMirrorNode) => {
        const data: Array<Array<string>> = [];
        const headerRow: Array<string> = [];

        // Get table rows
        const rows = node.content.content;

        // Process header row (if exists)
        if (rows.length > 0) {
            const firstRow = rows[0];
            if (firstRow.type.name === 'tableRow') {
                const headerCells = firstRow.content.content;
                headerCells.forEach((cell) => {
                    // Extract text from the header cell
                    let cellText = '';
                    cell.descendants((descendant) => {
                        if (descendant.isText) {
                            cellText += descendant.text;
                        }
                        return true;
                    });
                    headerRow.push(cellText.trim());
                });
            }
        }

        // Process data rows
        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            if (row.type.name === 'tableRow') {
                const rowData: string[] = [];
                const cells = row.content.content;

                cells.forEach((cell) => {
                    // Extract text from the cell
                    let cellText = '';
                    cell.descendants((descendant) => {
                        if (descendant.isText) {
                            cellText += descendant.text;
                        }
                        return true;
                    });
                    rowData.push(cellText.trim());
                });

                data.push(rowData);
            }
        }

        setHeaders(headerRow);
        setTableData(data);
    };

    // Function to add an empty row at the top of the table in the dialog
    const addEmptyRowAtTop = () => {
        if (!headers.length) return;

        // Create an empty row with the same number of cells as the headers
        const emptyRow = Array(headers.length).fill('');

        // Add the empty row at the beginning of the tableData
        setTableData([emptyRow, ...tableData]);
    };

    // Function to save the table data to the Tiptap editor
    const saveTableData = () => {
        if (!editor || !tableNode || !headers.length || !tableData.length) return;

        try {
            // Find the position of the table in the document
            let tablePos = -1;
            editor.state.doc.descendants((node, pos) => {
                if (node.type.name === 'table' && node.eq(tableNode)) {
                    tablePos = pos;
                    return false; // Stop traversal once found
                }
                return true;
            });

            if (tablePos === -1) {
                console.error('Could not find table position');
                return;
            }

            // Create a new table with the updated data
            const createCell = (content: string, isHeader: boolean) => {
                const type = isHeader ? 'tableHeader' : 'tableCell';
                // Always create a paragraph node, even for empty content
                // Use a non-breaking space for empty cells to avoid empty text node error
                const paragraph = editor.schema.nodes.paragraph.createChecked(
                    {},
                    content ? editor.schema.text(content) : editor.schema.text('\u00A0'),
                );
                return editor.schema.nodes[type].createChecked({}, paragraph);
            };

            // Create header row
            const headerRow = editor.schema.nodes.tableRow.createChecked(
                {},
                headers.map((header) => createCell(header, true)),
            );

            // Create data rows
            const dataRows = tableData.map((row) => {
                return editor.schema.nodes.tableRow.createChecked(
                    {},
                    row.map((cell) => createCell(cell, false)),
                );
            });
            // Create the complete table
            const newTable = editor.schema.nodes.table.createChecked({}, [headerRow, ...dataRows]);

            // Replace the old table with the new one
            editor.chain().focus().setNodeSelection(tablePos).deleteSelection().insertContentAt(tablePos, newTable).run();

            toast.success('Table updated successfully!', {
                description: 'The changes have been applied to the table.',
                duration: 3000,
            });
            onClose();
        } catch (error) {
            console.error('Error updating table:', error);
            toast.error('Failed to update table', {
                description: 'Please try again.',
                duration: 5000,
            });
        }
    };



    return (
        <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <DialogContent className="max-w-6xl lg:max-w-7xl">
                <DialogHeader>
                    <DialogTitle>Table Data</DialogTitle>
                    <DialogDescription>View and analyze the data from the selected table</DialogDescription>
                </DialogHeader>

                <div className="mb-4 flex justify-end gap-2">
                    <Button onClick={() => addEmptyRowAtTop()} variant="secondary" disabled={!headers.length}>
                        Add Empty Row at Top
                    </Button>
                    <Button onClick={() => setIsSecondaryDialogOpen(true)} variant="default" className="gap-1">
                        <ScanLine className="h-4 w-4" />
                        Scan & Fetch Data
                    </Button>
                </div>

                {/* Scan Dialog */}
                <ScanDialog
                    isOpen={isSecondaryDialogOpen}
                    onClose={() => setIsSecondaryDialogOpen(false)}
                    headers={headers}
                    setHeaders={setHeaders}
                    tableData={tableData}
                    setTableData={setTableData}
                />

                <div className="max-h-[60vh] overflow-auto">
                    <table className="w-full border-collapse border">
                        <thead>
                            <tr>
                                {headers.map((header, index) => (
                                    <th key={`header-${index}`} className="border bg-gray-100 px-4 py-2">
                                        {header || `Column ${index + 1}`}
                                    </th>
                                ))}
                            </tr>
                        </thead>
                        <tbody>
                            {tableData.map((row, rowIndex) => (
                                <tr key={`row-${rowIndex}`}>
                                    {row.map((cell, cellIndex) => (
                                        <td key={`cell-${rowIndex}-${cellIndex}`} className="border px-4 py-2">
                                            {cell || '—'}
                                        </td>
                                    ))}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                <DialogFooter className="flex justify-between gap-2">
                    <DialogClose asChild>
                        <Button variant="outline">Close</Button>
                    </DialogClose>
                    {tableData.length > 0 && (
                        <Button onClick={saveTableData} variant="default" className="gap-1">
                            <Save className="h-4 w-4" />
                            Save to Table
                        </Button>
                    )}
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export default TableDataDialog;
