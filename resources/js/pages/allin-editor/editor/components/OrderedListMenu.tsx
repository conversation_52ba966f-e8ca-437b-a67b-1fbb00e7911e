import React, { useEffect, useRef, useState } from 'react';
import { Editor } from '@tiptap/react';

// Helper types for the custom setOrderedListStart command from CustomOrderedList extension
import { ListOrderedIcon, Settings2 } from 'lucide-react';
import Tooltip from './Tooltip';

interface OrderedListMenuProps {
  editor: Editor;
}

const OrderedListMenu: React.FC<OrderedListMenuProps> = ({ editor }) => {
  const [showMenu, setShowMenu] = useState(false);
  const [startValue, setStartValue] = useState(1);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const toolbarButtonClass = (isActive: boolean) =>
    `p-2 text-sm font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
      isActive ? 'bg-slate-900 text-slate-50 hover:bg-slate-900/90' : 'bg-slate-100 text-slate-900 hover:bg-slate-100/80'
    }`;

  const handleClickOutside = (event: MouseEvent) => {
    if (
      menuRef.current && 
      !menuRef.current.contains(event.target as Node) && 
      buttonRef.current && 
      !buttonRef.current.contains(event.target as Node)
    ) {
      setShowMenu(false);
    }
  };

  useEffect(() => {
    // Add event listener when menu is shown
    if (showMenu) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }
    
    // Clean up event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showMenu]);

  // Update startValue when the editor selection changes
  useEffect(() => {
    const updateStartValue = () => {
      if (editor.isActive('orderedList')) {
        const attrs = editor.getAttributes('orderedList');
        if (attrs.start) {
          setStartValue(attrs.start);
        }
      }
    };

    editor.on('selectionUpdate', updateStartValue);
    editor.on('transaction', updateStartValue);
    
    return () => {
      editor.off('selectionUpdate', updateStartValue);
      editor.off('transaction', updateStartValue);
    };
  }, [editor]);

  const toggleOrderedList = () => {
    editor.chain().focus().toggleOrderedList().run();
  };

  const setOrderedListStart = (chain: ReturnType<typeof editor.chain>, value: number) => {
    type ExtendedChain = ReturnType<typeof editor.chain> & { 
      setOrderedListStart: (value: number) => ReturnType<typeof editor.chain> 
    };
    return (chain as ExtendedChain).setOrderedListStart(value);
  };

  const applyStartValue = () => {
    if (editor.isActive('orderedList')) {
      setOrderedListStart(editor.chain().focus(), startValue).run();
    } else {
      // If not already in an ordered list, create one with the specified start value
      setOrderedListStart(editor.chain().focus().toggleOrderedList(), startValue).run();
    }
    setShowMenu(false);
  };

  const continueNumbering = () => {
    // Find the previous ordered list and get its last number
    const { state } = editor;
    const { doc, selection } = state;
    let lastNumber = 0;
    
    // Find all ordered lists in the document
    type OrderedListInfo = {pos: number, node: {type: {name: string}, attrs: {start?: number}, childCount: number}, start: number, items: number};
    const orderedLists: OrderedListInfo[] = [];
    
    doc.descendants((node, pos) => {
      if (node.type.name === 'orderedList') {
        orderedLists.push({
          pos,
          node,
          start: node.attrs.start || 1,
          items: node.childCount
        });
      }
      return true;
    });
    
    // Find the most recent ordered list before cursor position
    let previousList: OrderedListInfo | null = null;
    for (const list of orderedLists) {
      if (list.pos < selection.from) {
        if (!previousList || list.pos > previousList.pos) {
          previousList = list;
        }
      }
    }
    
    if (previousList) {
      lastNumber = previousList.start + previousList.items - 1;
    }
    
    // Set the new start value to continue from the last number
    const newStartValue = lastNumber + 1;
    setStartValue(newStartValue);
    
    if (editor.isActive('orderedList')) {
      setOrderedListStart(editor.chain().focus(), newStartValue).run();
    } else {
      setOrderedListStart(editor.chain().focus().toggleOrderedList(), newStartValue).run();
    }
    
    setShowMenu(false);
  };

  const resetNumbering = () => {
    setStartValue(1);
    if (editor.isActive('orderedList')) {
      setOrderedListStart(editor.chain().focus(), 1).run();
    } else {
      editor.chain().focus().toggleOrderedList().run();
    }
    setShowMenu(false);
  };

  return (
    <div className="relative">
      <div className="flex items-center gap-1">
        <Tooltip tooltip="Ordered List">
          <button
            ref={buttonRef}
            type="button"
            onClick={toggleOrderedList}
            className={toolbarButtonClass(editor.isActive('orderedList'))}
          >
            <ListOrderedIcon size={18} />
          </button>
        </Tooltip>
        <Tooltip tooltip="List Options">
          <button
            type="button"
            onClick={() => setShowMenu(!showMenu)}
            className={toolbarButtonClass(false)}
          >
            <Settings2 size={16} />
          </button>
        </Tooltip>
      </div>

      {showMenu && (
        <div 
          ref={menuRef}
          className="absolute z-50 mt-1 bg-white rounded-md shadow-lg p-3 border border-gray-200 min-w-[200px]"
        >
          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">Start At Number:</label>
            <div className="flex items-center gap-2">
              <input
                type="number"
                min="1"
                value={startValue}
                onChange={(e) => setStartValue(parseInt(e.target.value) || 1)}
                className="w-20 p-1 border border-gray-300 rounded-md"
              />
              <button
                onClick={applyStartValue}
                className="px-2 py-1 bg-slate-800 text-white rounded-md text-xs"
              >
                Apply
              </button>
            </div>
          </div>
          
          <div className="flex flex-col gap-2">
            <button
              onClick={continueNumbering}
              className="w-full text-left px-2 py-1 hover:bg-slate-100 rounded-md text-sm"
            >
              Continue Numbering
            </button>
            <button
              onClick={resetNumbering}
              className="w-full text-left px-2 py-1 hover:bg-slate-100 rounded-md text-sm"
            >
              Restart at 1
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderedListMenu;
