import { ReactNode, useState, useRef, useEffect } from 'react';

interface TooltipProps {
    children: ReactNode;
    tooltip: string;
    position?: 'top' | 'bottom' | 'left' | 'right';
    delay?: number;
}

const Tooltip = ({ 
    children, 
    tooltip, 
    position = 'top', 
    delay = 300 
}: TooltipProps) => {
    const [isVisible, setIsVisible] = useState(false);
    const [tooltipStyles, setTooltipStyles] = useState({});
    const targetRef = useRef<HTMLDivElement>(null);
    const tooltipRef = useRef<HTMLDivElement>(null);
    const timerRef = useRef<NodeJS.Timeout | null>(null);
    
    const showTooltip = () => {
        timerRef.current = setTimeout(() => {
            if (!targetRef.current || !tooltipRef.current) return;
            
            const targetRect = targetRef.current.getBoundingClientRect();
            const tooltipRect = tooltipRef.current.getBoundingClientRect();
            
            let top = 0;
            let left = 0;
            
            switch (position) {
                case 'top':
                    top = -tooltipRect.height - 8;
                    left = (targetRect.width - tooltipRect.width) / 2;
                    break;
                case 'bottom':
                    top = targetRect.height + 8;
                    left = (targetRect.width - tooltipRect.width) / 2;
                    break;
                case 'left':
                    top = (targetRect.height - tooltipRect.height) / 2;
                    left = -tooltipRect.width - 8;
                    break;
                case 'right':
                    top = (targetRect.height - tooltipRect.height) / 2;
                    left = targetRect.width + 8;
                    break;
            }
            
            setTooltipStyles({
                top: `${top}px`,
                left: `${left}px`
            });
            
            setIsVisible(true);
        }, delay);
    };
    
    const hideTooltip = () => {
        if (timerRef.current) {
            clearTimeout(timerRef.current);
            timerRef.current = null;
        }
        setIsVisible(false);
    };
    
    // Clear timer when component unmounts
    useEffect(() => {
        return () => {
            if (timerRef.current) {
                clearTimeout(timerRef.current);
            }
        };
    }, []);
    
    return (
        <div 
            className="relative inline-block"
            ref={targetRef}
            onMouseEnter={showTooltip}
            onMouseLeave={hideTooltip}
            onFocus={showTooltip}
            onBlur={hideTooltip}
        >
            {children}
            {isVisible && (
                <div
                    ref={tooltipRef}
                    className="absolute z-50 px-2 py-1 text-xs font-medium text-white bg-gray-800 rounded whitespace-nowrap"
                    style={tooltipStyles}
                >
                    {tooltip}
                    <div 
                        className={`absolute w-2 h-2 bg-gray-800 transform rotate-45 ${
                            position === 'top' ? 'bottom-0 translate-y-1/2' : 
                            position === 'bottom' ? 'top-0 -translate-y-1/2' :
                            position === 'left' ? 'right-0 translate-x-1/2' :
                            'left-0 -translate-x-1/2'
                        } ${
                            (position === 'top' || position === 'bottom') ? 'left-1/2 -translate-x-1/2' :
                            'top-1/2 -translate-y-1/2'
                        }`}
                    />
                </div>
            )}
        </div>
    );
};

export default Tooltip;
