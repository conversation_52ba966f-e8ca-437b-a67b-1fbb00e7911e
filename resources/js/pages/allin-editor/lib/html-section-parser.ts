/**
 * HTML section parser for extracting sections from HTML documents
 * Based on identifying ordered lists as section headers and grouping content until the next section
 */

// Define interfaces for document structure
export interface DocumentSubsection {
    title: string;
    content: string;
}

export interface DocumentSection {
    title: string;
    content: string;
    subsections: DocumentSubsection[];
}

/**
 * Parse HTML content into sections based on ordered lists
 */
export function parseHtmlIntoSections(htmlContent: string): DocumentSection[] {
    if (!htmlContent) return [];

    try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const bodyElements = Array.from(doc.body.children);
        const parsedSections: DocumentSection[] = [];
        const topLevelOls = Array.from(doc.querySelectorAll('body > ol'));
        const processedElements = new Set<Element>();

        topLevelOls.forEach((ol) => {
            const sectionListItems = Array.from(ol.querySelectorAll(':scope > li'));

            sectionListItems.forEach((listItem, itemIndex) => {
                if (processedElements.has(listItem)) return;

                processedElements.add(listItem);

                const paragraph = listItem.querySelector('p');
                const title = paragraph ? paragraph.textContent?.trim() || '' : '';
                const olBodyIndex = bodyElements.indexOf(ol);

                // Create HTML container for section content
                const tempContainer = document.createElement('div');
                const newOl = document.createElement('ol');

                // Preserve original OL attributes
                if (ol.hasAttribute('type')) {
                    newOl.setAttribute('type', ol.getAttribute('type') || '');
                }
                if (ol.hasAttribute('start')) {
                    newOl.setAttribute('start', ol.getAttribute('start') || '');
                }

                const listItemClone = listItem.cloneNode(true);
                newOl.appendChild(listItemClone);
                tempContainer.appendChild(newOl);

                let sectionContent = tempContainer.innerHTML;
                let nextElementIndex = olBodyIndex + 1;
                const isLastItemInList = itemIndex === sectionListItems.length - 1;

                // For last items in a list, include following content until next OL
                if (isLastItemInList) {
                    while (nextElementIndex < bodyElements.length && bodyElements[nextElementIndex].tagName !== 'OL') {
                        sectionContent += bodyElements[nextElementIndex].outerHTML;
                        processedElements.add(bodyElements[nextElementIndex]);
                        nextElementIndex++;
                    }
                } else {
                    nextElementIndex = olBodyIndex;
                }

                // Section is ready to be created

                const section: DocumentSection = {
                    title,
                    content: sectionContent,
                    subsections: [],
                };

                parsedSections.push(section);
            });
        });

        // Extract subsections for each section
        parsedSections.forEach((section) => {
            section.subsections = extractSubsections(section.content);
        });

        return parsedSections;
    } catch (error) {
        console.error('Error parsing HTML into sections:', error);
        return [];
    }
}

/**
 * Extract subsections from a section's content
 */
function extractSubsections(sectionContent: string): DocumentSubsection[] {
    if (!sectionContent) return [];

    try {
        const parser = new DOMParser();
        const sectionDoc = parser.parseFromString(sectionContent, 'text/html');
        const subsections: DocumentSubsection[] = [];

        // Find nested OLs (usually subsections)
        const nestedOls = Array.from(sectionDoc.querySelectorAll('ol[type="a"], ol'));

        for (let olIndex = 0; olIndex < nestedOls.length; olIndex++) {
            const nestedOl = nestedOls[olIndex] as HTMLElement;

            // Skip top-level OLs - they're sections, not subsections
            if (!nestedOl.closest('li')) continue;

            const listItems = Array.from(nestedOl.querySelectorAll(':scope > li'));

            for (let itemIndex = 0; itemIndex < listItems.length; itemIndex++) {
                const listItem = listItems[itemIndex] as HTMLElement;

                const paragraph = listItem.querySelector('p');
                const title = paragraph ? paragraph.textContent?.trim() || '' : '';

                // Create wrapper for subsection content
                const wrapper = document.createElement('div');
                wrapper.innerHTML = `<ol type="${nestedOl.getAttribute('type') || '1'}"${nestedOl.getAttribute('start') ? ` start="${nestedOl.getAttribute('start')}"` : ''}>
                    ${listItem.outerHTML}
                </ol>`;

                const handledElements = new Set<Element>();
                const nextElement = listItem.nextElementSibling;

                // Handle associated ULs that belong to this subsection
                if (!nextElement) {
                    let currentElement = nestedOl.nextElementSibling;

                    // Add ULs until the next OL
                    while (currentElement && currentElement.tagName !== 'OL') {
                        if (currentElement.tagName === 'UL' && !handledElements.has(currentElement)) {
                            wrapper.appendChild(currentElement.cloneNode(true));
                            handledElements.add(currentElement);
                        }
                        currentElement = currentElement.nextElementSibling;
                    }
                } else if (nextElement.tagName === 'UL') {
                    wrapper.appendChild(nextElement.cloneNode(true));
                    handledElements.add(nextElement);
                }

                // Subsection is ready to be created

                subsections.push({
                    title,
                    content: wrapper.innerHTML,
                });
            }
        }

        return subsections;
    } catch (error) {
        console.error('Error extracting subsections:', error);
        return [];
    }
}
