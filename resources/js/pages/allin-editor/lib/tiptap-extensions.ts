// Imports for HTML to Tiptap JSON conversion
import { generateJSON } from '@tiptap/html';
import Document from '@tiptap/extension-document';
import Paragraph from '@tiptap/extension-paragraph';
import Text from '@tiptap/extension-text';
import Bold from '@tiptap/extension-bold';
import Italic from '@tiptap/extension-italic';
import BulletList from '@tiptap/extension-bullet-list';
import ListItem from '@tiptap/extension-list-item';
import OrderedList from '@tiptap/extension-ordered-list';
import Heading from '@tiptap/extension-heading';
import Table from '@tiptap/extension-table';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import TableRow from '@tiptap/extension-table-row';
import Image from '@tiptap/extension-image';
import HorizontalRule from '@tiptap/extension-horizontal-rule';
import CodeBlock from '@tiptap/extension-code-block';
import Link from '@tiptap/extension-link';

// Array of extensions for HTML to JSON conversion
export const tiptapExtensions = [
  Document,
  Paragraph,
  Text,
  Bold,
  Italic,
  BulletList,
  ListItem,
  OrderedList,
  Heading,
  Table,
  TableCell,
  TableHeader,
  TableRow,
  Image,
  HorizontalRule,
  CodeBlock,
  Link,
];

/**
 * Converts HTML content to Tiptap JSON format
 * @param html HTML string to convert
 * @returns Tiptap JSON object
 */
export const htmlToTiptapJson = (html: string) => {
  return generateJSON(html, tiptapExtensions);
};
