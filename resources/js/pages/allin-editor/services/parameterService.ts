import axios from 'axios';

export interface Parameter {
    id: number;
    question_id: number;
    header_orientation: 'horizontal' | 'vertical';
    headers: string[];
    values: string[][];
    created_at: string;
    updated_at: string;
}

export interface CreateParameterRequest {
    question_id: number;
    header_orientation: 'horizontal' | 'vertical';
    headers: string;
    values: string;
}

export interface UpdateParameterRequest {
    header_orientation?: 'horizontal' | 'vertical';
    headers?: string;
    values?: string;
}

/**
 * Create a new parameter
 */
export const createParameter = async (data: CreateParameterRequest): Promise<Parameter> => {
    const response = await axios.post(route('parameters.store'), data);
    return response.data;
};

/**
 * Get a parameter by ID
 */
export const getParameter = async (id: number): Promise<Parameter> => {
    const response = await axios.get(route('parameters.show', id));
    return response.data;
};

/**
 * Update a parameter
 */
export const updateParameter = async (id: number, data: UpdateParameterRequest): Promise<Parameter> => {
    const response = await axios.put(route('parameters.update', id), data);
    return response.data;
};

/**
 * Delete a parameter
 */
export const deleteParameter = async (id: number): Promise<void> => {
    await axios.delete(route('parameters.destroy', id));
};

/**
 * Get all parameters for a question
 */
export const getParametersByQuestion = async (questionId: number): Promise<Parameter[]> => {
    const response = await axios.get(route('parameters.by-question', questionId));
    return response.data;
};
