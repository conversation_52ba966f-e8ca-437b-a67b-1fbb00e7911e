import { Editor } from '@tiptap/react';
import axios from 'axios';
import { toast } from 'sonner';

export interface Section {
  id: number;
  document_id: number;
  section_number: number;
  title: string;
  content: string;
  metadata?: Record<string, unknown>;
  subsections?: Array<{
    id: number;
    title: string;
    content: string;
    metadata?: Record<string, unknown>;
    section_id: number;
    subsection_number: number;
  }>;
}

export const createSectionFromSelection = async (
  editor: Editor, 
  currentDocumentId: number,
  fetchDocumentSections: (documentId: number) => Promise<void>
): Promise<boolean> => {
  if (!editor || !currentDocumentId) return false;
  
  const { from, to } = editor.state.selection;
  
  if (from === to) {
    toast.error('Please select some text first');
    return false;
  }
  
  const text = editor.state.doc.textBetween(from, to, ' ');
  
  if (!text.trim()) {
    toast.error('Selected text is empty');
    return false;
  }

  const sectionNumber = prompt('Enter section number:');

  if (!sectionNumber) {
    toast.error('Section number is required');
    return false;
  }

  const parsedSectionNumber = parseInt(sectionNumber, 10);

  if (isNaN(parsedSectionNumber) || parsedSectionNumber <= 0) {
    toast.error('Please enter a valid positive number');
    return false;
  }
  
  try {
    const sectionData = {
      document_id: currentDocumentId,
      title: text,
      section_number: parsedSectionNumber
    };
    
    await axios.post(route('document-sections.store'), sectionData);
    await fetchDocumentSections(currentDocumentId);
    
    toast.success('Section created from selection');
    return true;
  } catch (error) {
    console.error('Error creating section:', error);

    if (axios.isAxiosError(error) && error.response?.status === 422 && error.response?.data?.error) {
      toast.error(error.response.data.error);
    } else {
      toast.error('Failed to create section');
    }

    return false;
  }
};

export const createSubsectionForCurrentSection = async (
  editor: Editor,
  currentSectionId: number,
  currentDocumentId: number | null,
  fetchDocumentSections: (documentId: number) => Promise<void>
): Promise<boolean> => {
  if (!editor) return false;
  
  const { from, to } = editor.state.selection;
  
  if (from === to) {
    toast.error('Please select some text first');
    return false;
  }
  
  const text = editor.state.doc.textBetween(from, to, ' ');
  
  if (!text.trim()) {
    toast.error('Selected text is empty');
    return false;
  }
  
  const subsectionNumber = prompt('Enter subsection number:');
  
  if (!subsectionNumber) {
    toast.error('Subsection number is required');
    return false;
  }
  
  const parsedSubsectionNumber = parseInt(subsectionNumber, 10);
  
  if (isNaN(parsedSubsectionNumber) || parsedSubsectionNumber <= 0) {
    toast.error('Please enter a valid positive number');
    return false;
  }
  
  try {
    const subsectionData = {
      section_id: currentSectionId,
      title: text,
      subsection_number: parsedSubsectionNumber
    };
    
    await axios.post(route('document-subsections.store'), subsectionData);
    
    if (currentDocumentId) {
      await fetchDocumentSections(currentDocumentId);
    }
    
    toast.success('Subsection created successfully');
    return true;
  } catch (error) {
    console.error('Error creating subsection:', error);
    
    if (axios.isAxiosError(error) && error.response?.status === 422 && error.response?.data?.error) {
      toast.error(error.response.data.error);
    } else {
      toast.error('Failed to create subsection');
    }
    
    return false;
  }
};

export const fetchAvailableSections = async (
  documentId: number
): Promise<Section[]> => {
  try {
    const response = await axios.get(route('document-sections.get', documentId));
    return response.data.sections;
  } catch (error) {
    console.error('Error fetching sections:', error);
    toast.error('Failed to fetch sections');
    return [];
  }
};

export const createSubsectionForSection = async (
  sectionId: number,
  title: string,
  subsectionNumber: number,
  currentDocumentId: number | null,
  fetchDocumentSections: (documentId: number) => Promise<void>
): Promise<boolean> => {
  if (!title.trim()) {
    toast.error('Subsection title cannot be empty');
    return false;
  }

  if (isNaN(subsectionNumber) || subsectionNumber <= 0) {
    toast.error('Please enter a valid positive number');
    return false;
  }
  
  try {
    const subsectionData = {
      section_id: sectionId,
      title,
      subsection_number: subsectionNumber
    };
    
    await axios.post(route('document-subsections.store'), subsectionData);
    
    if (currentDocumentId) {
      await fetchDocumentSections(currentDocumentId);
    }
    
    toast.success('Subsection created successfully');
    return true;
  } catch (error) {
    console.error('Error creating subsection:', error);
    
    if (axios.isAxiosError(error) && error.response?.status === 422 && error.response?.data?.error) {
      toast.error(error.response.data.error);
    } else {
      toast.error('Failed to create subsection');
    }
    
    return false;
  }
};

export const deleteSection = async (
  sectionId: number | undefined,
  fetchDocumentSections: (documentId: number) => Promise<void>,
  currentDocumentId: number | null,
  setIsDeleting?: (callback: (prev: Record<string, boolean>) => Record<string, boolean>) => void
): Promise<boolean> => {
  if (!sectionId) return false;

  if (setIsDeleting) {
    setIsDeleting((prev) => ({ ...prev, [`section-${sectionId}`]: true }));
  }

  try {
    await axios.delete(route('document-sections.delete', { id: sectionId }));
    toast.success('Section deleted successfully');

    if (currentDocumentId) {
      await fetchDocumentSections(currentDocumentId);
    }
    return true;
  } catch (error) {
    console.error('Error deleting section:', error);
    toast.error('Failed to delete section');
    return false;
  } finally {
    if (setIsDeleting) {
      setIsDeleting((prev) => ({ ...prev, [`section-${sectionId}`]: false }));
    }
  }
};

export const deleteSubsection = async (
  subsectionId: number | undefined,
  fetchDocumentSections: (documentId: number) => Promise<void>,
  currentDocumentId: number | null,
  setIsDeleting?: (callback: (prev: Record<string, boolean>) => Record<string, boolean>) => void
): Promise<boolean> => {
  if (!subsectionId) return false;

  if (setIsDeleting) {
    setIsDeleting((prev) => ({ ...prev, [`subsection-${subsectionId}`]: true }));
  }

  try {
    await axios.delete(route('document-subsections.delete', { id: subsectionId }));
    toast.success('Subsection deleted successfully');

    if (currentDocumentId) {
      await fetchDocumentSections(currentDocumentId);
    }
    return true;
  } catch (error) {
    console.error('Error deleting subsection:', error);
    toast.error('Failed to delete subsection');
    return false;
  } finally {
    if (setIsDeleting) {
      setIsDeleting((prev) => ({ ...prev, [`subsection-${subsectionId}`]: false }));
    }
  }
}; 