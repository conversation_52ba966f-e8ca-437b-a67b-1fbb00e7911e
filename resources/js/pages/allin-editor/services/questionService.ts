import axios from 'axios';
import { toast } from 'sonner';

export interface Question {
    id: number;
    subsection_id?: number;
    section_id?: number;
    question_text: string;
    created_at: string;
    updated_at: string;
    subsection?: {
        id: number;
        title: string;
        section_number?: number;
        subsection_number?: number;
        section?: {
            id: number;
            title: string;
            section_number?: number;
        };
    };
    section?: {
        id: number;
        title: string;
        section_number?: number;
    };
}

export const createQuestionFromSelection = async (
    targetId: number,
    targetType: 'section' | 'subsection',
    questionText: string,
    currentDocumentId: number | null,
    fetchDocumentSections: (documentId: number) => Promise<void>,
    fetchQuestions?: () => Promise<void>,
) => {
    try {
        if (!questionText.trim()) {
            toast.error('Question text cannot be empty');
            return;
        }

        const requestData: {
            question_text: string;
            section_id?: number;
            subsection_id?: number;
        } = {
            question_text: questionText,
        };

        if (targetType === 'section') {
            requestData.section_id = targetId;
        } else {
            requestData.subsection_id = targetId;
        }

        const response = await axios.post(route('questions.store'), requestData);

        if (response.data?.question) {
            toast.success('Question created successfully');

            if (fetchQuestions) {
                await fetchQuestions();
            }

            if (currentDocumentId) {
                await fetchDocumentSections(currentDocumentId);
            }
        }
    } catch (error) {
        console.error('Error creating question:', error);
        toast.error('Failed to create question');
    }
};

export const fetchQuestionsForSubsection = async (subsectionId: number) => {
    try {
        const response = await axios.get(route('questions.by-subsection', subsectionId));
        return response.data.questions;
    } catch (error) {
        console.error('Error fetching questions:', error);
        toast.error('Failed to fetch questions');
        return [];
    }
};

export const fetchQuestionsForSection = async (sectionId: number) => {
    try {
        const response = await axios.get(route('questions.by-section', sectionId));
        return response.data.questions;
    } catch (error) {
        console.error('Error fetching questions:', error);
        toast.error('Failed to fetch questions');
        return [];
    }
};

export const fetchQuestionsForDocument = async (documentId: number) => {
    try {
        const response = await axios.get(route('questions.by-document', documentId));
        return response.data.questions;
    } catch (error) {
        console.error('Error fetching questions:', error);
        toast.error('Failed to fetch questions');
        return [];
    }
};

export const deleteQuestion = async (
    questionId: number | undefined,
    fetchDocumentSections: (documentId: number) => Promise<void>,
    currentDocumentId: number | null,
    setIsDeleting?: (callback: (prev: Record<string, boolean>) => Record<string, boolean>) => void,
): Promise<boolean> => {
    if (!questionId) return false;

    if (setIsDeleting) {
        setIsDeleting((prev) => ({ ...prev, [`question-${questionId}`]: true }));
    }

    try {
        await axios.delete(route('questions.destroy', { id: questionId }));
        toast.success('Question deleted successfully');

        if (currentDocumentId) {
            await fetchDocumentSections(currentDocumentId);
        }
        return true;
    } catch (error) {
        console.error('Error deleting question:', error);
        toast.error('Failed to delete question');
        return false;
    } finally {
        if (setIsDeleting) {
            setIsDeleting((prev) => ({ ...prev, [`question-${questionId}`]: false }));
        }
    }
};
