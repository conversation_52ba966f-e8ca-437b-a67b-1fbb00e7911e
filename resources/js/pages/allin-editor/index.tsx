import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { Book, FileText, Layers, MenuSquare } from 'lucide-react';
import { useState } from 'react';
import { DocumentProvider } from './context/DocumentContext';
import { EditorTab } from './tabs/EditorTab';
import { RecentDocumentsTab } from './tabs/RecentDocumentsTab';
import { SectionsTab } from './tabs/SectionsTab';
import { UploadTab } from './tabs/UploadTab';

export default function AllinEditor() {
    const [activeTab, setActiveTab] = useState<string>('telecharger');

    const handleTabChange = (value: string) => {
        setActiveTab(value);
    };

    return (
        <DocumentProvider>
            <AppLayout>
                <div className="w-full p-4">
                    <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
                        <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
                            <h1 className="mb-2 text-3xl font-bold text-gray-800 md:mb-0">All-IN Editor</h1>
                            <TabsList className="ml-auto">
                                <TabsTrigger value="telecharger">
                                    <FileText className="mr-2 h-4 w-4" />
                                    Télécharger
                                </TabsTrigger>
                                <TabsTrigger value="recent">
                                    <MenuSquare className="mr-2 h-4 w-4" />
                                    Récents
                                </TabsTrigger>
                                <TabsTrigger value="editor">
                                    <Book className="mr-2 h-4 w-4" />
                                    Éditeur
                                </TabsTrigger>
                                <TabsTrigger value="sections">
                                    <Layers className="mr-2 h-4 w-4" />
                                    Sections
                                </TabsTrigger>
                            </TabsList>
                        </div>

                        <TabsContent value="telecharger" className="mt-0 w-full">
                            <UploadTab onUploadSuccess={() => setActiveTab('editor')} />
                        </TabsContent>

                        <TabsContent value="recent" className="mt-0 w-full">
                            <RecentDocumentsTab onDocumentOpen={() => setActiveTab('editor')} onViewSections={() => setActiveTab('sections')} />
                        </TabsContent>

                        <TabsContent value="editor" className="mt-0 w-full">
                            <EditorTab onSwitchToSections={() => setActiveTab('sections')} />
                        </TabsContent>

                        <TabsContent value="sections" className="mt-0 w-full">
                            <SectionsTab onSwitchToEditor={() => setActiveTab('editor')} />
                        </TabsContent>
                    </Tabs>
                </div>
            </AppLayout>
        </DocumentProvider>
    );
}
