import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON><PERSON>ooter, Di<PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useState } from 'react';

interface Section {
    id: number;
    title: string;
    section_number: number;
}

interface SectionSelectionDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onSelect: (sectionId: number, subsectionNumber: number) => void;
    sections: Section[];
    title: string;
}

export function SectionSelectionDialog({ isOpen, onClose, onSelect, sections, title }: SectionSelectionDialogProps) {
    const [selectedSectionId, setSelectedSectionId] = useState<number | null>(sections.length > 0 ? sections[0].id : null);
    const [subsectionNumber, setSubsectionNumber] = useState<number>(1);

    const selectedSection = sections.find(section => section.id === selectedSectionId);

    const handleSubmit = () => {
        if (selectedSectionId && subsectionNumber > 0) {
            onSelect(selectedSectionId, subsectionNumber);
            onClose();
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-md md:max-w-lg">
                <DialogHeader>
                    <DialogTitle>Select Section</DialogTitle>
                    <DialogDescription>Choose which section to add "{title}" as a subsection</DialogDescription>
                </DialogHeader>
                <div className="py-4 space-y-4">
                    <div>
                        <Label className="text-sm font-medium text-gray-700 mb-2 block">
                            Select Section
                        </Label>
                        <ScrollArea className="h-[200px] pr-4">
                            <div className="space-y-3">
                                {sections.map((section) => (
                                    <div
                                        key={section.id}
                                        className={`flex cursor-pointer items-center space-x-2 rounded-md border p-3 hover:bg-slate-50 ${
                                            selectedSectionId === section.id ? 'border-blue-300 bg-blue-50' : ''
                                        }`}
                                        onClick={() => setSelectedSectionId(section.id)}
                                    >
                                        <input
                                            type="radio"
                                            id={`section-${section.id}`}
                                            checked={selectedSectionId === section.id}
                                            onChange={() => setSelectedSectionId(section.id)}
                                            className="h-4 w-4 text-blue-600"
                                        />
                                        <Label htmlFor={`section-${section.id}`} className="flex-1 cursor-pointer">
                                            <span className="mr-2 inline-block font-medium text-gray-700">Section {section.section_number}:</span>
                                            {section.title}
                                        </Label>
                                    </div>
                                ))}
                            </div>
                        </ScrollArea>
                    </div>

                    {selectedSection && (
                        <div className="border-t pt-4">
                            <Label className="text-sm font-medium text-gray-700 mb-3 block">
                                Subsection Number for Section {selectedSection.section_number}
                            </Label>
                            <div className="flex items-center space-x-2">
                                <div className="flex items-center space-x-1">
                                    <Input
                                        type="text"
                                        value={selectedSection.section_number}
                                        disabled
                                        className="w-16 text-center bg-gray-50"
                                    />
                                    <span className="text-gray-500 font-medium">.</span>
                                    <Input
                                        type="number"
                                        min="1"
                                        value={subsectionNumber}
                                        onChange={(e) => setSubsectionNumber(parseInt(e.target.value) || 1)}
                                        className="w-16 text-center"
                                        placeholder="1"
                                    />
                                </div>
                                <span className="text-sm text-gray-500">
                                    (Will create: {selectedSection.section_number}.{subsectionNumber})
                                </span>
                            </div>
                        </div>
                    )}
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={onClose}>
                        Cancel
                    </Button>
                    <Button onClick={handleSubmit} disabled={!selectedSectionId || subsectionNumber <= 0}>
                        Create Subsection
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
