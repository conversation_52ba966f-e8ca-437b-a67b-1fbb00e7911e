import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { FileText, Loader2, Upload } from 'lucide-react';
import { useDropzone } from 'react-dropzone';

interface FileUploadDropzoneProps {
    isUploading: boolean;
    uploadProgress: number;
    onDrop: (acceptedFiles: File[]) => void;
}

export function FileUploadDropzone({ isUploading, uploadProgress, onDrop }: FileUploadDropzoneProps) {
    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        accept: {
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
        },
        multiple: false,
        disabled: isUploading,
    });

    return (
        <Card>
            <CardContent className="p-0">
                <div
                    {...getRootProps()}
                    className={cn(
                        'rounded-lg border-2 border-dashed p-8 transition-all duration-300',
                        isDragActive ? 'border-primary bg-primary/5 scale-[1.02]' : 'border-muted hover:border-primary/50',
                        isUploading ? 'cursor-not-allowed opacity-80' : 'cursor-pointer',
                    )}
                >
                    <input {...getInputProps()} />
                    <div className="flex flex-col items-center justify-center text-center">
                        {isUploading ? (
                            <>
                                <div className="relative mb-4">
                                    <Loader2 className="text-primary h-16 w-16 animate-spin" />
                                    <div className="border-primary/10 bg-card absolute -bottom-2 left-1/2 -translate-x-1/2 transform rounded-full border px-2">
                                        <span className="text-primary text-xs font-semibold">{uploadProgress}%</span>
                                    </div>
                                </div>
                                <p className="text-lg font-medium">Conversion en cours...</p>
                                <p className="text-muted-foreground mt-1 text-sm">Cela peut prendre un moment</p>
                                <div className="bg-secondary mt-4 h-2 w-64 overflow-hidden rounded-full">
                                    <div
                                        className="bg-primary h-full rounded-full transition-all duration-300"
                                        style={{ width: `${uploadProgress}%` }}
                                    ></div>
                                </div>
                            </>
                        ) : isDragActive ? (
                            <>
                                <Upload className="text-primary mb-4 h-16 w-16" />
                                <p className="text-primary text-lg font-medium">Déposez votre document ici</p>
                            </>
                        ) : (
                            <>
                                <FileText className="text-muted-foreground group-hover:text-primary mb-4 h-16 w-16 transition-colors" />
                                <p className="text-lg font-medium">Glissez et déposez votre document Word ici</p>
                                <p className="text-muted-foreground mt-1 text-sm">
                                    ou <span className="text-primary font-medium">cliquez pour parcourir</span>
                                </p>
                                <p className="text-muted-foreground mt-4 text-xs">Supporte uniquement le format .docx (max 10 Mo)</p>
                            </>
                        )}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
