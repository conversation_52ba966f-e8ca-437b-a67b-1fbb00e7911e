import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ChevronDown, ChevronRight } from 'lucide-react';
import React from 'react';
import { useDocumentContext } from '../context/DocumentContext';

interface DocumentSectionsListProps {
    documentId: number | null;
}

export function DocumentSectionsList({ documentId }: DocumentSectionsListProps) {
    const { parsedSections, handleOpenSection, handleOpenSubsection } = useDocumentContext();
    const [expandedSections, setExpandedSections] = React.useState<Record<number, boolean>>({});

    // If no document is loaded
    if (!documentId) {
        return null;
    }

    const toggleSection = (sectionId: number) => {
        setExpandedSections((prev) => ({
            ...prev,
            [sectionId]: !prev[sectionId],
        }));
    };

    return (
        <Card className="mb-4 border border-gray-200">
            <CardContent className="p-4">
                <h3 className="mb-2 text-lg font-medium">Structure du document</h3>
                {parsedSections.length === 0 ? (
                    <p className="text-sm text-gray-500">
                        Aucune section trouvée. Utilisez le bouton "Gérer les sections" pour diviser votre document en sections.
                    </p>
                ) : (
                    <div className="space-y-1">
                        {parsedSections.map((section) => (
                            <div key={section.id} className="rounded-md">
                                <div className="flex items-center">
                                    {section.subsections && section.subsections.length > 0 ? (
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-6 w-6 p-0"
                                            onClick={() => section.id && toggleSection(section.id)}
                                        >
                                            {expandedSections[section.id as number] ? (
                                                <ChevronDown className="h-4 w-4" />
                                            ) : (
                                                <ChevronRight className="h-4 w-4" />
                                            )}
                                        </Button>
                                    ) : (
                                        <div className="w-6" />
                                    )}
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="justify-start px-2 text-left font-medium hover:bg-gray-100"
                                        onClick={() => handleOpenSection(section)}
                                    >
                                        {section.title}
                                    </Button>
                                </div>

                                {section.id && expandedSections[section.id] && section.subsections && section.subsections.length > 0 && (
                                    <div className="mt-1 ml-6 space-y-1 pl-2">
                                        {section.subsections.map((subsection) => (
                                            <Button
                                                key={subsection.id}
                                                variant="ghost"
                                                size="sm"
                                                className="justify-start px-2 text-left text-sm font-normal hover:bg-gray-100"
                                                onClick={() => handleOpenSubsection(subsection)}
                                            >
                                                {subsection.title}
                                            </Button>
                                        ))}
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
