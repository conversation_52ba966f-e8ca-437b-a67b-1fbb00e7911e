import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { EditorSectionsList } from './EditorSectionsList';

interface DocumentQuestion {
    id?: number;
    question_text: string;
    subsection_id?: number;
    section_id?: number;
}

interface DocumentSubsection {
    id?: number;
    title: string;
    content: string;
    questions?: DocumentQuestion[];
}

interface DocumentSection {
    id?: number;
    title: string;
    content: string;
    questions?: DocumentQuestion[];
    subsections: DocumentSubsection[];
}

interface SectionsListDialogProps {
    isOpen: boolean;
    onClose: () => void;
    sections: DocumentSection[];
    onOpenSection: (section: DocumentSection) => void;
    onOpenSubsection: (subsection: DocumentSubsection) => void;
}

export function SectionsListDialog({
    isOpen,
    onClose,
    sections,
    onOpenSection,
    onOpenSubsection,
}: SectionsListDialogProps) {
    if (!sections || sections.length === 0) {
        return null;
    }

    return (
        <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <DialogContent className="max-w-[95vw] w-full sm:max-w-[95vw] md:max-w-[95vw] lg:max-w-[1200px] xl:max-w-[1400px]">
                <DialogHeader>
                    <DialogTitle>Document Sections</DialogTitle>
                </DialogHeader>
                <div className="max-h-[80vh] overflow-y-auto pr-1">
                    <EditorSectionsList
                        sections={sections}
                        onOpenSection={(section) => {
                            onOpenSection(section);
                            onClose();
                        }}
                        onOpenSubsection={(subsection) => {
                            onOpenSubsection(subsection);
                            onClose();
                        }}
                    />
                </div>
            </DialogContent>
        </Dialog>
    );
}
