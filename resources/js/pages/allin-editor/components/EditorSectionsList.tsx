import { Button } from '@/components/ui/button';
import axios from 'axios';
import { ChevronDown, ChevronRight, Eye, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { useDocumentContext } from '../context/DocumentContext';
import { deleteQuestion } from '../services/questionService';
import { deleteSection, deleteSubsection } from '../services/sectionService';

interface DocumentQuestion {
    id?: number;
    question_text: string;
    subsection_id?: number;
    section_id?: number;
}

interface DocumentSubsection {
    id?: number;
    title: string;
    content: string;
    subsection_number?: number;
    questions?: DocumentQuestion[];
}

interface DocumentSection {
    id?: number;
    title: string;
    content: string;
    section_number?: number;
    questions?: DocumentQuestion[];
    subsections: DocumentSubsection[];
}

interface EditorSectionsListProps {
    sections: DocumentSection[];
    onOpenSection: (section: DocumentSection) => void;
    onOpenSubsection: (subsection: DocumentSubsection) => void;
}

export function EditorSectionsList({ sections, onOpenSection, onOpenSubsection }: EditorSectionsListProps) {
    const [expandedSections, setExpandedSections] = useState<Record<number, boolean>>({});
    const { fetchDocumentSections, currentDocumentId } = useDocumentContext();
    const [isDeleting, setIsDeleting] = useState<Record<string, boolean>>({});
    const [showNewSubsectionForm, setShowNewSubsectionForm] = useState<number | null>(null);
    const [newSubsection, setNewSubsection] = useState<{ title: string; section_id: number | undefined; subsection_number: number }>({
        title: '',
        section_id: undefined,
        subsection_number: 1,
    });
    const [isCreatingSubsection, setIsCreatingSubsection] = useState(false);

    const toggleSection = (index: number) => {
        setExpandedSections((prev) => ({
            ...prev,
            [index]: !prev[index],
        }));
    };

    const handleRemoveSection = async (sectionId: number | undefined) => {
        await deleteSection(sectionId, fetchDocumentSections, currentDocumentId, setIsDeleting);
    };

    const handleRemoveSubsection = async (subsectionId: number | undefined) => {
        await deleteSubsection(subsectionId, fetchDocumentSections, currentDocumentId, setIsDeleting);
    };

    const handleRemoveQuestion = async (questionId: number | undefined) => {
        await deleteQuestion(questionId, fetchDocumentSections, currentDocumentId, setIsDeleting);
    };

    const handleAddSubsection = (sectionId: number | undefined) => {
        if (!sectionId) return;

        setShowNewSubsectionForm(sectionId);
        setNewSubsection({
            title: '',
            section_id: sectionId,
            subsection_number: 1,
        });

        setExpandedSections((prev) => ({
            ...prev,
            [sectionId]: true,
        }));
    };

    const handleCreateSubsection = (e: React.FormEvent) => {
        e.preventDefault();

        if (!newSubsection.section_id) {
            toast.error('No section ID available');
            return;
        }

        setIsCreatingSubsection(true);

        const subsectionData = {
            section_id: newSubsection.section_id,
            title: newSubsection.title,
            subsection_number: newSubsection.subsection_number,
        };

        axios
            .post(route('document-subsections.store'), subsectionData)
            .then((response) => {
                console.log('Subsection created:', response.data);
                if (currentDocumentId) {
                    fetchDocumentSections(currentDocumentId);
                }
                setNewSubsection({ title: '', section_id: undefined, subsection_number: 1 });
                setShowNewSubsectionForm(null);
                toast.success('Subsection created successfully');
            })
            .catch((error) => {
                console.error('Error creating subsection:', error);
                toast.error('Failed to create subsection: ' + (error.response?.data?.message || error.message));
            })
            .finally(() => {
                setIsCreatingSubsection(false);
            });
    };

    if (!sections || sections.length === 0) {
        return null;
    }

    return (
        <div className="mb-4 rounded-md border bg-slate-50 p-3">
            <div className="mb-2">
                <h3 className="font-medium">Sections du document:</h3>
            </div>

            <div className="space-y-1">
                {sections.map((section) => (
                    <div key={section.id} className="rounded-md">
                        <div className="flex items-center">
                            {section.subsections && section.subsections.length > 0 ? (
                                <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={() => section.id && toggleSection(section.id)}>
                                    {expandedSections[section.id || 0] ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                                </Button>
                            ) : (
                                <div className="w-6" />
                            )}
                            <Button
                                variant="ghost"
                                size="sm"
                                className="cursor-pointer justify-start px-2 text-left font-medium hover:bg-slate-100"
                                onClick={() => onOpenSection(section)}
                            >
                                {section.section_number ? `${section.section_number}. ` : ''}
                                {section.title}
                            </Button>
                            <div className="ml-auto flex space-x-1">
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-6 w-6 text-blue-500 hover:bg-blue-50 hover:text-blue-600"
                                    onClick={() => handleAddSubsection(section.id)}
                                    title="Add subsection"
                                >
                                    <Plus className="h-3.5 w-3.5" />
                                </Button>
                                <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => onOpenSection(section)}>
                                    <Eye className="h-3.5 w-3.5" />
                                </Button>
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-6 w-6 text-red-500 hover:bg-red-50 hover:text-red-600"
                                    onClick={() => handleRemoveSection(section.id)}
                                    disabled={isDeleting[`section-${section.id}`]}
                                >
                                    {isDeleting[`section-${section.id}`] ? (
                                        <div className="h-3.5 w-3.5 animate-spin rounded-full border-2 border-red-500 border-t-transparent"></div>
                                    ) : (
                                        <Trash2 className="h-3.5 w-3.5" />
                                    )}
                                </Button>
                            </div>
                        </div>

                        {section.questions && section.questions.length > 0 && (
                            <div className="mt-1 mb-2 ml-6 space-y-1 border-l-2 border-blue-200 pl-2">
                                {section.questions.map((question, qIndex) => (
                                    <div
                                        key={qIndex}
                                        className="flex items-center justify-between rounded-sm bg-blue-50 px-2 py-1 text-xs text-blue-600 italic hover:bg-blue-100"
                                    >
                                        <span className="mr-2 flex-grow">
                                            <span className="font-medium text-blue-700">Q:</span> {question.question_text}
                                        </span>
                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            className="ml-auto h-5 w-5 text-red-500 hover:bg-red-50 hover:text-red-600"
                                            onClick={() => handleRemoveQuestion(question.id)}
                                            disabled={isDeleting[`question-${question.id}`]}
                                        >
                                            {isDeleting[`question-${question.id}`] ? (
                                                <div className="h-3 w-3 animate-spin rounded-full border-2 border-red-500 border-t-transparent"></div>
                                            ) : (
                                                <Trash2 className="h-3 w-3" />
                                            )}
                                        </Button>
                                    </div>
                                ))}
                            </div>
                        )}

                        {/* Subsection form */}
                        {showNewSubsectionForm === section.id && (
                            <div className="mt-2 mb-3 ml-6 rounded-md border bg-white p-3 shadow-sm">
                                <h4 className="mb-2 text-sm font-medium text-gray-700">Nouvelle sous-section</h4>
                                <form onSubmit={handleCreateSubsection}>
                                    <div className="mb-2">
                                        <label className="mb-1 block text-sm font-medium text-gray-700">Numéro de la sous-section</label>
                                        <input
                                            type="number"
                                            min="1"
                                            className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                                            value={newSubsection.subsection_number}
                                            onChange={(e) => setNewSubsection({ ...newSubsection, subsection_number: parseInt(e.target.value) || 1 })}
                                            placeholder="Entrez le numéro de la sous-section"
                                            required
                                        />
                                    </div>
                                    <div className="mb-2">
                                        <label className="mb-1 block text-sm font-medium text-gray-700">Titre de la sous-section</label>
                                        <input
                                            type="text"
                                            className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                                            value={newSubsection.title}
                                            onChange={(e) => setNewSubsection({ ...newSubsection, title: e.target.value })}
                                            placeholder="Entrez le titre de la sous-section"
                                            required
                                        />
                                    </div>
                                    <div className="flex justify-end space-x-2">
                                        <Button
                                            type="button"
                                            variant="outline"
                                            size="sm"
                                            onClick={() => setShowNewSubsectionForm(null)}
                                            disabled={isCreatingSubsection}
                                        >
                                            Annuler
                                        </Button>
                                        <Button type="submit" variant="default" size="sm" disabled={isCreatingSubsection}>
                                            {isCreatingSubsection ? (
                                                <>
                                                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                                                    Création...
                                                </>
                                            ) : (
                                                'Créer la sous-section'
                                            )}
                                        </Button>
                                    </div>
                                </form>
                            </div>
                        )}

                        {expandedSections[section.id || 0] && section.subsections && section.subsections.length > 0 && (
                            <div className="mt-1 ml-6 space-y-1 pr-1 pl-2">
                                {section.subsections.map((subsection, subIndex) => (
                                    <div key={subIndex} className="flex flex-col">
                                        <div className="flex items-center">
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                className="cursor-pointer justify-start px-2 text-left text-sm font-normal hover:bg-slate-100"
                                                onClick={() => onOpenSubsection(subsection)}
                                            >
                                                {section.section_number && subsection.subsection_number
                                                    ? `${section.section_number}.${subsection.subsection_number}. `
                                                    : ''}
                                                {subsection.title}
                                            </Button>
                                            <div className="ml-auto flex space-x-1">
                                                <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => onOpenSubsection(subsection)}>
                                                    <Eye className="h-3.5 w-3.5" />
                                                </Button>
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    className="h-6 w-6 text-red-500 hover:bg-red-50 hover:text-red-600"
                                                    onClick={() => handleRemoveSubsection(subsection.id)}
                                                    disabled={isDeleting[`subsection-${subsection.id}`]}
                                                >
                                                    {isDeleting[`subsection-${subsection.id}`] ? (
                                                        <div className="h-3.5 w-3.5 animate-spin rounded-full border-2 border-red-500 border-t-transparent"></div>
                                                    ) : (
                                                        <Trash2 className="h-3.5 w-3.5" />
                                                    )}
                                                </Button>
                                            </div>
                                        </div>

                                        {subsection.questions && subsection.questions.length > 0 && (
                                            <div className="mt-1 mb-2 ml-6 space-y-1 border-l-2 border-gray-200 pl-2">
                                                {subsection.questions.map((question, qIndex) => (
                                                    <div
                                                        key={qIndex}
                                                        className="flex items-center justify-between rounded-sm bg-gray-50 px-2 py-1 text-xs text-gray-600 italic hover:bg-gray-100"
                                                    >
                                                        <span className="mr-2 flex-grow">
                                                            <span className="font-medium text-gray-700">Q:</span> {question.question_text}
                                                        </span>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            className="ml-auto h-5 w-5 text-red-500 hover:bg-red-50 hover:text-red-600"
                                                            onClick={() => handleRemoveQuestion(question.id)}
                                                            disabled={isDeleting[`question-${question.id}`]}
                                                        >
                                                            {isDeleting[`question-${question.id}`] ? (
                                                                <div className="h-3 w-3 animate-spin rounded-full border-2 border-red-500 border-t-transparent"></div>
                                                            ) : (
                                                                <Trash2 className="h-3 w-3" />
                                                            )}
                                                        </Button>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );
}
