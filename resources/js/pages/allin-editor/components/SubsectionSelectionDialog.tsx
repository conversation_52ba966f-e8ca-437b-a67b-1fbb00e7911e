import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import React, { useState } from 'react';

interface SectionOption {
    id: number;
    title: string;
    section_number: number;
    document_id: number;
    content: string;
}

interface SubsectionOption {
    id: number;
    title: string;
    section_id: number;
    section_title: string;
    subsection_number: number;
    section_number: number;
}

interface SectionSubsectionSelectionDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onSelect: (targetId: number, targetType: 'section' | 'subsection') => void;
    sections: SectionOption[];
    subsections: SubsectionOption[];
    title: string;
}

export const SectionSubsectionSelectionDialog: React.FC<SectionSubsectionSelectionDialogProps> = ({ isOpen, onClose, onSelect, sections, subsections, title }) => {
    const [selectedTargetId, setSelectedTargetId] = useState<number | null>(null);
    const [selectedTargetType, setSelectedTargetType] = useState<'section' | 'subsection'>('subsection');

    const handleSubmit = () => {
        if (selectedTargetId && selectedTargetType) {
            onSelect(selectedTargetId, selectedTargetType);
            onClose();
        }
    };

    const handleTargetSelect = (id: number, type: 'section' | 'subsection') => {
        setSelectedTargetId(id);
        setSelectedTargetType(type);
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-4xl">
                <DialogHeader>
                    <DialogTitle>Select Target for Question</DialogTitle>
                    <DialogDescription>Choose where to add "{title}" as a question</DialogDescription>
                </DialogHeader>
                <div className="py-4">
                    <div className="grid grid-cols-2 gap-6 h-[400px]">
                        <div>
                            <h4 className="mb-3 text-sm font-medium text-gray-900 border-b pb-2">Sections</h4>
                            <ScrollArea className="h-[350px] pr-2">
                                <div className="space-y-2">
                                    {sections.map((section) => (
                                        <div
                                            key={`section-${section.id}`}
                                            className={`flex cursor-pointer items-center space-x-2 rounded-md border p-3 hover:bg-slate-50 ${
                                                selectedTargetId === section.id && selectedTargetType === 'section' ? 'border-blue-300 bg-blue-50' : ''
                                            }`}
                                            onClick={() => handleTargetSelect(section.id, 'section')}
                                        >
                                            <input
                                                type="radio"
                                                id={`section-${section.id}`}
                                                checked={selectedTargetId === section.id && selectedTargetType === 'section'}
                                                onChange={() => handleTargetSelect(section.id, 'section')}
                                                className="h-4 w-4 text-blue-600"
                                            />
                                            <div className="flex-1 cursor-pointer">
                                                <Label htmlFor={`section-${section.id}`} className="block font-medium">
                                                    <span className="mr-2 inline-block font-medium text-gray-700">
                                                        Section {section.section_number}:
                                                    </span>
                                                    {section.title}
                                                </Label>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </ScrollArea>
                        </div>

                        <div>
                            <h4 className="mb-3 text-sm font-medium text-gray-900 border-b pb-2">Subsections</h4>
                            <ScrollArea className="h-[350px] pr-2">
                                <div className="space-y-2">
                                    {subsections.map((subsection) => (
                                        <div
                                            key={`subsection-${subsection.id}`}
                                            className={`flex cursor-pointer items-center space-x-2 rounded-md border p-3 hover:bg-slate-50 ${
                                                selectedTargetId === subsection.id && selectedTargetType === 'subsection' ? 'border-blue-300 bg-blue-50' : ''
                                            }`}
                                            onClick={() => handleTargetSelect(subsection.id, 'subsection')}
                                        >
                                            <input
                                                type="radio"
                                                id={`subsection-${subsection.id}`}
                                                checked={selectedTargetId === subsection.id && selectedTargetType === 'subsection'}
                                                onChange={() => handleTargetSelect(subsection.id, 'subsection')}
                                                className="h-4 w-4 text-blue-600"
                                            />
                                            <div className="flex-1 cursor-pointer">
                                                <Label htmlFor={`subsection-${subsection.id}`} className="block font-medium">
                                                    <span className="mr-2 inline-block font-medium text-gray-700">
                                                        {subsection.section_number}.{subsection.subsection_number}:
                                                    </span>
                                                    {subsection.title}
                                                </Label>
                                                <span className="text-xs text-gray-500">Section: {subsection.section_title}</span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </ScrollArea>
                        </div>
                    </div>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={onClose}>
                        Cancel
                    </Button>
                    <Button onClick={handleSubmit} disabled={!selectedTargetId}>
                        Create Question
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export const SubsectionSelectionDialog = SectionSubsectionSelectionDialog;
