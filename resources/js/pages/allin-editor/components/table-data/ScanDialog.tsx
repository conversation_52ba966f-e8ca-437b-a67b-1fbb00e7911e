import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import axios from 'axios';
import { AlertCircle, CheckCircle2, Database, Loader2, ScanLine } from 'lucide-react';
import React, { useState } from 'react';

interface EquipmentVerification {
    id: number;
    code: string;
    equipment: string;
    verification: string;
    oui: boolean;
    non: boolean;
    created_at: string;
    updated_at: string;
}

interface ScanDialogProps {
    isOpen: boolean;
    onClose: () => void;
    headers: Array<string>;
    setHeaders: React.Dispatch<React.SetStateAction<Array<string>>>;
    tableData: Array<Array<string>>;
    setTableData: React.Dispatch<React.SetStateAction<Array<Array<string>>>>;
}

// Function to get a random equipment code from the seeder
const getRandomEquipmentCode = (): string => {
    // List of equipment codes from the seeder
    const equipmentCodes = [
        'P_F01_01', // Cabine de pesée
        'P_F01_04', // Balance 1
        'P_F01_05', // Balance 2
        'P_F01_06', // Thermomètre
        'P_F01_07', // Hygromètre
        'P_F01_08', // Réfrigérateur
        'P_F01_09', // Chambre froide
        'P_F01_10', // Spectrophotomètre
        'P_F01_11', // pH-mètre
        'P_F01_12', // Centrifugeuse
    ];

    // Return a random code from the list
    return equipmentCodes[Math.floor(Math.random() * equipmentCodes.length)];
};

const ScanDialog: React.FC<ScanDialogProps> = ({ isOpen, onClose, headers, setHeaders, tableData, setTableData }) => {
    // Scanner state
    const [scannedCode, setScannedCode] = useState('');
    const [isSimulatingScanning, setIsSimulatingScanning] = useState(false);
    const [scanStatus, setScanStatus] = useState<'idle' | 'scanning' | 'success' | 'error'>('idle');

    // Data fetching state
    const [isFetchingData, setIsFetchingData] = useState(false);
    const [fetchError, setFetchError] = useState<string | null>(null);
    const [equipmentVerification, setEquipmentVerification] = useState<EquipmentVerification | null>(null);

    // Function to simulate scanning a product code
    const simulateScan = () => {
        setIsSimulatingScanning(true);
        setScanStatus('scanning');
        // Reset any previous fetch data
        setEquipmentVerification(null);
        setFetchError(null);

        // Simulate a delay like a real scanner would have
        setTimeout(() => {
            try {
                // Get a random equipment code from our seeded data
                const equipmentCode = getRandomEquipmentCode();
                setScannedCode(equipmentCode);
                setScanStatus('success');

                // Don't auto-reset success status so user can see the code and fetch data
            } catch (e) {
                console.error('Error during scanning:', e);
                setScanStatus('error');

                // Reset error status after 2 seconds
                setTimeout(() => {
                    if (scanStatus === 'error') {
                        setScanStatus('idle');
                    }
                }, 2000);
            } finally {
                setIsSimulatingScanning(false);
            }
        }, 1500); // 1.5 second delay to simulate scanning
    };

    // Function to fetch equipment verification data by code
    const fetchEquipmentVerification = async (code: string) => {
        setIsFetchingData(true);
        setFetchError(null);

        try {
            const response = await axios.get(route('equipment-verifications.find-by-code', { code }));
            const data = response.data.data;
            setEquipmentVerification(data);

            // Update table data with the fetched information
            if (data) {
                // Create headers if they don't exist
                if (headers.length === 0) {
                    setHeaders(['Code', 'Equipment', 'Verification', 'Oui', 'Non']);
                }

                // Create a new row with the equipment verification data
                const newRow = [data.code, data.equipment, data.verification, data.oui ? 'Yes' : 'No', data.non ? 'Yes' : 'No'];

                // Add the new row to the table data
                setTableData([newRow, ...tableData]);

                // Keep the secondary dialog open
            }
        } catch (error) {
            console.error('Error fetching equipment verification:', error);
            setFetchError('Failed to fetch equipment verification data. Please try again.');
        } finally {
            setIsFetchingData(false);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <DialogContent className="max-w-md">
                <DialogHeader>
                    <DialogTitle>Scan Equipment Code</DialogTitle>
                    <DialogDescription>Scan or enter an equipment code to fetch verification data.</DialogDescription>
                </DialogHeader>

                <div className="flex flex-col gap-6 py-4">
                    {/* Scanner visualization */}
                    <div className="relative mx-auto mt-2 mb-2 flex h-20 w-full max-w-sm items-center justify-center overflow-hidden rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 p-1">
                        <div
                            className={`absolute inset-0 flex items-center justify-center ${scanStatus === 'scanning' ? 'animate-pulse' : ''}`}
                        >
                            {scanStatus === 'idle' && (
                                <div className="flex flex-col items-center justify-center gap-1 text-center">
                                    <ScanLine className="h-8 w-8 text-blue-500" />
                                    <span className="text-xs text-blue-700">Ready to scan</span>
                                </div>
                            )}
                            {scanStatus === 'scanning' && (
                                <div className="flex flex-col items-center justify-center gap-1 text-center">
                                    <div className="relative">
                                        <ScanLine className="h-8 w-8 text-blue-500" />
                                        <div className="animate-scan-line absolute top-1/2 left-0 h-0.5 w-full -translate-y-1/2 bg-red-500"></div>
                                    </div>
                                    <span className="text-xs text-blue-700">Scanning...</span>
                                </div>
                            )}
                            {scanStatus === 'success' && (
                                <div className="flex flex-col items-center justify-center gap-1 text-center">
                                    <CheckCircle2 className="h-8 w-8 text-green-500" />
                                    <span className="text-xs text-green-700">Code scanned successfully</span>
                                </div>
                            )}
                            {scanStatus === 'error' && (
                                <div className="flex flex-col items-center justify-center gap-1 text-center">
                                    <AlertCircle className="h-8 w-8 text-red-500" />
                                    <span className="text-xs text-red-700">Error scanning code</span>
                                </div>
                            )}
                        </div>
                        {scanStatus === 'scanning' && (
                            <div className="absolute top-0 left-0 h-full w-full">
                                <div className="animate-scanner-overlay absolute top-0 left-0 h-full w-full bg-gradient-to-b from-transparent via-blue-400/20 to-transparent"></div>
                            </div>
                        )}
                    </div>

                    {/* Product code input */}
                    <div className="space-y-2">
                        <Label htmlFor="product-code" className="text-sm font-medium">
                            Product Code
                        </Label>
                        <div className="flex gap-2">
                            <Input
                                id="product-code"
                                type="text"
                                value={scannedCode}
                                onChange={(e) => setScannedCode(e.target.value)}
                                placeholder="Enter code or scan..."
                                className="font-mono"
                                disabled={isSimulatingScanning}
                            />
                            <Button
                                type="button"
                                onClick={() => simulateScan()}
                                variant="outline"
                                className="gap-1 border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100 hover:text-blue-800"
                                disabled={isSimulatingScanning}
                            >
                                {isSimulatingScanning ? (
                                    <>
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                        <span>Scanning</span>
                                    </>
                                ) : (
                                    <>
                                        <ScanLine className="h-4 w-4" />
                                        <span>Scan</span>
                                    </>
                                )}
                            </Button>
                        </div>
                    </div>

                    {/* Information about scanned code */}
                    {scannedCode && scanStatus === 'success' && (
                        <div className="rounded-md bg-green-50 p-3 text-sm text-green-800">
                            <div className="flex items-center gap-2">
                                <CheckCircle2 className="h-4 w-4" />
                                <span className="font-medium">Product code detected: {scannedCode}</span>
                            </div>
                            <p className="mt-1 text-xs">Ready to fetch equipment verification data from the database.</p>
                        </div>
                    )}

                    {/* Fetch error message */}
                    {fetchError && (
                        <div className="rounded-md bg-red-50 p-3 text-sm text-red-800">
                            <div className="flex items-center gap-2">
                                <AlertCircle className="h-4 w-4" />
                                <span className="font-medium">Error</span>
                            </div>
                            <p className="mt-1 text-xs">{fetchError}</p>
                        </div>
                    )}

                    {/* Equipment verification data */}
                    {equipmentVerification && (
                        <div className="rounded-md bg-blue-50 p-3 text-sm text-blue-800">
                            <div className="flex items-center gap-2">
                                <Database className="h-4 w-4" />
                                <span className="font-medium">Equipment Verification Found</span>
                            </div>
                            <div className="mt-2 grid grid-cols-2 gap-1 text-xs">
                                <span className="font-medium">Code:</span>
                                <span>{equipmentVerification.code}</span>
                                <span className="font-medium">Equipment:</span>
                                <span>{equipmentVerification.equipment}</span>
                                <span className="font-medium">Verification:</span>
                                <span>{equipmentVerification.verification}</span>
                                <span className="font-medium">Oui:</span>
                                <span>{equipmentVerification.oui ? 'Yes' : 'No'}</span>
                                <span className="font-medium">Non:</span>
                                <span>{equipmentVerification.non ? 'Yes' : 'No'}</span>
                            </div>
                        </div>
                    )}

                    {/* Table headers info */}
                    {headers.length > 0 && (
                        <div className="mt-4">
                            <h3 className="font-medium">Current Table Headers:</h3>
                            <ul className="mt-2 list-disc pl-5">
                                {headers.map((header, index) => (
                                    <li key={`secondary-header-${index}`}>{header || `Column ${index + 1}`}</li>
                                ))}
                            </ul>
                        </div>
                    )}
                </div>

                <DialogFooter className="flex justify-between gap-2">
                    <Button onClick={onClose} variant="outline">
                        Close
                    </Button>
                    {scannedCode && scanStatus === 'success' && (
                        <Button onClick={() => fetchEquipmentVerification(scannedCode)} className="gap-1" disabled={isFetchingData}>
                            {isFetchingData ? (
                                <>
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    <span>Fetching...</span>
                                </>
                            ) : (
                                <>
                                    <Database className="h-4 w-4" />
                                    <span>Fetch Data</span>
                                </>
                            )}
                        </Button>
                    )}
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export default ScanDialog;
