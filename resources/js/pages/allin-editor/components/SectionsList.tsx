import { Button } from '@/components/ui/button';
import { DocumentSection, DocumentSubsection } from '../lib/html-section-parser';
import { ChevronDown, ChevronRight, Eye, File, FileText, Trash } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface SectionsListProps {
    sections: DocumentSection[];
    onOpenSection?: (section: DocumentSection) => void;
    onOpenSubsection?: (subsection: DocumentSubsection) => void;
    showDeleteButtons?: boolean;
    onSectionsChange?: (sections: DocumentSection[]) => void;
}

export function SectionsList({ sections, onOpenSection, onOpenSubsection, showDeleteButtons = true, onSectionsChange }: SectionsListProps) {
    const [expandedSections, setExpandedSections] = useState<Record<number, boolean>>({});
    const [selectedSection, setSelectedSection] = useState<DocumentSection | null>(null);
    const [selectedSubsection, setSelectedSubsection] = useState<DocumentSubsection | null>(null);

    const handleRemoveSection = (index: number) => {
        if (!onSectionsChange) return;

        const newSections = [...sections];
        newSections.splice(index, 1);
        onSectionsChange(newSections);
        toast.success('Section supprimée');
    };

    const handleRemoveSubsection = (sectionIndex: number, subsectionIndex: number) => {
        if (!onSectionsChange) return;

        const newSections = [...sections];
        if (newSections[sectionIndex] && newSections[sectionIndex].subsections) {
            newSections[sectionIndex].subsections.splice(subsectionIndex, 1);
            onSectionsChange(newSections);
            toast.success('Sous-section supprimée');
        }
    };

    const toggleSection = (index: number) => {
        setExpandedSections((prev) => ({
            ...prev,
            [index]: !prev[index],
        }));
    };

    const viewSection = (section: DocumentSection) => {
        setSelectedSection(section);
        setSelectedSubsection(null);
    };

    const viewSubsection = (subsection: DocumentSubsection) => {
        setSelectedSubsection(subsection);
        setSelectedSection(null);
    };

    const renderSection = (section: DocumentSection, index: number) => {
        const hasChildren = section.subsections && section.subsections.length > 0;
        const isExpanded = expandedSections[index] || false;

        return (
            <div key={index} className="mb-1">
                <div
                    className={`flex cursor-pointer items-center rounded-lg border p-3 transition-colors hover:bg-gray-50 ${!section.title || section.title.trim() === '' || !section.content || section.content.trim() === '' ? 'border-red-200 bg-red-50' : ''}`}
                    onClick={() => {
                        if (hasChildren) {
                            toggleSection(index);
                        } else {
                            viewSection(section);
                        }
                    }}
                >
                    {hasChildren ? (
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-5 w-5 p-0"
                            onClick={(e) => {
                                e.stopPropagation();
                                toggleSection(index);
                            }}
                        >
                            {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                        </Button>
                    ) : (
                        <FileText className="mr-1 h-4 w-4 text-blue-500" />
                    )}
                    <span className="ml-2 flex-1">
                        <span
                            className={`font-medium ${!section.title || section.title.trim() === '' || !section.content || section.content.trim() === '' ? 'text-red-600' : ''}`}
                        >
                            {section.title || `Section ${index + 1}`}
                            {(!section.title || section.title.trim() === '' || !section.content || section.content.trim() === '') && ' (Empty)'}
                        </span>
                    </span>
                    <div className="flex space-x-2">
                        <Button
                            variant="outline"
                            size="sm"
                            className="ml-2"
                            onClick={(e) => {
                                e.stopPropagation();
                                viewSection(section);
                            }}
                        >
                            <Eye className="mr-1 h-4 w-4" />
                            Voir
                        </Button>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="ml-2"
                            onClick={(e) => {
                                e.stopPropagation();
                                if (onOpenSection) {
                                    onOpenSection(section);
                                }
                            }}
                        >
                            Ouvrir
                        </Button>
                        {showDeleteButtons && (
                            <Button
                                variant="ghost"
                                size="sm"
                                className="ml-2 hover:bg-red-50 hover:text-red-600"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    if (confirm('Êtes-vous sûr de vouloir supprimer cette section?')) {
                                        handleRemoveSection(index);
                                    }
                                }}
                            >
                                <Trash className="h-4 w-4" />
                            </Button>
                        )}
                    </div>
                </div>

                {hasChildren && isExpanded && (
                    <div className="mt-1 ml-6 space-y-1">
                        {section.subsections.map((subsection, subIndex) => (
                            <div
                                key={subIndex}
                                className={`flex cursor-pointer items-center rounded-lg border p-2 transition-colors hover:bg-gray-50 ${!subsection.title || subsection.title.trim() === '' || !subsection.content || subsection.content.trim() === '' ? 'border-red-200 bg-red-50' : ''}`}
                                onClick={() => viewSubsection(subsection)}
                            >
                                <File className="h-4 w-4 text-blue-400" />
                                <span className="ml-2 flex-1 text-sm">
                                    <span
                                        className={`${!subsection.title || subsection.title.trim() === '' || !subsection.content || subsection.content.trim() === '' ? 'text-red-600' : ''}`}
                                    >
                                        {index + 1}.{subIndex + 1} {subsection.title || `Subsection ${index + 1}.${subIndex + 1}`}
                                        {(!subsection.title ||
                                            subsection.title.trim() === '' ||
                                            !subsection.content ||
                                            subsection.content.trim() === '') &&
                                            ' (Empty)'}
                                    </span>
                                </span>
                                <div className="flex space-x-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="ml-2"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            viewSubsection(subsection);
                                        }}
                                    >
                                        <Eye className="mr-1 h-4 w-4" />
                                        Voir
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="ml-2"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            if (onOpenSubsection) {
                                                onOpenSubsection(subsection);
                                            }
                                        }}
                                    >
                                        Ouvrir
                                    </Button>
                                    {showDeleteButtons && (
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="ml-2 hover:bg-red-50 hover:text-red-600"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                if (confirm('Êtes-vous sûr de vouloir supprimer cette sous-section?')) {
                                                    handleRemoveSubsection(index, subIndex);
                                                }
                                            }}
                                        >
                                            <Trash className="h-4 w-4" />
                                        </Button>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        );
    };

    return (
        <div className="flex h-full flex-col">
            <div className="mb-4">
                <h3 className="text-lg font-medium">Document Sections</h3>
            </div>

            <div className="grid h-full grid-cols-1 gap-4 md:grid-cols-2">
                <div className="max-h-[500px] overflow-y-auto">
                    <div className="space-y-2">
                        {sections.length === 0 ? (
                            <div className="py-4 text-center text-gray-500">Aucune section trouvée pour ce document.</div>
                        ) : (
                            sections.map((section, index) => renderSection(section, index))
                        )}
                    </div>
                </div>

                <div className="max-h-[500px] overflow-y-auto rounded-md border p-4">
                    {selectedSection ? (
                        <div>
                            <h3 className="mb-2 font-medium">{selectedSection.title || 'Section Content'}</h3>
                            <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: selectedSection.content }} />
                        </div>
                    ) : selectedSubsection ? (
                        <div>
                            <h3 className="mb-2 font-medium">{selectedSubsection.title || 'Subsection Content'}</h3>
                            <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: selectedSubsection.content }} />
                        </div>
                    ) : (
                        <p className="text-gray-500">Select a section or subsection to view its content.</p>
                    )}
                </div>
            </div>
        </div>
    );
}
