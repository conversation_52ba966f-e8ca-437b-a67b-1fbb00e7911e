import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ChevronDown, ChevronRight, Copy, Edit2, FileIcon, Layers, Save, Search, Trash2, X, SplitSquareVertical } from 'lucide-react';
import { useEffect, useState } from 'react';

type DatabaseDocument = {
    id: number;
    original_filename: string;
    file_hash: string;
    mime_type: string;
    file_size: number;
    html_content: string;
    metadata: {
        conversion_date?: string;
        pandoc_version?: string;
        last_edited?: string;
        edit_count?: number;
        duplicated_from?: number;
        [key: string]: string | number | boolean | null | undefined;
    };
    created_at: string;
    updated_at: string;
};

interface RecentDocumentsListProps {
    documents: DatabaseDocument[];
    isLoading: boolean;
    onDocumentOpen: (document: DatabaseDocument) => void;
    onDocumentDelete: (id: number, e: React.MouseEvent) => void;
    onViewSections: (document: DatabaseDocument) => void;
    onDocumentDuplicate: (id: number, e: React.MouseEvent) => void;
    onDocumentRename?: (id: number, newName: string) => Promise<void>;
    onCompareDocuments?: (originalId: number, duplicateId: number) => void;
}

export function RecentDocumentsList({
    documents,
    isLoading,
    onDocumentOpen,
    onDocumentDelete,
    onViewSections,
    onDocumentDuplicate,
    onDocumentRename,
    onCompareDocuments,
}: RecentDocumentsListProps) {
    const [searchQuery, setSearchQuery] = useState('');
    const [filteredDocuments, setFilteredDocuments] = useState<DatabaseDocument[]>([]);
    const [editingDocumentId, setEditingDocumentId] = useState<number | null>(null);
    const [editedName, setEditedName] = useState('');
    const [showDuplicatesForId, setShowDuplicatesForId] = useState<number | null>(null);

    // Filter documents based on search query
    useEffect(() => {
        if (!searchQuery.trim()) {
            setFilteredDocuments(documents);
        } else {
            const query = searchQuery.toLowerCase();
            setFilteredDocuments(documents.filter((doc) => doc.original_filename.toLowerCase().includes(query)));
        }
    }, [searchQuery, documents]);

    // Format date
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString(undefined, {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    // Get the name of the original document a duplicate was copied from
    const getOriginalDocumentName = (originalId: number): string => {
        const originalDoc = documents.find((doc) => doc.id === originalId);
        return originalDoc ? originalDoc.original_filename : 'Document inconnu';
    };

    // Get all duplicates of a document
    const getDuplicatesOfDocument = (documentId: number): DatabaseDocument[] => {
        return documents.filter((doc) => doc.metadata?.duplicated_from === documentId);
    };

    // Check if a document has any duplicates
    const hasDocumentDuplicates = (documentId: number): boolean => {
        return documents.some((doc) => doc.metadata?.duplicated_from === documentId);
    };

    // Toggle showing duplicates for a document
    const toggleShowDuplicates = (documentId: number, e: React.MouseEvent) => {
        e.stopPropagation();
        if (showDuplicatesForId === documentId) {
            setShowDuplicatesForId(null);
        } else {
            setShowDuplicatesForId(documentId);
        }
    };

    // Handle comparing a duplicate document with its original
    const handleCompareDocuments = (originalId: number, duplicateId: number, e: React.MouseEvent) => {
        e.stopPropagation();
        if (onCompareDocuments) {
            onCompareDocuments(originalId, duplicateId);
        }
    };

    const handleStartEditing = (document: DatabaseDocument, e: React.MouseEvent) => {
        e.stopPropagation();
        setEditingDocumentId(document.id);
        setEditedName(document.original_filename);
    };

    const handleSaveRename = async (id: number, e: React.MouseEvent) => {
        e.stopPropagation();
        if (!editedName.trim() || !onDocumentRename) return;

        try {
            await onDocumentRename(id, editedName);
            setEditingDocumentId(null);
            setEditedName('');
        } catch (error) {
            console.error('Error renaming document:', error);
        }
    };

    const handleCancelEditing = (e: React.MouseEvent) => {
        e.stopPropagation();
        setEditingDocumentId(null);
        setEditedName('');
    };

    // Handle key press in editing mode
    const handleKeyPress = (e: React.KeyboardEvent, id: number) => {
        e.stopPropagation();
        if (e.key === 'Enter') {
            handleSaveRename(id, e as unknown as React.MouseEvent);
        } else if (e.key === 'Escape') {
            handleCancelEditing(e as unknown as React.MouseEvent);
        }
    };

    return (
        <Card className="border-0 shadow-sm">
            <CardHeader className="pb-0">
                <div className="flex items-center justify-between">
                    <CardTitle>Documents récents</CardTitle>
                    <div className="relative w-64">
                        <Search className="text-muted-foreground absolute top-2.5 left-2 h-4 w-4" />
                        <input
                            type="text"
                            placeholder="Rechercher des documents..."
                            className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring h-9 w-full rounded-md border pr-4 pl-8 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                        />
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                <p className="text-muted-foreground mb-6">
                    Voici vos documents récemment convertis. Cliquez sur un document pour l'ouvrir dans l'éditeur.
                </p>

                {isLoading ? (
                    <div className="flex justify-center py-8">
                        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"></div>
                    </div>
                ) : filteredDocuments.length > 0 ? (
                    <ScrollArea className="h-[400px] pr-4">
                        <div className="space-y-2">
                            {filteredDocuments.map((document) => (
                                <div key={document.id} className="mb-1">
                                    <div
                                        onClick={() => onDocumentOpen(document)}
                                        className="flex cursor-pointer items-center justify-between rounded-lg border p-4 transition-colors hover:bg-gray-100"
                                    >
                                        <div className="flex items-center space-x-3">
                                            <FileIcon className="h-5 w-5 text-blue-500" />
                                            <div>
                                                {editingDocumentId === document.id ? (
                                                    <div onClick={(e) => e.stopPropagation()} className="flex items-center space-x-2">
                                                        <Input
                                                            value={editedName}
                                                            onChange={(e) => setEditedName(e.target.value)}
                                                            className="h-8 w-56 py-1"
                                                            autoFocus
                                                            onKeyDown={(e) => handleKeyPress(e, document.id)}
                                                        />
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            onClick={(e) => handleSaveRename(document.id, e)}
                                                            className="h-8 w-8 text-green-500 hover:bg-green-50 hover:text-green-700"
                                                            disabled={!editedName.trim()}
                                                        >
                                                            <Save className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="icon"
                                                            onClick={(e) => handleCancelEditing(e)}
                                                            className="h-8 w-8 text-gray-500 hover:bg-gray-50 hover:text-gray-700"
                                                        >
                                                            <X className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                ) : (
                                                    <div className="flex items-center space-x-2">
                                                        <p className="font-medium">{document.original_filename}</p>
                                                        {onDocumentRename && (
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={(e) => handleStartEditing(document, e)}
                                                                className="h-6 w-6 p-0 text-amber-500 hover:bg-amber-50 hover:text-amber-700"
                                                            >
                                                                <Edit2 className="h-3.5 w-3.5" />
                                                            </Button>
                                                        )}
                                                    </div>
                                                )}
                                                <p className="text-sm text-gray-500">
                                                    {formatDate(document.created_at)}
                                                    {document.metadata?.last_edited && (
                                                        <span className="ml-2 text-xs text-gray-400">
                                                            (modifié le {formatDate(document.metadata.last_edited as string)})
                                                        </span>
                                                    )}
                                                </p>
                                                {document.metadata?.duplicated_from && (
                                                    <div className="mt-1 flex items-center">
                                                        <span className="inline-flex items-center rounded-full bg-blue-50 px-2 py-0.5 text-xs font-medium text-blue-700">
                                                            <Copy className="mr-1 h-3 w-3" />
                                                            Copié de: {getOriginalDocumentName(document.metadata.duplicated_from as number)}
                                                        </span>
                                                    </div>
                                                )}
                                                {hasDocumentDuplicates(document.id) && (
                                                    <div className="mt-1">
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="h-6 px-2 py-0 text-xs text-blue-600 hover:bg-blue-50"
                                                            onClick={(e) => toggleShowDuplicates(document.id, e)}
                                                        >
                                                            {showDuplicatesForId === document.id ? (
                                                                <ChevronDown className="mr-1 h-3 w-3" />
                                                            ) : (
                                                                <ChevronRight className="mr-1 h-3 w-3" />
                                                            )}
                                                            Voir les copies ({getDuplicatesOfDocument(document.id).length})
                                                        </Button>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        <div className="flex space-x-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    onViewSections(document);
                                                }}
                                                className="text-blue-500 hover:bg-blue-50 hover:text-blue-700"
                                            >
                                                <Layers className="mr-1 h-4 w-4" />
                                                Sections
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    onDocumentDuplicate(document.id, e);
                                                }}
                                                className="text-green-500 hover:bg-green-50 hover:text-green-700"
                                                disabled={document.metadata?.duplicated_from !== undefined}
                                                title={
                                                    document.metadata?.duplicated_from !== undefined
                                                        ? 'Ce document est déjà une copie'
                                                        : 'Dupliquer ce document'
                                                }
                                            >
                                                <Copy className="mr-1 h-4 w-4" />
                                                Dupliquer
                                            </Button>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={(e) => onDocumentDelete(document.id, e)}
                                                className="text-red-500 hover:bg-red-50 hover:text-red-700"
                                            >
                                                <Trash2 className="mr-1 h-4 w-4" />
                                                Supprimer
                                            </Button>
                                        </div>
                                    </div>

                                    {/* Display duplicates if expanded */}
                                    {showDuplicatesForId === document.id && getDuplicatesOfDocument(document.id).length > 0 && (
                                        <div className="mt-1 ml-8 space-y-1">
                                            {getDuplicatesOfDocument(document.id).map((duplicate) => (
                                                <div
                                                    key={duplicate.id}
                                                    onClick={() => onDocumentOpen(duplicate)}
                                                    className="flex cursor-pointer items-center justify-between rounded-lg border border-blue-100 bg-blue-50 p-3 transition-colors hover:bg-blue-100"
                                                >
                                                    <div className="flex items-center space-x-3">
                                                        <Copy className="h-4 w-4 text-blue-500" />
                                                        <div>
                                                            <p className="font-medium">{duplicate.original_filename}</p>
                                                            <p className="text-sm text-gray-500">{formatDate(duplicate.created_at)}</p>
                                                            {duplicate.metadata?.last_edited && (
                                                                <div className="mt-1">
                                                                    <span className="inline-flex items-center rounded-full bg-amber-50 px-2 py-0.5 text-xs font-medium text-amber-700">
                                                                        Modifié le {formatDate(duplicate.metadata.last_edited as string)}
                                                                    </span>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                    <div className="flex space-x-2">
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                onViewSections(duplicate);
                                                            }}
                                                            className="text-blue-500 hover:bg-blue-200"
                                                        >
                                                            <Layers className="mr-1 h-4 w-4" />
                                                            Sections
                                                        </Button>
                                                        {onCompareDocuments && (
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={(e) => handleCompareDocuments(document.id, duplicate.id, e)}
                                                                className="text-blue-500 hover:bg-blue-200"
                                                            >
                                                                <SplitSquareVertical className="mr-1 h-4 w-4" />
                                                                Comparer
                                                            </Button>
                                                        )}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                    </ScrollArea>
                ) : (
                    <div className="py-8 text-center text-gray-500">
                        {searchQuery ? (
                            <p>Aucun document trouvé correspondant à "{searchQuery}"</p>
                        ) : (
                            <p>Aucun document trouvé. Téléchargez un document pour commencer.</p>
                        )}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
