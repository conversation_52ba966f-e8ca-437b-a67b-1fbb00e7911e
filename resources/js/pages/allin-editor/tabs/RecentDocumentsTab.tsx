import React, { useEffect } from 'react';
import { RecentDocumentsList } from '../components/RecentDocumentsList';
import { useDocumentContext } from '../context/DocumentContext';
import axios from 'axios';
import { toast } from 'sonner';
import { DatabaseDocument } from '../context/DocumentContext';

interface RecentDocumentsTabProps {
    onDocumentOpen: () => void;
    onViewSections: () => void;
}

export function RecentDocumentsTab({ onDocumentOpen, onViewSections }: RecentDocumentsTabProps) {
    const { 
        recentDocuments, 
        isLoading, 
        fetchRecentDocuments,
        openDocument,
        setCurrentDocumentId,
        fetchDocumentSections
    } = useDocumentContext();

    useEffect(() => {
        fetchRecentDocuments();
    }, []);

    const handleDocumentDelete = async (id: number, e: React.MouseEvent) => {
        e.stopPropagation();

        if (!confirm('Are you sure you want to delete this document?')) {
            return;
        }

        try {
            await axios.delete(route('pandoc-documents.destroy', { id }));
            await fetchRecentDocuments();
            toast.success('Document deleted successfully');
        } catch (error) {
            console.error('Error deleting document:', error);
            toast.error('Failed to delete document');
        }
    };

    const handleDocumentDuplicate = async (id: number, e: React.MouseEvent) => {
        e.stopPropagation();

        try {
            await axios.post(route('pandoc-documents.duplicate', { id }));
            await fetchRecentDocuments();
            toast.success('Document duplicated successfully');
        } catch (error) {
            console.error('Error duplicating document:', error);
            toast.error('Failed to duplicate document');
        }
    };

    const handleDocumentRename = async (id: number, newName: string) => {
        try {
            await axios.put(route('pandoc-documents.update', { id }), {
                original_filename: newName,
            });
            await fetchRecentDocuments();
            toast.success('Document renamed successfully');
            return Promise.resolve();
        } catch (error) {
            console.error('Error renaming document:', error);
            toast.error('Failed to rename document');
            return Promise.reject(error);
        }
    };

    const handleViewSections = (document: DatabaseDocument) => {
        setCurrentDocumentId(document.id);
        fetchDocumentSections(document.id);
        onViewSections();
    };

    const handleOpenDocument = async (document: DatabaseDocument) => {
        await openDocument(document);
        onDocumentOpen();
    };

    return (
        <RecentDocumentsList
            documents={recentDocuments}
            isLoading={isLoading}
            onDocumentOpen={handleOpenDocument}
            onDocumentDelete={handleDocumentDelete}
            onViewSections={handleViewSections}
            onDocumentDuplicate={handleDocumentDuplicate}
            onDocumentRename={handleDocumentRename}
        />
    );
}
