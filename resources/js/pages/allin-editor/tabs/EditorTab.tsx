import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Editor } from '@tiptap/react';
import { useDocumentContext } from '../context/DocumentContext';
import TiptapEditor from '../editor/TiptapEditor';
import { EditorSectionsList } from '../components/EditorSectionsList';

interface EditorTabProps {
    onSwitchToSections: () => void;
}

export function EditorTab({ onSwitchToSections }: EditorTabProps) {
    const {
        documentContent,
        showEditor,
        editingSection,
        closeEditor,
        handleDocumentSave,
        documentHtml,
        handleSplitDocument,
        setEditorRef,
        parsedSections,
        handleOpenSection,
        handleOpenSubsection,
    } = useDocumentContext();

    const handleEditorReady = (editor: Editor) => {
        setEditorRef(editor);
    };

    if (!showEditor || !documentContent) {
        return (
            <Card className="border-0 shadow-sm">
                <CardHeader className="pb-0">
                    <CardTitle>Éditeur</CardTitle>
                </CardHeader>
                <CardContent>
                    <p>Aucun document ouvert. Veuillez télécharger un document d'abord.</p>
                </CardContent>
            </Card>
        );
    }

    return (
        <div className="space-y-4">
            <Card className="w-full border-0 shadow-sm">
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle>{editingSection ? `Édition de la section: ${editingSection.sectionTitle}` : 'Document complet'}</CardTitle>
                    <div className="flex items-center space-x-2">
                        {!editingSection && (
                            <div className="flex space-x-2">
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        handleSplitDocument(documentHtml);
                                        onSwitchToSections();
                                    }}
                                    className="border-blue-600 text-blue-600 hover:border-blue-800 hover:bg-blue-50 hover:text-blue-800"
                                >
                                    Gérer les sections
                                </Button>
                            </div>
                        )}
                        <Button
                            variant="outline"
                            onClick={closeEditor}
                            className="border-red-600 text-red-600 hover:border-red-800 hover:bg-red-50 hover:text-red-800"
                        >
                            Fermer l'éditeur
                        </Button>
                    </div>
                </CardHeader>
                <CardContent>
                    {parsedSections && parsedSections.length > 0 && !editingSection && (
                        <EditorSectionsList 
                            sections={parsedSections} 
                            onOpenSection={handleOpenSection} 
                            onOpenSubsection={handleOpenSubsection} 
                        />
                    )}
                    <TiptapEditor
                        content={documentContent}
                        showBubbleMenus={true}
                        onSave={handleDocumentSave}
                        currentSectionId={editingSection ? editingSection.sectionIndex : null}
                        onEditorReady={handleEditorReady}
                        useTableDataDialog={true}
                    />
                </CardContent>
            </Card>
        </div>
    );
}
