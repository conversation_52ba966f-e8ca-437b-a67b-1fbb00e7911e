import React, { useCallback, useState } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle } from 'lucide-react';
import { FileUploadDropzone } from '../components/FileUploadDropzone';
import axios from 'axios';
import { toast } from 'sonner';
import { useDocumentContext } from '../context/DocumentContext';
import { htmlToTiptapJson } from '../lib/tiptap-extensions';

export function UploadTab({ onUploadSuccess }: { onUploadSuccess: () => void }) {
    const { 
        setDocumentContent, 
        setDocumentHtml, 
        setCurrentDocumentId, 
        setShowEditor,
        fetchRecentDocuments,
        setParsedSections
    } = useDocumentContext();

    const [isUploading, setIsUploading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [uploadProgress, setUploadProgress] = useState(0);

    const processFile = async (file: File) => {
        if (!file) return;

        if (!file.name.endsWith('.docx')) {
            setError('Please upload a valid Word document (.docx)');
            return;
        }

        const MAX_FILE_SIZE = 10 * 1024 * 1024;
        if (file.size > MAX_FILE_SIZE) {
            setError('File too large. Maximum size is 10 MB.');
            return;
        }

        setIsUploading(true);
        setError(null);
        setUploadProgress(0);

        try {
            const formData = new FormData();
            formData.append('document', file);
            const response = await axios.post(route('convert.raw-html'), formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                onUploadProgress: (progressEvent) => {
                    if (progressEvent.total) {
                        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                        setUploadProgress(percentCompleted);
                    }
                },
            });

            const html = response.data.html;
            const documentId = response.data.document_id;

            setDocumentHtml(html);
            const newDocument = htmlToTiptapJson(html);

            toast.success('Document uploaded successfully!');

            setParsedSections([]);
            
            setDocumentContent(newDocument);
            setShowEditor(true);
            setCurrentDocumentId(documentId);

            // Refresh the documents list
            await fetchRecentDocuments();

            // Switch to editor tab
            setTimeout(() => {
                onUploadSuccess();
            }, 500);
        } catch (error) {
            console.error('Conversion error:', error);
            setError('Document conversion failed. Please try again.');
            toast.error('Document conversion failed. Please try again.');
        } finally {
            setIsUploading(false);
            setUploadProgress(0);
        }
    };

    const onDrop = useCallback((acceptedFiles: File[]) => {
        if (acceptedFiles.length > 0) {
            processFile(acceptedFiles[0]);
        }
    }, []);

    return (
        <div className="space-y-6">
            <Card className="border-0 shadow-sm">
                <CardHeader className="pb-0">
                    <CardTitle>Téléchargez votre document Word</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-muted-foreground mb-6">
                        Glissez et déposez votre document Word pour le convertir au format Tiptap. Les documents sont stockés dans la
                        base de données.
                    </p>
                    <FileUploadDropzone isUploading={isUploading} uploadProgress={uploadProgress} onDrop={onDrop} />
                </CardContent>
            </Card>

            {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}
        </div>
    );
}
