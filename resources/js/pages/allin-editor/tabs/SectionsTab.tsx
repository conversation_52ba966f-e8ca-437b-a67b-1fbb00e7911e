import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DocumentSection, DocumentSubsection } from '../lib/html-section-parser';
import axios from 'axios';
import { Save } from 'lucide-react';
import { toast } from 'sonner';
import { SectionsList } from '../components/SectionsList';
import { useDocumentContext } from '../context/DocumentContext';

interface SectionsTabProps {
    onSwitchToEditor: () => void;
}

export function SectionsTab({ onSwitchToEditor }: SectionsTabProps) {
    const {
        currentDocumentId,
        parsedSections,
        isSavingSections,
        setIsSavingSections,
        fetchDocumentSections,
        handleOpenSection,
        handleOpenSubsection,
        setParsedSections,
    } = useDocumentContext();

    const handleSectionsSaved = async () => {
        if (currentDocumentId) {
            await fetchDocumentSections(currentDocumentId);
        }
    };

    const handleSaveSections = async () => {
        if (!currentDocumentId) {
            toast.error('No document ID provided');
            return;
        }

        setIsSavingSections(true);
        try {
            const response = await axios.post(route('document-sections.store-client'), {
                document_id: currentDocumentId,
                sections: parsedSections,
                replace_existing: true,
            });

            toast.success(`Saved ${response.data.section_count} sections to the database`);
            await handleSectionsSaved();
            onSwitchToEditor();
        } catch (error) {
            console.error('Error saving sections:', error);
            toast.error('Failed to save sections to the database');
        } finally {
            setIsSavingSections(false);
        }
    };

    const handleOpenSectionAndSwitch = (section: DocumentSection) => {
        handleOpenSection(section);
        onSwitchToEditor();
    };

    const handleOpenSubsectionAndSwitch = (subsection: DocumentSubsection) => {
        handleOpenSubsection(subsection);
        onSwitchToEditor();
    };

    return (
        <Card className="border-0 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle>Sections</CardTitle>
                {currentDocumentId && (
                    <div className="flex space-x-2">
                        <Button onClick={handleSaveSections} disabled={isSavingSections || parsedSections.length === 0} variant="default">
                            {isSavingSections ? (
                                <>
                                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                                    Saving...
                                </>
                            ) : (
                                <>
                                    <Save className="mr-2 h-4 w-4" />
                                    Save Sections
                                </>
                            )}
                        </Button>
                    </div>
                )}
            </CardHeader>
            <CardContent>
                {currentDocumentId ? (
                    <SectionsList
                        sections={parsedSections}
                        onOpenSection={handleOpenSectionAndSwitch}
                        onOpenSubsection={handleOpenSubsectionAndSwitch}
                        onSectionsChange={setParsedSections}
                    />
                ) : (
                    <p>Aucun document sélectionné. Veuillez ouvrir un document d'abord.</p>
                )}
            </CardContent>
        </Card>
    );
}
