import { parseHtmlIntoSections } from '../lib/html-section-parser';
import { htmlToTiptapJson } from '../lib/tiptap-extensions';
import { Editor } from '@tiptap/react';
import axios from 'axios';
import React, { createContext, ReactNode, useContext, useState } from 'react';
import { toast } from 'sonner';
import { Question } from '../services/questionService';
import { Section } from '../services/sectionService';

interface DocumentQuestion {
    id?: number;
    question_text: string;
    subsection_id?: number;
    section_id?: number;
}

interface DocumentSubsection {
    id?: number;
    title: string;
    content: string;
    subsection_number?: number;
    questions?: DocumentQuestion[];
}

interface DocumentSection {
    id?: number;
    title: string;
    content: string;
    section_number?: number;
    questions?: DocumentQuestion[];
    subsections: DocumentSubsection[];
}

export type DatabaseDocument = {
    id: number;
    original_filename: string;
    file_hash: string;
    mime_type: string;
    file_size: number;
    html_content: string;
    metadata: {
        conversion_date?: string;
        pandoc_version?: string;
        last_edited?: string;
        edit_count?: number;
        [key: string]: string | number | boolean | null | undefined;
    };
    created_at: string;
    updated_at: string;
};

interface EditingSectionInfo {
    documentId: number;
    sectionIndex: number;
    sectionTitle: string;
    originalContent: object;
}

interface DocumentContextType {
    documentContent: string | object | null;
    setDocumentContent: React.Dispatch<React.SetStateAction<string | object | null>>;
    documentHtml: string;
    setDocumentHtml: React.Dispatch<React.SetStateAction<string>>;
    currentDocumentId: number | null;
    setCurrentDocumentId: React.Dispatch<React.SetStateAction<number | null>>;
    showEditor: boolean;
    setShowEditor: React.Dispatch<React.SetStateAction<boolean>>;
    editingSection: EditingSectionInfo | null;
    recentDocuments: DatabaseDocument[];
    isLoading: boolean;
    parsedSections: DocumentSection[];
    setParsedSections: React.Dispatch<React.SetStateAction<DocumentSection[]>>;
    isSavingSections: boolean;
    setIsSavingSections: React.Dispatch<React.SetStateAction<boolean>>;
    setEditorRef: (editor: Editor | null) => void;
    fetchRecentDocuments: () => Promise<void>;
    openDocument: (document: DatabaseDocument) => Promise<void>;
    closeEditor: () => void;
    handleDocumentSave: (updatedContent: { html: string; json: object }) => Promise<void>;
    fetchDocumentSections: (documentId: number) => Promise<void>;
    handleSplitDocument: (html: string) => void;
    handleOpenSection: (section: DocumentSection) => void;
    handleOpenSubsection: (subsection: DocumentSubsection) => void;
    documentSections: Section[];
    setDocumentSections: (sections: Section[]) => void;
    currentSectionId: number | null;
    setCurrentSectionId: (sectionId: number | null) => void;
    currentSubsectionId: number | null;
    setCurrentSubsectionId: (subsectionId: number | null) => void;
    currentQuestions: Question[];
    setCurrentQuestions: (questions: Question[]) => void;
}

const DocumentContext = createContext<DocumentContextType | undefined>(undefined);

export const DocumentProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [documentContent, setDocumentContent] = useState<string | object | null>(null);
    const [documentHtml, setDocumentHtml] = useState<string>('');
    const [currentDocumentId, setCurrentDocumentId] = useState<number | null>(null);
    const [showEditor, setShowEditor] = useState(false);
    const [editingSection, setEditingSection] = useState<EditingSectionInfo | null>(null);
    const [recentDocuments, setRecentDocuments] = useState<DatabaseDocument[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [parsedSections, setParsedSections] = useState<DocumentSection[]>([]);
    const [isSavingSections, setIsSavingSections] = useState(false);
    const [documentSections, setDocumentSections] = useState<Section[]>([]);
    const [currentSectionId, setCurrentSectionId] = useState<number | null>(null);
    const [currentSubsectionId, setCurrentSubsectionId] = useState<number | null>(null);
    const [currentQuestions, setCurrentQuestions] = useState<Question[]>([]);
    // We use a ref to store the editor instance without causing re-renders
    const editorInstance = React.useRef<Editor | null>(null);
    const setEditorRef = (editor: Editor | null) => {
        editorInstance.current = editor;
    };

    const fetchRecentDocuments = async () => {
        try {
            setIsLoading(true);
            const response = await axios.get(route('pandoc-documents.index'));
            setRecentDocuments(response.data.documents);
        } catch (error) {
            console.error('Error fetching documents:', error);
            toast.error('Failed to load documents');
        } finally {
            setIsLoading(false);
        }
    };

    const openDocument = async (document: DatabaseDocument) => {
        try {
            setIsLoading(true);
            const response = await axios.get(route('pandoc-documents.show', { id: document.id }));
            const html = response.data.html;
            setDocumentHtml(html);
            const documentJson = htmlToTiptapJson(html);
            setCurrentDocumentId(document.id);
            setShowEditor(true);
            setDocumentContent(documentJson);
            setEditingSection(null);
            setParsedSections([]);
            await fetchDocumentSections(document.id);
            toast.success(`Document "${document.original_filename}" loaded`);
        } catch (error) {
            console.error('Error loading document:', error);
            toast.error('Failed to load document');
        } finally {
            setIsLoading(false);
        }
    };

    const closeEditor = () => {
        setShowEditor(false);
        setDocumentContent(null);
        setCurrentDocumentId(null);
        setEditingSection(null);
        setParsedSections([]);
    };

    const handleDocumentSave = async (updatedContent: { html: string; json: object }) => {
        if (!currentDocumentId) {
            toast.error('No document ID available for saving');
            return;
        }

        try {
            if (editingSection) {
                await axios.put(route('document-sections.update', { id: editingSection.sectionIndex }), {
                    content: updatedContent,
                });
                setDocumentContent(updatedContent.json);
                toast.success(`Section "${editingSection.sectionTitle}" enregistrée avec succès!`);
                if (currentDocumentId) {
                    await fetchDocumentSections(currentDocumentId);
                    await refreshDocumentHtml();
                }
            } else {
                await axios.put(route('pandoc-documents.update', { id: currentDocumentId }), {
                    content: updatedContent,
                });
                setDocumentContent(updatedContent.json);
                setDocumentHtml(updatedContent.html);
                toast.success('Document enregistré avec succès!');
            }
            await fetchRecentDocuments();
        } catch (error) {
            console.error('Error saving document:', error);
            toast.error("Échec de l'enregistrement du document");
        }
    };

    const refreshDocumentHtml = async () => {
        if (!currentDocumentId) return;
        try {
            const response = await axios.get(route('pandoc-documents.show', { id: currentDocumentId }));
            const html = response.data.html;
            setDocumentHtml(html);
        } catch (error) {
            console.error('Error refreshing document HTML:', error);
        }
    };

    const fetchDocumentSections = async (documentId: number) => {
        try {
            setIsLoading(true);
            const response = await axios.get(route('pandoc-documents.sections', { id: documentId }));
            const dbSections = response.data.sections || [];
            const transformedSections: DocumentSection[] = dbSections.map(
                (section: {
                    id: number;
                    title: string;
                    content: string;
                    section_number: number;
                    metadata?: {
                        [key: string]: unknown;
                    };
                    questions?: Array<{
                        id: number;
                        question_text: string;
                        section_id: number;
                    }>;
                    subsections?: Array<{
                        id: number;
                        title: string;
                        content: string;
                        subsection_number: number;
                        metadata?: {
                            [key: string]: unknown;
                        };
                        questions?: Array<{
                            id: number;
                            question_text: string;
                            subsection_id: number;
                        }>;
                    }>;
                }) => {
                    return {
                        id: section.id,
                        title: section.title,
                        content: section.content,
                        section_number: section.section_number,
                        questions:
                            section.questions?.map((question) => ({
                                id: question.id,
                                question_text: question.question_text,
                                section_id: question.section_id,
                            })) || [],
                        subsections:
                            section.subsections?.map((subsection) => ({
                                id: subsection.id,
                                title: subsection.title,
                                content: subsection.content,
                                subsection_number: subsection.subsection_number,
                                questions:
                                    subsection.questions?.map((question) => ({
                                        id: question.id,
                                        question_text: question.question_text,
                                        subsection_id: question.subsection_id,
                                    })) || [],
                            })) || [],
                    };
                },
            );
            setParsedSections(transformedSections);
            toast.success('Sections du document chargées');
        } catch (error) {
            console.error('Error fetching document sections:', error);
            toast.error('Échec du chargement des sections');
        } finally {
            setIsLoading(false);
        }
    };

    const handleSplitDocument = (html: string) => {
        try {
            const sections = parseHtmlIntoSections(html);
            setParsedSections(sections);
            setDocumentHtml(html);
            toast.success(
                `Document split into ${sections.length} sections with ${sections.reduce((total, section) => total + section.subsections.length, 0)} subsections`,
            );
        } catch (error) {
            console.error('Error parsing document into sections:', error);
            toast.error('Failed to split document into sections');
        }
    };

    const handleOpenSection = (section: DocumentSection) => {
        try {
            if (!currentDocumentId || !section.id) {
                toast.error('No document ID or section ID available');
                return;
            }

            setCurrentSectionId(section.id);
            setCurrentSubsectionId(null);

            const sectionJson = htmlToTiptapJson(section.content);
            setDocumentContent(sectionJson);
            setShowEditor(true);

            setEditingSection({
                documentId: currentDocumentId,
                sectionIndex: section.id,
                sectionTitle: section.title,
                originalContent: sectionJson,
            });

            toast.success(`Section "${section.title}" ouverte dans l'éditeur`);
        } catch (error) {
            console.error('Error opening section:', error);
            toast.error('Failed to open section');
        }
    };

    const handleOpenSubsection = (subsection: DocumentSubsection) => {
        try {
            if (!currentDocumentId || !subsection.id) {
                toast.error('No document ID or subsection ID available');
                return;
            }

            // Find the parent section
            const section = parsedSections.find((s) => s.subsections.some((sub) => sub.id === subsection.id));

            if (!section || !section.id) {
                toast.error('Parent section not found');
                return;
            }

            setCurrentSectionId(section.id);
            setCurrentSubsectionId(subsection.id);

            const json = htmlToTiptapJson(subsection.content);
            setEditingSection({
                documentId: currentDocumentId,
                sectionIndex: subsection.id,
                sectionTitle: subsection.title,
                originalContent: json,
            });
            setDocumentContent(json);
            toast.success(`Subsection "${subsection.title}" opened for editing`);
        } catch (error) {
            console.error('Error opening subsection for editing:', error);
            toast.error('Failed to open subsection for editing');
        }
    };

    const value = {
        documentContent,
        setDocumentContent,
        documentHtml,
        setDocumentHtml,
        currentDocumentId,
        setCurrentDocumentId,
        showEditor,
        setShowEditor,
        editingSection,
        recentDocuments,
        isLoading,
        parsedSections,
        setParsedSections,
        isSavingSections,
        setIsSavingSections,
        setEditorRef,
        fetchRecentDocuments,
        openDocument,
        closeEditor,
        handleDocumentSave,
        fetchDocumentSections,
        handleSplitDocument,
        handleOpenSection,
        handleOpenSubsection,
        documentSections,
        setDocumentSections,
        currentSectionId,
        setCurrentSectionId,
        currentSubsectionId,
        setCurrentSubsectionId,
        currentQuestions,
        setCurrentQuestions,
    };

    return <DocumentContext.Provider value={value}>{children}</DocumentContext.Provider>;
};

export const useDocumentContext = () => {
    const context = useContext(DocumentContext);
    if (context === undefined) {
        throw new Error('useDocumentContext must be used within a DocumentProvider');
    }
    return context;
};
