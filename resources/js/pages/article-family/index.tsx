import { frenchLocaleText } from './components/ag-grid/localization';
import { createLoadingOverlayTemplate, createNoRowsTemplate } from './components/ag-grid/templates';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuSub,
    DropdownMenuSubContent,
    DropdownMenuSubTrigger,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
    NavigationMenu,
    NavigationMenuContent,
    NavigationMenuItem,
    NavigationMenuList,
    NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import { PERMISSIONS } from '@/constants/permissions';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type SharedData } from '@/types';
import { Head, usePage } from '@inertiajs/react';
import {
    AllCommunityModule,
    ColDef,
    GridApi,
    GridReadyEvent,
    ICellRendererParams,
    IDatasource,
    IGetRowsParams,
    ModuleRegistry,
    PaginationChangedEvent,
    RowDoubleClickedEvent,
    themeAlpine,
} from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import axios from 'axios';
import { ArrowDownToLine, Database, Eye, FileText, Filter, Pencil, Plus, RefreshCw, Settings, TableProperties, Trash2 } from 'lucide-react';
import { useCallback, useRef, useState } from 'react';

// Register AG Grid Modules
ModuleRegistry.registerModules([AllCommunityModule]);

interface ArticleFamily {
    id: number;
    family_code: string;
    family_name: string;
    created_at: string;
    updated_at: string;
}

// Cell renderer component for edit button
const EditCellRenderer = (props: ICellRendererParams<ArticleFamily>) => {
    if (!props.data) return null;

    return (
        <div className="flex h-full w-full items-center justify-center">
            <Button
                variant="default"
                size="icon"
                onClick={() => props.context.handleEdit(props.data)}
                className="h-8 w-8 bg-black text-white hover:bg-black/80"
            >
                <Pencil className="size-4" />
            </Button>
        </div>
    );
};

// Cell renderer component for delete button
const DeleteCellRenderer = (props: ICellRendererParams<ArticleFamily>) => {
    if (!props.data) return null;

    return (
        <div className="flex h-full w-full items-center justify-center">
            <Button
                variant="default"
                size="icon"
                onClick={() => props.context.handleDelete(props.data)}
                className="h-8 w-8 bg-red-600 text-white hover:bg-red-700"
            >
                <Trash2 className="size-4" />
            </Button>
        </div>
    );
};

const ArticleFamilyPage = () => {
    const { auth } = usePage<SharedData>().props;

    // Permission checks
    const canViewArticleFamily = auth.permissions.includes(PERMISSIONS.VIEW_ARTICLE_FAMILY);
    const canCreateArticleFamily = auth.permissions.includes(PERMISSIONS.CREATE_ARTICLE_FAMILY);
    const canEditArticleFamily = auth.permissions.includes(PERMISSIONS.EDIT_ARTICLE_FAMILY);
    const canDeleteArticleFamily = auth.permissions.includes(PERMISSIONS.DELETE_ARTICLE_FAMILY);
    const canExportArticleFamily = auth.permissions.includes(PERMISSIONS.EXPORT_ARTICLE_FAMILY);
    const canFilterArticleFamily = auth.permissions.includes(PERMISSIONS.FILTER_ARTICLE_FAMILY);

    // Grid API reference
    const gridApiRef = useRef<GridApi<ArticleFamily> | null>(null);

    // Track if grid has been initialized at least once
    const [isGridInitialized, setIsGridInitialized] = useState<boolean>(false);

    // Modal states
    const [showViewModal, setShowViewModal] = useState<boolean>(false);
    const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
    const [showEditModal, setShowEditModal] = useState<boolean>(false);
    const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState<boolean>(false);
    const [selectedArticleFamily, setSelectedArticleFamily] = useState<ArticleFamily | null>(null);

    // Handle edit action
    const handleEdit = useCallback((articleFamily: ArticleFamily) => {
        setSelectedArticleFamily(articleFamily);
        setShowEditModal(true);
    }, []);

    // Handle delete action
    const handleDelete = useCallback((articleFamily: ArticleFamily) => {
        setSelectedArticleFamily(articleFamily);
        setShowDeleteConfirmModal(true);
    }, []);

    // Column Definitions
    const [colDefs] = useState<ColDef<ArticleFamily>[]>(() => {
        const baseColumns: ColDef<ArticleFamily>[] = [
            {
                field: 'id',
                headerName: 'ID',
                sortable: true,
                filter: true,
                width: 80,
            },
            {
                field: 'family_code',
                headerName: 'Code de la famille',
                sortable: true,
                filter: true,
            },
            {
                field: 'family_name',
                headerName: 'Nom de la famille',
                sortable: true,
                filter: true,
            },
            {
                field: 'created_at',
                headerName: 'Créé le',
                sortable: true,
                filter: true,
                valueFormatter: (params) => (params.value ? new Date(params.value).toLocaleString('fr-FR') : ''),
            },
            {
                field: 'updated_at',
                headerName: 'Modifié le',
                sortable: true,
                filter: true,
                valueFormatter: (params) => (params.value ? new Date(params.value).toLocaleString('fr-FR') : ''),
            },
        ];

        // Add action columns based on permissions
        if (canEditArticleFamily) {
            baseColumns.push({
                headerName: 'Modifier',
                cellRenderer: EditCellRenderer,
                sortable: false,
                filter: false,
                width: 100,
                flex: 0,
                cellClass: 'ag-cell-actions',
                cellStyle: { padding: 0 },
            });
        }

        if (canDeleteArticleFamily) {
            baseColumns.push({
                headerName: 'Supprimer',
                cellRenderer: DeleteCellRenderer,
                sortable: false,
                filter: false,
                width: 100,
                flex: 0,
                cellClass: 'ag-cell-actions',
                cellStyle: { padding: 0 },
            });
        }

        return baseColumns;
    });

    // Default column definitions applied to all columns
    const defaultColDef = {
        resizable: true,
        minWidth: 100,
        flex: 1,
    };

    // Context for cell renderers (passing callbacks to the cell renderer)
    const gridContext = {
        handleEdit,
        handleDelete,
    };

    const createDatasource = useCallback((): IDatasource => {
        return {
            getRows: (params: IGetRowsParams) => {
                // Get pagination parameters
                const startRow = params.startRow || 0;
                const perPage = gridApiRef.current?.paginationGetPageSize() || 20;

                // Get sorting parameters
                const sortModel = params.sortModel;
                let sortField = 'id';
                let sortOrder = 'asc';

                if (sortModel && sortModel.length > 0) {
                    sortField = sortModel[0].colId;
                    sortOrder = sortModel[0].sort || 'asc';
                }

                // Get filter parameters
                const filterModel = params.filterModel;

                // Prepare request with Laravel paginator parameters
                const requestParams = {
                    startRow,
                    perPage,
                    sortField,
                    sortOrder,
                    filterModel,
                };

                // Make API request
                axios
                    .get(route('article-families.data'), { params: requestParams })
                    .then((response) => {
                        const { rows, lastRow } = response.data;

                        // Update grid state based on response
                        if (gridApiRef.current) {
                            gridApiRef.current.setGridOption('loading', false);

                            if (!rows || rows.length === 0) {
                                gridApiRef.current.showNoRowsOverlay();
                            }
                        }

                        // Send data to the grid
                        params.successCallback(rows, lastRow);
                    })
                    .catch(() => {
                        params.failCallback();
                    });
            },
        };
    }, []);

    const onGridReady = useCallback(
        (params: GridReadyEvent) => {
            gridApiRef.current = params.api;
            setIsGridInitialized(true);
            params.api.setGridOption('loading', true);

            const dataSource = createDatasource();
            params.api.setGridOption('datasource', dataSource);
        },
        [createDatasource],
    );

    // Handle pagination changes (e.g., page size selection)
    const onPaginationChanged = useCallback((event: PaginationChangedEvent<ArticleFamily>) => {
        // Check if the grid api is available and if the change was triggered by user action (not initial load)
        if (gridApiRef.current && event.newPageSize) {
            // Refresh the cache to fetch data based on the new page size
            gridApiRef.current.refreshInfiniteCache();
        }
    }, []);

    // Handle row selection
    const onRowSelectionChanged = useCallback(() => {
        if (!gridApiRef.current) return;

        const selectedRows = gridApiRef.current.getSelectedRows();
        if (selectedRows.length > 0) {
            setSelectedArticleFamily(selectedRows[0]);
        } else {
            setSelectedArticleFamily(null);
        }
    }, []);

    // Handle refresh action
    const handleRefresh = useCallback(() => {
        if (gridApiRef.current) {
            gridApiRef.current.refreshInfiniteCache();
        }
    }, []);

    // Handle view action
    const handleView = useCallback(() => {
        if (selectedArticleFamily) {
            setShowViewModal(true);
        }
    }, [selectedArticleFamily]);

    // Handle create action
    const handleCreate = useCallback(() => {
        setShowCreateModal(true);
    }, []);

    // Handle row double click
    const onRowDoubleClicked = useCallback((event: RowDoubleClickedEvent<ArticleFamily>) => {
        if (event.data) {
            setSelectedArticleFamily(event.data);
            setShowViewModal(true);
        }
    }, []);

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: "Familles d'articles",
            href: '/article-families',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Gestion des Familles d'articles" />
            <div className="flex h-full flex-col gap-4">
                <div className="flex flex-col space-y-3">
                    {/* Navigation Menu as Action Bar */}
                    <div className="flex items-center justify-start p-2">
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                size="sm"
                                className="flex items-center gap-1"
                                onClick={handleRefresh}
                                disabled={!isGridInitialized}
                            >
                                <RefreshCw className="size-4" />
                                <span>Actualiser</span>
                            </Button>

                            {canCreateArticleFamily && (
                                <Button
                                    variant="default"
                                    size="sm"
                                    className="flex items-center gap-1 bg-black text-white hover:bg-black/80"
                                    onClick={handleCreate}
                                >
                                    <Plus className="size-4" />
                                    <span>Nouvelle famille</span>
                                </Button>
                            )}

                            {canViewArticleFamily && (
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="flex items-center gap-1"
                                    onClick={handleView}
                                    disabled={!selectedArticleFamily}
                                >
                                    <Eye className="size-4" />
                                    <span>Voir les détails</span>
                                </Button>
                            )}

                            {canExportArticleFamily && (
                                <NavigationMenu>
                                    <NavigationMenuList>
                                        <NavigationMenuItem>
                                            <NavigationMenuTrigger className="h-9 px-4 py-2 text-sm font-medium">Plus d'actions</NavigationMenuTrigger>
                                            <NavigationMenuContent>
                                                <div className="flex w-[160px] flex-col p-2">
                                                    <button
                                                        className="hover:bg-accent flex items-center gap-2 rounded-sm px-2 py-1.5 text-left text-sm"
                                                        onClick={() => console.log('Export to CSV')}
                                                    >
                                                        <FileText className="size-4" />
                                                        Exporter en CSV
                                                    </button>
                                                    <button
                                                        className="hover:bg-accent flex items-center gap-2 rounded-sm px-2 py-1.5 text-left text-sm"
                                                        onClick={() => console.log('Export to Excel')}
                                                    >
                                                        <ArrowDownToLine className="size-4" />
                                                        Exporter en Excel
                                                    </button>
                                                </div>
                                            </NavigationMenuContent>
                                        </NavigationMenuItem>
                                    </NavigationMenuList>
                                </NavigationMenu>
                            )}

                            {canFilterArticleFamily && (
                                <NavigationMenu>
                                    <NavigationMenuList>
                                        <NavigationMenuItem>
                                            <NavigationMenuTrigger className="h-9 px-4 py-2 text-sm font-medium">
                                                <Filter className="mr-1 size-4" />
                                                Filtres
                                            </NavigationMenuTrigger>
                                            <NavigationMenuContent>
                                                <div className="flex w-[220px] flex-col p-2">
                                                    <div className="mb-1 px-2 py-1">
                                                        <p className="text-sm font-medium">Filtrer par statut</p>
                                                    </div>
                                                    <button className="hover:bg-accent rounded-sm px-2 py-1.5 text-left text-sm">
                                                        Toutes les familles
                                                    </button>
                                                    <button className="hover:bg-accent rounded-sm px-2 py-1.5 text-left text-sm">Familles actives</button>
                                                    <button className="hover:bg-accent rounded-sm px-2 py-1.5 text-left text-sm">
                                                        Familles inactives
                                                    </button>
                                                    <div className="my-1 border-t" />
                                                    <div className="mb-1 px-2 py-1">
                                                        <p className="text-sm font-medium">Filtres rapides</p>
                                                    </div>
                                                    <button className="hover:bg-accent rounded-sm px-2 py-1.5 text-left text-sm">
                                                        Créées ce mois-ci
                                                    </button>
                                                    <button className="hover:bg-accent rounded-sm px-2 py-1.5 text-left text-sm">
                                                        Mises à jour récemment
                                                    </button>
                                                </div>
                                            </NavigationMenuContent>
                                        </NavigationMenuItem>
                                    </NavigationMenuList>
                                </NavigationMenu>
                            )}

                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="outline" size="sm" className="flex items-center gap-1">
                                        <Settings className="size-4" />
                                        <span>Options</span>
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent className="w-56">
                                    <DropdownMenuLabel>Paramètres du tableau</DropdownMenuLabel>
                                    <DropdownMenuSeparator />

                                    <DropdownMenuItem>
                                        <TableProperties className="mr-2 size-4" />
                                        Paramètres des colonnes
                                    </DropdownMenuItem>

                                    {canExportArticleFamily && (
                                        <DropdownMenuSub>
                                            <DropdownMenuSubTrigger>
                                                <Database className="mr-2 size-4" />
                                                Options d'exportation
                                            </DropdownMenuSubTrigger>
                                            <DropdownMenuSubContent className="w-48">
                                                <DropdownMenuItem>
                                                    <FileText className="mr-2 size-4" />
                                                    Exporter en CSV
                                                </DropdownMenuItem>
                                                <DropdownMenuItem>
                                                    <ArrowDownToLine className="mr-2 size-4" />
                                                    Exporter en Excel
                                                </DropdownMenuItem>
                                                <DropdownMenuSeparator />
                                                <DropdownMenuSub>
                                                    <DropdownMenuSubTrigger>Exportation avancée</DropdownMenuSubTrigger>
                                                    <DropdownMenuSubContent className="w-48">
                                                        <DropdownMenuItem>Lignes sélectionnées uniquement</DropdownMenuItem>
                                                        <DropdownMenuItem>Avec données filtrées</DropdownMenuItem>
                                                        <DropdownMenuItem>Ensemble des données</DropdownMenuItem>
                                                    </DropdownMenuSubContent>
                                                </DropdownMenuSub>
                                            </DropdownMenuSubContent>
                                        </DropdownMenuSub>
                                    )}

                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem onClick={handleRefresh}>
                                        <RefreshCw className="mr-2 size-4" />
                                        Actualiser les données
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    </div>
                </div>

                {/* AG Grid Component */}
                <div className="flex-1 overflow-hidden">
                    <div className="h-full w-full">
                        <AgGridReact
                            columnDefs={colDefs}
                            defaultColDef={defaultColDef}
                            animateRows={true}
                            rowModelType="infinite"
                            cacheBlockSize={100}
                            cacheOverflowSize={2}
                            maxConcurrentDatasourceRequests={1}
                            infiniteInitialRowCount={100}
                            maxBlocksInCache={10}
                            onGridReady={onGridReady}
                            pagination={true}
                            paginationPageSizeSelector={[10, 20, 50, 100]}
                            paginationPageSize={20}
                            onRowDoubleClicked={onRowDoubleClicked}
                            onSelectionChanged={onRowSelectionChanged}
                            theme={themeAlpine}
                            overlayLoadingTemplate={createLoadingOverlayTemplate("Chargement des familles d'articles...")}
                            overlayNoRowsTemplate={createNoRowsTemplate("Aucune famille d'articles trouvée")}
                            debug={false}
                            rowSelection={{
                                mode: 'singleRow',
                            }}
                            paginationNumberFormatter={(params) => {
                                return params.value.toLocaleString('fr-FR');
                            }}
                            context={gridContext}
                            localeText={frenchLocaleText}
                            onPaginationChanged={onPaginationChanged}
                        />
                    </div>
                </div>

                {/* View Article Family Modal */}
                <Dialog open={showViewModal} onOpenChange={setShowViewModal}>
                    <DialogContent className="sm:max-w-3xl">
                        <DialogHeader>
                            <DialogTitle>
                                {selectedArticleFamily?.family_name
                                    ? `Détails de la famille - ${selectedArticleFamily.family_name}`
                                    : "Détails de la famille d'articles"}
                            </DialogTitle>
                        </DialogHeader>
                        {selectedArticleFamily && (
                            <div className="grid grid-cols-2 gap-4 py-4">
                                <div>
                                    <p className="mb-2 text-sm font-medium text-gray-500">ID</p>
                                    <p>{selectedArticleFamily.id}</p>
                                </div>
                                <div>
                                    <p className="mb-2 text-sm font-medium text-gray-500">Code de la famille</p>
                                    <p>{selectedArticleFamily.family_code}</p>
                                </div>
                                <div>
                                    <p className="mb-2 text-sm font-medium text-gray-500">Nom de la famille</p>
                                    <p>{selectedArticleFamily.family_name}</p>
                                </div>
                                <div>
                                    <p className="mb-2 text-sm font-medium text-gray-500">Créé le</p>
                                    <p>
                                        {selectedArticleFamily.created_at ? new Date(selectedArticleFamily.created_at).toLocaleString('fr-FR') : ''}
                                    </p>
                                </div>
                                <div>
                                    <p className="mb-2 text-sm font-medium text-gray-500">Modifié le</p>
                                    <p>
                                        {selectedArticleFamily.updated_at ? new Date(selectedArticleFamily.updated_at).toLocaleString('fr-FR') : ''}
                                    </p>
                                </div>
                            </div>
                        )}
                        <DialogFooter>
                            <Button variant="outline" onClick={() => setShowViewModal(false)}>
                                Fermer
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {/* Create Article Family Modal */}
                <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
                    <DialogContent className="sm:max-w-3xl">
                        <DialogHeader>
                            <DialogTitle>Créer une nouvelle famille d'articles</DialogTitle>
                        </DialogHeader>
                        <div className="py-4">
                            <p className="text-muted-foreground mb-4">Le formulaire sera implémenté ultérieurement.</p>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="text-muted-foreground flex h-12 items-center justify-center rounded-md border border-dashed">
                                    Code de la famille
                                </div>
                                <div className="text-muted-foreground flex h-12 items-center justify-center rounded-md border border-dashed">
                                    Nom de la famille
                                </div>
                            </div>
                        </div>
                        <DialogFooter className="gap-2">
                            <Button variant="outline" onClick={() => setShowCreateModal(false)}>
                                Annuler
                            </Button>
                            <Button>Créer</Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {/* Edit Article Family Modal */}
                <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
                    <DialogContent className="sm:max-w-3xl">
                        <DialogHeader>
                            <DialogTitle>
                                {selectedArticleFamily?.family_name
                                    ? `Modifier la famille - ${selectedArticleFamily.family_name}`
                                    : "Modifier la famille d'articles"}
                            </DialogTitle>
                        </DialogHeader>
                        <div className="py-4">
                            <p className="text-muted-foreground mb-4">Le formulaire de modification sera implémenté ultérieurement.</p>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="text-muted-foreground flex h-12 items-center justify-center rounded-md border border-dashed">
                                    Code de la famille
                                </div>
                                <div className="text-muted-foreground flex h-12 items-center justify-center rounded-md border border-dashed">
                                    Nom de la famille
                                </div>
                            </div>
                        </div>
                        <DialogFooter className="gap-2">
                            <Button variant="outline" onClick={() => setShowEditModal(false)}>
                                Annuler
                            </Button>
                            <Button>Enregistrer</Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {/* Delete Confirmation Modal */}
                <Dialog open={showDeleteConfirmModal} onOpenChange={setShowDeleteConfirmModal}>
                    <DialogContent className="sm:max-w-md">
                        <DialogHeader>
                            <DialogTitle>Confirmer la suppression</DialogTitle>
                        </DialogHeader>
                        <div className="py-4">
                            <p>Êtes-vous sûr de vouloir supprimer la famille {selectedArticleFamily?.family_name} ? Cette action est irréversible.</p>
                        </div>
                        <DialogFooter className="gap-2">
                            <Button variant="outline" onClick={() => setShowDeleteConfirmModal(false)}>
                                Annuler
                            </Button>
                            <Button
                                variant="destructive"
                                onClick={() => {
                                    console.log('Suppression de la famille', selectedArticleFamily);
                                    setShowDeleteConfirmModal(false);
                                }}
                            >
                                Supprimer
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
};

export default ArticleFamilyPage;
