import { GoogleGenAI } from '@google/genai';
import { DynamicSectionsResponse } from '@/pages/demo-llm/types';

interface DocumentChat {
    chat: any; // Using any for now to avoid complex typing issues with Gemini SDK
    documentId: string;
    createdAt: Date;
}

class GeminiService {
    private ai: GoogleGenAI | null = null;
    private apiKey: string | null = null;
    private documentChats: Map<string, DocumentChat> = new Map();

    constructor() {
        this.apiKey = import.meta.env.VITE_GEMINI_API_KEY;
        if (this.apiKey) {
            this.ai = new GoogleGenAI({ apiKey: this.apiKey });
        }
    }

    private isConfigured(): boolean {
        return !!(this.ai && this.apiKey);
    }

    async analyzeAndSplitContent(htmlContent: string, temperature: number = 0.5): Promise<DynamicSectionsResponse> {
        if (!this.isConfigured()) {
            throw new Error('Gemini API key is not configured. Please set VITE_GEMINI_API_KEY in your environment variables.');
        }

        const prompt = `Analyze the following HTML content and split it into logical sections based on content structure.

ANALYSIS RULES:
1. Look for headings (h1, h2, h3) as natural section breaks
2. Identify ordered lists (<ol>) as potential separate sections
3. Find topic changes in content flow
4. Detect standalone elements (blockquotes, tables, etc.)

SECTION CREATION RULES:
- Create 1-8 sections based on content complexity
- Each section should have meaningful, substantial content
- Avoid creating sections with just 1-2 sentences
- Preserve HTML structure and relationships

RESPONSE FORMAT:
Return ONLY a JSON object with this exact structure:
{
  "totalSections": number,
  "sections": {
    "section1": {
      "title": "descriptive title",
      "html": "HTML content for this section"
    },
    "section2": {
      "title": "descriptive title",
      "html": "HTML content for this section"
    }
    // ... more sections as needed
  }
}

EXAMPLES OF GOOD SPLITS:
- Introduction + Main Content + Conclusion
- Overview + Step-by-step List + Additional Notes
- Problem + Solution + Implementation + Results

HTML Content to Analyze:
${htmlContent}`;

        try {
            const response = await this.ai!.models.generateContent({
                model: 'gemini-2.5-flash-preview-05-20',
                contents: prompt,
                config: {
                    temperature: temperature,
                },
            });

            const responseText = response.text;

            if (!responseText) {
                throw new Error('No content received from Gemini API');
            }

            // Try to parse JSON response
            let parsedResponse: DynamicSectionsResponse;
            try {
                // Clean the response text (remove any markdown formatting)
                const cleanedResponse = responseText.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
                parsedResponse = JSON.parse(cleanedResponse);
            } catch {
                console.error('Failed to parse JSON response:', responseText);
                throw new Error('AI returned invalid JSON format. Please try again.');
            }

            // Validate the response structure
            if (!parsedResponse.totalSections || !parsedResponse.sections) {
                throw new Error('AI response missing required fields (totalSections, sections)');
            }

            // Ensure we have at least 1 section and at most 8 sections
            const sectionCount = parsedResponse.totalSections;
            if (sectionCount < 1 || sectionCount > 8) {
                throw new Error(`Invalid section count: ${sectionCount}. Must be between 1 and 8.`);
            }

            // Validate that we have the expected number of sections
            const actualSections = Object.keys(parsedResponse.sections).length;
            if (actualSections !== sectionCount) {
                console.warn(`Section count mismatch: expected ${sectionCount}, got ${actualSections}`);
                parsedResponse.totalSections = actualSections;
            }

            return parsedResponse;

        } catch (error) {
            console.error('Error analyzing and splitting content with Gemini:', error);

            if (error instanceof Error) {
                if (error.message.includes('API_KEY_INVALID')) {
                    throw new Error('Invalid Gemini API key. Please check your VITE_GEMINI_API_KEY environment variable.');
                } else if (error.message.includes('QUOTA_EXCEEDED')) {
                    throw new Error('Gemini API quota exceeded. Please try again later.');
                } else if (error.message.includes('JSON') || error.message.includes('section')) {
                    throw error; // Re-throw our custom validation errors
                } else {
                    throw new Error(`Gemini API error: ${error.message}`);
                }
            }

            throw new Error('Failed to analyze and split content. Please try again.');
        }
    }

    private generateDocumentId(htmlContent: string): string {
        return btoa(htmlContent.substring(0, 100)).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
    }

    private cleanupOldChats(): void {
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
        for (const [key, documentChat] of this.documentChats.entries()) {
            if (documentChat.createdAt < oneHourAgo) {
                this.documentChats.delete(key);
            }
        }
    }

    async createDocumentChat(htmlContent: string, temperature: number = 0.7): Promise<string> {
        if (!this.isConfigured()) {
            throw new Error('Gemini API key is not configured. Please set VITE_GEMINI_API_KEY in your environment variables.');
        }

        const documentId = this.generateDocumentId(htmlContent);

        this.cleanupOldChats();

        if (this.documentChats.has(documentId)) {
            return documentId;
        }

        try {
            const chat = this.ai!.chats.create({
                model: 'gemini-2.5-flash-preview-05-20',
                config: {
                    temperature: temperature,
                    systemInstruction: `You are an AI assistant helping users understand and analyze their document content.

INSTRUCTIONS:
- Answer questions based on the document content provided
- Be helpful, accurate, and concise
- If a question cannot be answered from the document content, politely explain that
- Provide specific references to the document when possible
- Use a conversational and friendly tone`,
                },
                history: [
                    {
                        role: 'user',
                        parts: [{ text: `Please analyze this document content: ${htmlContent}` }],
                    },
                    {
                        role: 'model',
                        parts: [{ text: "I've received and analyzed your document. I'm ready to answer any questions you have about its content. What would you like to know?" }],
                    },
                ],
            });

            this.documentChats.set(documentId, {
                chat,
                documentId,
                createdAt: new Date(),
            });

            return documentId;

        } catch (error) {
            console.error('Error creating document chat:', error);
            throw this.handleGeminiError(error);
        }
    }

    async *chatWithDocumentStream(documentId: string, userMessage: string): AsyncGenerator<string, void, unknown> {
        if (!this.isConfigured()) {
            throw new Error('Gemini API key is not configured. Please set VITE_GEMINI_API_KEY in your environment variables.');
        }

        const documentChat = this.documentChats.get(documentId);
        if (!documentChat) {
            throw new Error('Document chat not found. Please create a new chat session.');
        }

        try {
            const stream = await documentChat.chat.sendMessageStream({
                message: userMessage,
            });

            for await (const chunk of stream) {
                if (chunk.text) {
                    yield chunk.text;
                }
            }

        } catch (error) {
            console.error('Error streaming chat with document:', error);
            throw this.handleGeminiError(error);
        }
    }

    private handleGeminiError(error: unknown): Error {
        if (error instanceof Error) {
            if (error.message.includes('API_KEY_INVALID')) {
                return new Error('Invalid Gemini API key. Please check your VITE_GEMINI_API_KEY environment variable.');
            } else if (error.message.includes('QUOTA_EXCEEDED')) {
                return new Error('Gemini API quota exceeded. Please try again later.');
            } else {
                return new Error(`Gemini API error: ${error.message}`);
            }
        }
        return new Error('Failed to get response from AI. Please try again.');
    }

    getConfigurationStatus(): {
        isConfigured: boolean;
        hasApiKey: boolean;
        message: string;
    } {
        const hasApiKey = !!this.apiKey;
        const isConfigured = this.isConfigured();

        let message = '';
        if (!hasApiKey) {
            message = 'Gemini API key is not set. Please add VITE_GEMINI_API_KEY to your environment variables.';
        } else if (!isConfigured) {
            message = 'Gemini service is not properly configured.';
        } else {
            message = 'Gemini service is ready.';
        }

        return {
            isConfigured,
            hasApiKey,
            message
        };
    }
}

// Export a singleton instance
export const geminiService = new GeminiService();
