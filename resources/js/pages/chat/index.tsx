import React, { useState, useRef, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

import { geminiService } from './utils/geminiService';
import { Upload, Send, Bot, User } from 'lucide-react';
import axios from 'axios';
import { MarkdownRenderer } from './components/MarkdownRenderer';

interface ChatMessage {
    id: string;
    type: 'user' | 'ai';
    content: string;
    timestamp: Date;
}

export default function Chat() {
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [isUploading, setIsUploading] = useState(false);
    const [uploadSuccess, setUploadSuccess] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
    const [userMessage, setUserMessage] = useState('');
    const [isSending, setIsSending] = useState(false);
    const [documentChatId, setDocumentChatId] = useState<string | null>(null);
    const [isInitializingChat, setIsInitializingChat] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const chatEndRef = useRef<HTMLDivElement>(null);

    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setSelectedFile(file);
            setError(null);
            // Automatically upload the selected file
            await handleFileUpload(file);
        }
    };

    const handleFileUpload = async (file?: File) => {
        const fileToUpload = file || selectedFile;
        if (!fileToUpload) {
            setError('Please select a DOCX file');
            return;
        }

        setIsUploading(true);
        setError(null);

        const formData = new FormData();
        formData.append('document', fileToUpload);

        try {
            const response = await axios.post(route('convert.raw-html'), formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            if (response.data.html) {
                setIsInitializingChat(true);
                try {
                    const chatId = await geminiService.createDocumentChat(response.data.html);
                    setDocumentChatId(chatId);
                    setUploadSuccess(true);
                    setError(null);

                    // Add default AI welcome message
                    const welcomeMessage: ChatMessage = {
                        id: Date.now().toString(),
                        type: 'ai',
                        content: `📄 **Document uploaded successfully!**

I've analyzed your document \`${fileToUpload.name}\` and I'm ready to help you with any questions about its content. You can ask me to:

- Summarize the document
- Extract key information
- Answer specific questions about the content
- Explain complex sections
- Find particular details

What would you like to know about your document?`,
                        timestamp: new Date()
                    };

                    setChatMessages([welcomeMessage]);
                } catch (chatError) {
                    const errorMessage = chatError instanceof Error ? chatError.message : 'Failed to initialize chat with AI';
                    setError(errorMessage);
                } finally {
                    setIsInitializingChat(false);
                }
            } else {
                setError('Conversion completed but no HTML was returned.');
            }
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to upload and convert document';
            setError(errorMessage);
        } finally {
            setIsUploading(false);
        }
    };

    const handleSendMessage = async () => {
        if (!userMessage.trim() || !documentChatId) return;

        const newUserMessage: ChatMessage = {
            id: Date.now().toString(),
            type: 'user',
            content: userMessage.trim(),
            timestamp: new Date()
        };

        setChatMessages(prev => [...prev, newUserMessage]);
        setUserMessage('');
        setIsSending(true);

        try {
            // Stream the response
            const stream = geminiService.chatWithDocumentStream(documentChatId, newUserMessage.content);

            let aiMessageId: string | null = null;
            let isFirstChunk = true;

            for await (const chunk of stream) {
                if (isFirstChunk) {
                    // Create AI message on first chunk
                    aiMessageId = (Date.now() + 1).toString();
                    const newAiMessage: ChatMessage = {
                        id: aiMessageId,
                        type: 'ai',
                        content: chunk,
                        timestamp: new Date()
                    };
                    setChatMessages(prev => [...prev, newAiMessage]);
                    isFirstChunk = false;
                } else {
                    // Append to existing message
                    setChatMessages(prev =>
                        prev.map(msg =>
                            msg.id === aiMessageId
                                ? { ...msg, content: msg.content + chunk }
                                : msg
                        )
                    );
                }
            }
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to get AI response';
            setError(errorMessage);
        } finally {
            setIsSending(false);
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    // Auto-scroll to bottom when new messages are added
    useEffect(() => {
        chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [chatMessages, isSending]);

    return (
        <AppLayout>
            <div className="flex flex-col h-full">
                <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    accept=".docx"
                    className="hidden"
                    disabled={isUploading}
                />
                <Card className="flex flex-col h-full m-4 overflow-hidden">
                            {/* Chat Messages */}
                            <ScrollArea className="flex-1 min-h-0">
                                <div className="space-y-4 p-4 max-w-full">
                                    {chatMessages.length === 0 ? (
                                        <div className="flex items-center justify-center h-full min-h-[200px]">
                                            <div className="text-center">
                                                <Bot className="mx-auto w-12 h-12 text-muted-foreground mb-4" />
                                                <p className="text-lg text-muted-foreground">
                                                    {!uploadSuccess ? "Upload a document to start chatting!" : "Ask me anything about your document!"}
                                                </p>
                                            </div>
                                        </div>
                                    ) : (
                                        <>
                                            {chatMessages.map((message) => (
                                                <div
                                                    key={message.id}
                                                    className={`flex gap-3 w-full ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                                                >
                                                    {message.type === 'ai' && (
                                                        <Avatar className="flex-shrink-0">
                                                            <AvatarFallback className="bg-primary/10">
                                                                <Bot className="w-4 h-4 text-primary" />
                                                            </AvatarFallback>
                                                        </Avatar>
                                                    )}
                                                    <div
                                                        className={`${
                                                            message.type === 'user'
                                                                ? 'max-w-xs lg:max-w-2xl'
                                                                : 'max-w-xs sm:max-w-sm md:max-w-md lg:max-w-3xl xl:max-w-4xl'
                                                        } px-4 py-3 rounded-lg break-words overflow-hidden ${
                                                            message.type === 'user'
                                                                ? 'bg-primary text-primary-foreground'
                                                                : 'bg-muted border'
                                                        }`}
                                                    >
                                                        {message.type === 'ai' ? (
                                                            <div className="text-sm leading-relaxed overflow-hidden">
                                                                <MarkdownRenderer content={message.content} />
                                                            </div>
                                                        ) : (
                                                            <p className="text-sm whitespace-pre-wrap leading-relaxed break-words">{message.content}</p>
                                                        )}
                                                    </div>
                                                    {message.type === 'user' && (
                                                        <Avatar className="flex-shrink-0">
                                                            <AvatarFallback>
                                                                <User className="w-4 h-4" />
                                                            </AvatarFallback>
                                                        </Avatar>
                                                    )}
                                                </div>
                                            ))}
                                            {isSending && (
                                                <div className="flex gap-3 justify-start w-full">
                                                    <Avatar className="flex-shrink-0">
                                                        <AvatarFallback className="bg-primary/10">
                                                            <Bot className="w-4 h-4 text-primary" />
                                                        </AvatarFallback>
                                                    </Avatar>
                                                    <div className="bg-muted border rounded-lg px-4 py-3 max-w-xs sm:max-w-sm md:max-w-md lg:max-w-3xl xl:max-w-4xl">
                                                        <div className="flex space-x-1">
                                                            <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                                                            <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                                            <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            )}
                                            <div ref={chatEndRef} />
                                        </>
                                    )}
                                </div>
                            </ScrollArea>

                            {/* Message Input */}
                            <CardContent className="flex-shrink-0 py-4">
                                {/* Error Status */}
                                {error && (
                                    <div className="mb-3 p-2 rounded-lg bg-destructive/10 text-sm">
                                        <div className="text-destructive">
                                            {error}
                                        </div>
                                    </div>
                                )}
                                <div className="flex gap-3">
                                    <Textarea
                                        value={userMessage}
                                        onChange={(e) => setUserMessage(e.target.value)}
                                        onKeyDown={handleKeyDown}
                                        placeholder={!uploadSuccess ? "Upload a document first to start chatting..." : "Ask me anything about your document..."}
                                        className="flex-1 min-h-[50px] max-h-32 resize-none"
                                        disabled={isSending || !uploadSuccess}
                                    />
                                    <Button
                                        onClick={handleSendMessage}
                                        disabled={!userMessage.trim() || isSending || !documentChatId}
                                        className="px-4 py-2"
                                    >
                                        <Send className="h-4 w-4" />
                                    </Button>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => fileInputRef.current?.click()}
                                        disabled={isUploading || isInitializingChat}
                                        className="flex items-center gap-2 px-4 py-2"
                                    >
                                        <Upload className="h-4 w-4" />
                                        {isUploading ? 'Converting...' : isInitializingChat ? 'Initializing...' : 'Upload'}
                                    </Button>
                                </div>
                            </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
