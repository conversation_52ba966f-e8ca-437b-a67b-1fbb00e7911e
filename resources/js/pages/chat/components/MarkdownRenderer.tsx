import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Maximize2 } from 'lucide-react';

interface MarkdownRendererProps {
    content: string;
}

// Custom table component with dialog functionality
const TableWithDialog: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    return (
        <div className="my-6 overflow-hidden rounded-lg border border-border shadow-sm relative max-w-full">
            {/* Expand button - always visible */}
            <Dialog>
                <DialogTrigger asChild>
                    <Button
                        variant="outline"
                        size="sm"
                        className="absolute top-2 right-2 z-10 bg-background/90 backdrop-blur-sm shadow-sm"
                    >
                        <Maximize2 className="h-3 w-3" />
                        <span className="sr-only">Open table in dialog</span>
                    </Button>
                </DialogTrigger>
                <DialogContent className="max-w-6xl max-h-[80vh] overflow-hidden">
                    <DialogHeader>
                        <DialogTitle>Table View</DialogTitle>
                    </DialogHeader>
                    <div className="overflow-auto max-h-[60vh]">
                        <Table className="w-full">
                            {children}
                        </Table>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Regular table view with proper overflow handling */}
            <div className="overflow-x-auto max-w-full">
                <Table className="w-full min-w-full">
                    {children}
                </Table>
            </div>
        </div>
    );
};

export const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content }) => {
    return (
        <div className="max-w-full overflow-hidden">
            <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                    // Enhanced table components with professional styling and dialog
                    table: ({ children }) => <TableWithDialog>{children}</TableWithDialog>,
                    thead: ({ children }) => (
                        <TableHeader className="bg-muted/50">
                            {children}
                        </TableHeader>
                    ),
                    tbody: ({ children }) => (
                        <TableBody className="divide-y divide-border">
                            {children}
                        </TableBody>
                    ),
                    tr: ({ children }) => (
                        <TableRow className="hover:bg-muted/30 transition-colors duration-150">
                            {children}
                        </TableRow>
                    ),
                    th: ({ children }) => (
                        <TableHead className="font-semibold text-foreground bg-muted/50 px-4 py-3 text-left border-r border-border last:border-r-0 break-words">
                            {children}
                        </TableHead>
                    ),
                    td: ({ children }) => (
                        <TableCell className="px-4 py-3 text-muted-foreground border-r border-border/50 last:border-r-0 align-top break-words">
                            {children}
                        </TableCell>
                    ),
                    // Simple inline code styling with word break
                    code: ({ children }) => (
                        <code className="bg-black text-white px-1 py-0.5 rounded text-sm font-mono break-all">
                            {children}
                        </code>
                    ),
                    // Headings with word break
                    h1: ({ children }) => (
                        <h1 className="text-2xl font-bold mb-4 mt-6 break-words">
                            {children}
                        </h1>
                    ),
                    h2: ({ children }) => (
                        <h2 className="text-xl font-semibold mb-3 mt-5 break-words">
                            {children}
                        </h2>
                    ),
                    h3: ({ children }) => (
                        <h3 className="text-lg font-medium mb-2 mt-4 break-words">
                            {children}
                        </h3>
                    ),
                    // Paragraphs and text with word break
                    p: ({ children }) => (
                        <p className="mb-3 leading-relaxed break-words">
                            {children}
                        </p>
                    ),
                    // Lists with word break
                    ul: ({ children }) => (
                        <ul className="list-disc list-inside mb-3 space-y-1 break-words">
                            {children}
                        </ul>
                    ),
                    ol: ({ children }) => (
                        <ol className="list-decimal list-inside mb-3 space-y-1 break-words">
                            {children}
                        </ol>
                    ),
                    li: ({ children }) => (
                        <li className="ml-2 break-words">
                            {children}
                        </li>
                    ),
                    // Blockquotes with word break
                    blockquote: ({ children }) => (
                        <blockquote className="border-l-4 border-muted-foreground/25 pl-4 my-4 italic text-muted-foreground break-words">
                            {children}
                        </blockquote>
                    ),
                    // Text formatting
                    strong: ({ children }) => (
                        <strong className="font-semibold break-words">
                            {children}
                        </strong>
                    ),
                    em: ({ children }) => (
                        <em className="italic break-words">
                            {children}
                        </em>
                    ),
                }}
            >
                {content}
            </ReactMarkdown>
        </div>
    );
};
