import ExcelJS from 'exceljs';
import { ParsedArticle, ParsedFinishedProduct } from './types';

/**
 * Parse a boolean value from Excel
 * @param value - The value to parse
 * @returns The parsed boolean
 */
export function parseBoolean(value: unknown): boolean {
    if (typeof value === 'boolean') return value;
    if (typeof value === 'string') {
        const normalized = value.toLowerCase().trim();
        return normalized === 'oui' || normalized === 'true' || normalized === '1';
    }
    return Boolean(value);
}

/**
 * Utility function to parse Excel files
 * @param file - The Excel file to parse
 * @returns Promise with the parsed data and headers
 */
export async function parseExcelFile(file: File): Promise<{
    headers: string[];
    data: ParsedFinishedProduct[];
    rawData: Record<string, unknown>[];
    hasErrors: boolean;
    errors: { row: number; message: string }[];
}> {
    const workbook = new ExcelJS.Workbook();
    const arrayBuffer = await file.arrayBuffer();

    await workbook.xlsx.load(arrayBuffer);

    const worksheet = workbook.getWorksheet(1); // Get the first worksheet
    const rawData: Record<string, unknown>[] = [];
    const errors: { row: number; message: string }[] = [];

    if (!worksheet) {
        return { headers: [], data: [], rawData: [], hasErrors: true, errors: [{ row: 0, message: 'Aucune feuille trouvée dans le fichier Excel' }] };
    }

    // Get headers from the first row
    const headers: string[] = [];
    worksheet.getRow(1).eachCell((cell) => {
        headers.push(cell.value as string);
    });

    // Check if required headers are present
    const requiredHeaders = ['Code Article', 'Code Vrac', 'Est Complexe', 'Taille Lot'];
    const missingHeaders = requiredHeaders.filter((header) => !headers.includes(header));

    if (missingHeaders.length > 0) {
        return {
            headers,
            data: [],
            rawData: [],
            hasErrors: true,
            errors: [
                {
                    row: 0,
                    message: `En-têtes manquants: ${missingHeaders.join(', ')}`,
                },
            ],
        };
    }

    // Get data from the rest of the rows
    worksheet.eachRow((row, rowNumber) => {
        if (rowNumber > 1) {
            // Skip header row
            const rowData: Record<string, unknown> = {};
            let isEmpty = true;

            row.eachCell((cell, colNumber) => {
                if (colNumber <= headers.length) {
                    rowData[headers[colNumber - 1]] = cell.value;
                    if (cell.value !== null && cell.value !== undefined && cell.value !== '') {
                        isEmpty = false;
                    }
                }
            });

            // Skip completely empty rows
            if (!isEmpty) {
                rawData.push(rowData);
            }
        }
    });

    // Map raw data to structured data
    const data: ParsedFinishedProduct[] = rawData.map((row, index) => {
        const rowNumber = index + 2; // +2 because excel is 1-indexed and we skipped header
        const rowErrors: string[] = [];

        // Validate required fields
        if (!row['Code Article']) {
            rowErrors.push('Code Article est requis');
        }

        if (!row['Code Vrac']) {
            rowErrors.push('Code Vrac est requis');
        }

        if (!row['Taille Lot'] || isNaN(Number(row['Taille Lot']))) {
            rowErrors.push('Taille Lot doit être un nombre valide');
        }

        // Add row errors if any
        if (rowErrors.length > 0) {
            errors.push({
                row: rowNumber,
                message: rowErrors.join(', '),
            });
        }

        // Parse values with proper types
        const isComplex = parseBoolean(row['Est Complexe'] || 'Non');
        const targetCoverage = Number(row['Couverture Cible (Mois)'] || (isComplex ? 4 : 3));

        return {
            articleCode: String(row['Code Article'] || ''),
            articleName: String(row['Libellé Article'] || ''),
            availableStock: isNaN(Number(row['Stock Disponible'])) ? 0 : Number(row['Stock Disponible']),
            bulkCode: String(row['Code Vrac'] || ''),
            isComplex,
            isSample: parseBoolean(row['Est Echantillon'] || 'Non'),
            productType: String(row['Type Produit'] || 'OFFICINAL').toUpperCase() === 'HOSPITALIER' ? 'HOSPITALIER' : 'OFFICINAL',
            batchSize: isNaN(Number(row['Taille Lot'])) ? 0 : Number(row['Taille Lot']),
            targetCoverageMonths: Math.max(3, Math.min(6, targetCoverage)), // Ensure between 3-6
            monthlyBudget: isNaN(Number(row['Budget Mensuel'])) ? 0 : Number(row['Budget Mensuel']),
            plannedQuantity: isNaN(Number(row['Quantité Prévue'])) ? 0 : Number(row['Quantité Prévue']),
        };
    });

    return {
        headers,
        data,
        rawData,
        hasErrors: errors.length > 0,
        errors,
    };
}

/**
 * Utility function to parse Article Excel files
 * @param file - The Excel file to parse
 * @returns Promise with the parsed data and headers
 */
export async function parseArticleExcelFile(file: File): Promise<{
    headers: string[];
    data: ParsedArticle[];
    rawData: Record<string, unknown>[];
    hasErrors: boolean;
    errors: { row: number; message: string }[];
}> {
    const workbook = new ExcelJS.Workbook();
    const arrayBuffer = await file.arrayBuffer();

    await workbook.xlsx.load(arrayBuffer);

    const worksheet = workbook.getWorksheet(1); // Get the first worksheet
    const rawData: Record<string, unknown>[] = [];
    const errors: { row: number; message: string }[] = [];

    if (!worksheet) {
        return { headers: [], data: [], rawData: [], hasErrors: true, errors: [{ row: 0, message: 'Aucune feuille trouvée dans le fichier Excel' }] };
    }

    // Get headers from the first row
    const headers: string[] = [];
    worksheet.getRow(1).eachCell((cell) => {
        headers.push(cell.value as string);
    });

    // Check if required headers are present
    const requiredHeaders = ['TCLCOD_0', 'ITMREF_0', 'CPNITMREF_0', 'BOMQTY_0', 'BOMUOM_0'];
    const missingHeaders = requiredHeaders.filter((header) => !headers.includes(header));

    if (missingHeaders.length > 0) {
        return {
            headers,
            data: [],
            rawData: [],
            hasErrors: true,
            errors: [
                {
                    row: 0,
                    message: `En-têtes manquants: ${missingHeaders.join(', ')}`,
                },
            ],
        };
    }

    // Get data from the rest of the rows
    worksheet.eachRow((row, rowNumber) => {
        if (rowNumber > 1) {
            // Skip header row
            const rowData: Record<string, unknown> = {};
            let isEmpty = true;

            row.eachCell((cell, colNumber) => {
                if (colNumber <= headers.length) {
                    rowData[headers[colNumber - 1]] = cell.value;
                    if (cell.value !== null && cell.value !== undefined && cell.value !== '') {
                        isEmpty = false;
                    }
                }
            });

            // Skip completely empty rows
            if (!isEmpty) {
                rawData.push(rowData);
            }
        }
    });

    // Map raw data to structured data
    const data: ParsedArticle[] = rawData.map((row, index) => {
        const rowNumber = index + 2; // +2 because excel is 1-indexed and we skipped header
        const rowErrors: string[] = [];

        // Validate required fields
        if (!row['TCLCOD_0']) {
            rowErrors.push('TCLCOD_0 est requis');
        }

        if (!row['ITMREF_0']) {
            rowErrors.push('ITMREF_0 est requis');
        }

        if (!row['CPNITMREF_0']) {
            rowErrors.push('CPNITMREF_0 est requis');
        }

        // Add row errors if any
        if (rowErrors.length > 0) {
            errors.push({
                row: rowNumber,
                message: rowErrors.join(', '),
            });
        }

        // Parse values with proper types
        return {
            tclcod: String(row['TCLCOD_0'] || ''),
            itmref: String(row['ITMREF_0'] || ''),
            cpnitmref: String(row['CPNITMREF_0'] || ''),
            bomqty: isNaN(Number(row['BOMQTY_0'])) ? 0 : Number(row['BOMQTY_0']),
            bomuom: String(row['BOMUOM_0'] || ''),
        };
    });

    return {
        headers,
        data,
        rawData,
        hasErrors: errors.length > 0,
        errors,
    };
}
