/**
 * Interface for parsed Excel data of finished products
 */
export interface ParsedFinishedProduct {
    articleCode: string;
    articleName: string;
    availableStock: number;
    bulkCode: string;
    isComplex: boolean;
    isSample: boolean;
    productType: string;
    batchSize: number;
    targetCoverageMonths: number;
    monthlyBudget: number;
    plannedQuantity: number;
    [key: string]: unknown;
}

/**
 * Interface for parsed Excel data of articles
 */
export interface ParsedArticle {
    tclcod: string;
    itmref: string;
    cpnitmref: string;
    bomqty: number;
    bomuom: string;
}

/**
 * Interface for production order data to be exported
 */
export interface ExportProductionOrder {
    id: number;
    article_code: string;
    article_name: string;
    bulk_code: string;
    batch_count: number;
    created_at: string;
    notes: string;
}

/**
 * Column mapping for custom exports
 */
export const COLUMN_MAPPING: Record<string, string> = {
    id: 'ID OF',
    article_code: 'Code Article',
    bulk_code: 'Code Vrac',
    designation: 'Désignation',
    status: 'Statut',
    batch_count: 'Nombre de Lots',
    created_at: 'Date de Création',
    notes: 'Notes',
};

/**
 * Status mapping for display in Excel
 */
export const STATUS_MAPPING: Record<string, string> = {
    pending: 'En attente',
    in_progress: 'En cours',
    completed: 'Terminé',
    cancelled: 'Annulé',
};
