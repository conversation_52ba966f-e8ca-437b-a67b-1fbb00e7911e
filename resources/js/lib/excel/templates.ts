import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';

/**
 * Generates an Excel template for finished products import
 * @returns {Promise<void>} - Promise that resolves when the Excel file is generated and downloaded
 */
export async function generateFinishedProductsTemplate(): Promise<void> {
    // Create a new workbook
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'Dar Essaydali System';
    workbook.lastModifiedBy = 'Dar Essaydali';
    workbook.created = new Date();
    workbook.modified = new Date();

    // Create main sheet
    const sheet = workbook.addWorksheet('Produits Finis');

    // Define column headers
    const headers = [
        'Code Article',
        'Libellé Article',
        'Stock Disponible',
        'Code Vrac',
        'Est Complexe',
        'Est Echantillon',
        'Type Produit',
        'Taille Lot',
        'Couverture Cible (Mois)',
        'Budget Mensuel',
        'Quantité Prévue',
    ];

    // Add header row
    const headerRow = sheet.addRow(headers);

    // Style header row
    headerRow.eachCell((cell) => {
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {
                argb: '4472C4',
            },
        };
        cell.font = {
            bold: true,
            color: {
                argb: 'FFFFFF',
            },
        };
        cell.border = {
            top: {
                style: 'thin',
            },
            left: {
                style: 'thin',
            },
            bottom: {
                style: 'thin',
            },
            right: {
                style: 'thin',
            },
        };
        cell.alignment = {
            horizontal: 'center',
            vertical: 'middle',
        };
    });

    // Set column widths
    headers.forEach((header, i) => {
        sheet.getColumn(i + 1).width = Math.max(15, header.length + 2);
    });

    // Add sample data
    const sampleData = [
        ['ART001', 'Paracétamol 500mg', 100, 'V001', 'Non', 'Non', 'OFFICINAL', 1000, 3, 50, 0],
        ['ART002', 'Aspirine 1000mg', 200, 'V002', 'Oui', 'Non', 'HOSPITALIER', 500, 4, 75, 0],
        ['ART003', 'Amoxicilline 1g', 150, 'V003', 'Non', 'Oui', 'OFFICINAL', 800, 3, 100, 0],
    ];

    // Add sample data rows
    sampleData.forEach((rowData) => {
        sheet.addRow(rowData);
    });

    // Apply data validations
    // Est Complexe (column E) - dropdown list
    sheet.getColumn(5).eachCell(
        {
            includeEmpty: false,
        },
        (cell, rowNumber) => {
            if (rowNumber > 1) {
                sheet.getCell(`E${rowNumber}`).dataValidation = {
                    type: 'list',
                    allowBlank: true,
                    formulae: ['"Oui,Non"'],
                };
            }
        },
    );

    // Est Echantillon (column F) - dropdown list
    sheet.getColumn(6).eachCell(
        {
            includeEmpty: false,
        },
        (cell, rowNumber) => {
            if (rowNumber > 1) {
                sheet.getCell(`F${rowNumber}`).dataValidation = {
                    type: 'list',
                    allowBlank: true,
                    formulae: ['"Oui,Non"'],
                };
            }
        },
    );

    // Type Produit (column G) - dropdown list
    sheet.getColumn(7).eachCell(
        {
            includeEmpty: false,
        },
        (cell, rowNumber) => {
            if (rowNumber > 1) {
                sheet.getCell(`G${rowNumber}`).dataValidation = {
                    type: 'list',
                    allowBlank: true,
                    formulae: ['"OFFICINAL,HOSPITALIER"'],
                };
            }
        },
    );

    // Couverture Cible (column I) - number range validation
    sheet.getColumn(9).eachCell(
        {
            includeEmpty: false,
        },
        (cell, rowNumber) => {
            if (rowNumber > 1) {
                sheet.getCell(`I${rowNumber}`).dataValidation = {
                    type: 'whole',
                    operator: 'between',
                    formulae: [3, 6],
                    showErrorMessage: true,
                    errorStyle: 'error',
                    errorTitle: 'Valeur invalide',
                    error: 'La valeur doit être entre 3 et 6.',
                };
            }
        },
    );

    // Positive numbers validation for relevant columns
    [3, 8, 10, 11].forEach((colIndex) => {
        sheet.getColumn(colIndex).eachCell(
            {
                includeEmpty: false,
            },
            (cell, rowNumber) => {
                if (rowNumber > 1) {
                    cell.dataValidation = {
                        type: 'decimal',
                        operator: 'greaterThanOrEqual',
                        formulae: [0],
                        showErrorMessage: true,
                        errorStyle: 'error',
                        errorTitle: 'Valeur invalide',
                        error: 'La valeur doit être positive ou zéro.',
                    };
                }
            },
        );
    });

    // Add a help sheet
    const helpSheet = workbook.addWorksheet('Instructions');

    // Add title
    helpSheet.mergeCells('A1:D1');
    const titleCell = helpSheet.getCell('A1');
    titleCell.value = "Instructions pour l'importation des produits finis";
    titleCell.font = {
        bold: true,
        size: 14,
    };
    titleCell.alignment = {
        horizontal: 'center',
    };

    // Required fields
    helpSheet.getCell('A3').value = 'Champs obligatoires:';
    helpSheet.getCell('A3').font = {
        bold: true,
    };

    const requiredFields = [
        ['Code Article', "Identifiant unique de l'article (si le code existe déjà, le produit sera mis à jour)"],
        ['Code Vrac', 'Identifiant du produit en vrac'],
        ['Est Complexe', 'Indique si le produit a un processus de fabrication complexe (Oui/Non)'],
        ['Taille Lot', "Taille standard d'un lot de production (nombre positif)"],
    ];

    requiredFields.forEach((field, index) => {
        helpSheet.getCell(`A${index + 4}`).value = '- ' + field[0];
        helpSheet.getCell(`B${index + 4}`).value = field[1];
    });

    // Optional fields
    helpSheet.getCell('A' + (requiredFields.length + 5)).value = 'Champs optionnels:';
    helpSheet.getCell('A' + (requiredFields.length + 5)).font = {
        bold: true,
    };

    const optionalFields = [
        ['Libellé Article', "Nom de l'article (si le code existe déjà, cette valeur sera ignorée)"],
        ['Stock Disponible', 'Stock actuel (nombre positif)'],
        ['Est Echantillon', "Indique s'il s'agit d'un échantillon (Oui/Non, défaut: Non)"],
        ['Type Produit', 'OFFICINAL ou HOSPITALIER (défaut: OFFICINAL)'],
        ['Couverture Cible (Mois)', 'Couverture de stock cible en mois (3-6, défaut: 3)'],
        ['Budget Mensuel', 'Ventes mensuelles prévues (nombre positif, défaut: 0)'],
        ['Quantité Prévue', 'Quantité de production prévue (nombre positif, défaut: 0)'],
    ];

    optionalFields.forEach((field, index) => {
        helpSheet.getCell(`A${requiredFields.length + 6 + index}`).value = '- ' + field[0];
        helpSheet.getCell(`B${requiredFields.length + 6 + index}`).value = field[1];
    });

    // Notes
    helpSheet.getCell('A' + (requiredFields.length + optionalFields.length + 7)).value = 'Notes importantes:';
    helpSheet.getCell('A' + (requiredFields.length + optionalFields.length + 7)).font = {
        bold: true,
    };

    const notes = [
        'La famille article est automatiquement définie comme "PR".',
        'Les produits complexes ont une couverture cible conseillée de 4-6 mois.',
        'Les produits non-complexes ont une couverture cible conseillée de 3-6 mois.',
        'Pour mettre à jour un article existant, fournissez son code article.',
        'Les valeurs Oui/Non doivent être exactement orthographiées ainsi.',
        'Les types de produit doivent être exactement orthographiés: OFFICINAL ou HOSPITALIER.',
        'Toutes les valeurs numériques doivent être positives ou nulles.',
    ];

    notes.forEach((note, index) => {
        helpSheet.getCell(`A${requiredFields.length + optionalFields.length + 8 + index}`).value = `${index + 1}. ${note}`;
        helpSheet.mergeCells(
            `A${requiredFields.length + optionalFields.length + 8 + index}:D${requiredFields.length + optionalFields.length + 8 + index}`,
        );
    });

    // Set column widths in help sheet
    helpSheet.getColumn('A').width = 25;
    helpSheet.getColumn('B').width = 60;

    // Save the workbook
    const buffer = await workbook.xlsx.writeBuffer();
    saveAs(new Blob([buffer]), 'Template_Produits_Finis.xlsx');
}
