/**
 * Upload and import Excel file to the server
 * @param file - The Excel file to upload
 * @returns Promise with the import results
 */
export async function importFinishedProducts(file: File): Promise<{
    success: boolean;
    message: string;
    created: number;
    updated: number;
    errors: string[];
    errorRows: number[];
}> {
    try {
        const formData = new FormData();
        formData.append('file', file);

        // Get the CSRF token from the meta tag
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        if (!csrfToken) {
            console.error('CSRF token not found');
            throw new Error('CSRF token not found');
        }

        // Add the CSRF token to the form data instead of using headers
        formData.append('_token', csrfToken);

        const response = await fetch('/finished-products/import', {
            method: 'POST',
            // Don't set X-CSRF-TOKEN header when using FormData with the token included
            body: formData,
        });

        const result = await response.json();

        return {
            success: result.success,
            message: result.message,
            created: result.created || 0,
            updated: result.updated || 0,
            errors: result.errors || [],
            errorRows: result.error_rows || [],
        };
    } catch (error) {
        return {
            success: false,
            message: "Erreur de connexion lors de l'importation",
            created: 0,
            updated: 0,
            errors: [error instanceof Error ? error.message : 'Erreur inconnue'],
            errorRows: [],
        };
    }
}
