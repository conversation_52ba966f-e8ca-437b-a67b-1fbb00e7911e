import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import { toast } from 'sonner';
import { COLUMN_MAPPING, ExportProductionOrder, STATUS_MAPPING } from './types';

/**
 * Exports all in-progress production orders to an Excel file
 * @returns {Promise<void>} - Promise that resolves when the Excel file is generated and downloaded
 */
export async function exportInProgressOrdersToExcel(): Promise<void> {
    try {
        // Fetch the data from the backend
        const response = await fetch('/production-orders/export/in-progress');
        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message || 'Failed to fetch export data');
        }

        const orders: ExportProductionOrder[] = result.data;

        if (orders.length === 0) {
            // Show a toast message if there are no orders to export
            toast.info('Aucun ordre de production en cours à exporter');
            return;
        }

        // Create a new workbook
        const workbook = new ExcelJS.Workbook();
        workbook.creator = 'Dar E<PERSON>ydali System';
        workbook.lastModifiedBy = '<PERSON>';
        workbook.created = new Date();
        workbook.modified = new Date();

        // Create a worksheet
        const worksheet = workbook.addWorksheet('Ordres En Cours');

        // Add title row
        const titleRow = worksheet.addRow(['Rapport: Ordres de Fabrication - Lancé']);
        titleRow.font = { bold: true, size: 14 };
        worksheet.mergeCells(`A1:G1`);
        titleRow.getCell(1).alignment = { horizontal: 'left' };

        // Add date row
        const currentDate = new Date();
        const dateString = `Date d'export: ${currentDate.toLocaleDateString('fr-FR')} ${currentDate.toLocaleTimeString('fr-FR')}`;
        const dateRow = worksheet.addRow([dateString]);
        worksheet.mergeCells(`A2:G2`);
        dateRow.getCell(1).alignment = { horizontal: 'left' };

        // Add empty row
        worksheet.addRow([]);

        // Define column headers
        const headers = ['ID OF', 'C.Article', 'Code Vrac', 'Désignation', 'Nombre de Lots', 'Date de Création', 'Notes'];

        // Add header row (at row 4 now)
        const headerRow = worksheet.addRow(headers);

        // Style header row
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: {
                    argb: '4472C4',
                },
            };
            cell.font = {
                bold: true,
                color: {
                    argb: 'FFFFFF',
                },
            };
            cell.border = {
                top: {
                    style: 'thin',
                },
                left: {
                    style: 'thin',
                },
                bottom: {
                    style: 'thin',
                },
                right: {
                    style: 'thin',
                },
            };
            cell.alignment = {
                horizontal: 'center',
                vertical: 'middle',
            };
        });

        // Set column widths
        headers.forEach((header, i) => {
            worksheet.getColumn(i + 1).width = Math.max(15, header.length + 2);
        });

        // Add data rows
        orders.forEach((order) => {
            worksheet.addRow([order.id, order.article_code, order.bulk_code, order.article_name, order.batch_count, order.created_at, order.notes]);
        });

        // Apply borders to all data cells
        for (let i = 5; i <= worksheet.rowCount; i++) {
            worksheet.getRow(i).eachCell((cell) => {
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' },
                };
            });
        }

        // Auto-filter for all columns
        worksheet.autoFilter = {
            from: { row: 4, column: 1 },
            to: { row: 4, column: headers.length },
        };

        // Save the workbook
        const buffer = await workbook.xlsx.writeBuffer();
        saveAs(new Blob([buffer]), `Ordres_En_Cours_${new Date().toISOString().slice(0, 10)}.xlsx`);

        // Show success message
        toast.success(`${orders.length} ordres de production exportés avec succès`);
    } catch (error) {
        console.error('Error exporting in-progress orders:', error);

        // Show error message
        toast.error("Erreur lors de l'exportation: " + (error instanceof Error ? error.message : 'Erreur inconnue'));
    }
}

/**
 * Exports production orders based on custom filters to an Excel file
 * @param {string[]} columns - The columns to include in the export
 * @param {string[]} statuses - The statuses to filter by
 * @returns {Promise<void>} - Promise that resolves when the Excel file is generated and downloaded
 */
export async function exportCustomOrdersToExcel(columns: string[], statuses: string[]): Promise<void> {
    try {
        // Check if columns are selected
        if (columns.length === 0) {
            toast.error("Veuillez sélectionner au moins une colonne pour l'export");
            return;
        }

        // Map column IDs to their actual data keys
        const columnKeys: string[] = [];
        const columnNames: string[] = [];

        columns.forEach((columnId) => {
            let dataKey = '';
            switch (columnId) {
                case 'id':
                    dataKey = 'id';
                    break;
                case 'article':
                    dataKey = 'article_code';
                    break;
                case 'bulk_code':
                    dataKey = 'bulk_code';
                    break;
                case 'designation':
                    dataKey = 'designation';
                    break;
                case 'status':
                    dataKey = 'status';
                    break;
                case 'batch_count':
                    dataKey = 'batch_count';
                    break;
                case 'created_at':
                    dataKey = 'created_at';
                    break;
                case 'notes':
                    dataKey = 'notes';
                    break;
            }

            if (dataKey) {
                columnKeys.push(dataKey);
                columnNames.push(COLUMN_MAPPING[dataKey] || dataKey);
            }
        });

        // Fetch data from the backend
        const response = await fetch('/production-orders/export/custom', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            },
            body: JSON.stringify({
                columns: columnKeys,
                statuses,
            }),
        });

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message || 'Failed to fetch export data');
        }

        const orders = result.data;

        if (orders.length === 0) {
            toast.info('Aucun ordre de production correspondant aux critères sélectionnés');
            return;
        }

        // Create a new workbook
        const workbook = new ExcelJS.Workbook();
        workbook.creator = 'Dar Essaydali System';
        workbook.lastModifiedBy = 'Dar Essaydali';
        workbook.created = new Date();
        workbook.modified = new Date();

        // Create a worksheet
        const worksheet = workbook.addWorksheet('Ordres de Production');

        // Add header row
        const headerRow = worksheet.addRow(columnNames);

        // Style header row
        headerRow.eachCell((cell) => {
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: {
                    argb: '4472C4',
                },
            };
            cell.font = {
                bold: true,
                color: {
                    argb: 'FFFFFF',
                },
            };
            cell.border = {
                top: {
                    style: 'thin',
                },
                left: {
                    style: 'thin',
                },
                bottom: {
                    style: 'thin',
                },
                right: {
                    style: 'thin',
                },
            };
            cell.alignment = {
                horizontal: 'center',
                vertical: 'middle',
            };
        });

        // Set column widths
        columnNames.forEach((header, i) => {
            worksheet.getColumn(i + 1).width = Math.max(15, header.length + 2);
        });

        // Add data rows
        orders.forEach((order: Record<string, string | number>) => {
            const rowData: (string | number)[] = [];

            columnKeys.forEach((key) => {
                let value = order[key];

                // Format status values if present
                if (key === 'status' && value in STATUS_MAPPING) {
                    value = STATUS_MAPPING[value as keyof typeof STATUS_MAPPING];
                }

                rowData.push(value || '');
            });

            worksheet.addRow(rowData);
        });

        // Auto-filter for all columns
        worksheet.autoFilter = {
            from: { row: 1, column: 1 },
            to: { row: 1, column: columnNames.length },
        };

        // Save the workbook
        const buffer = await workbook.xlsx.writeBuffer();

        // Create filename with status information
        let statusInfo = '';
        if (statuses.includes('all')) {
            statusInfo = 'Tous';
        } else {
            const statusNames = statuses.map((s) => STATUS_MAPPING[s] || s).join('_');
            statusInfo = statusNames;
        }

        saveAs(new Blob([buffer]), `Ordres_${statusInfo}_${new Date().toISOString().slice(0, 10)}.xlsx`);

        // Show success message
        toast.success(`${orders.length} ordres de production exportés avec succès`);

        return;
    } catch (error) {
        console.error('Error exporting orders:', error);
        toast.error("Erreur lors de l'exportation: " + (error instanceof Error ? error.message : 'Erreur inconnue'));
    }
}
