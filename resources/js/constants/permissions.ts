/**
 * Centralized permission constants
 *
 * This file contains all permission names used throughout the application.
 * When you need to change a permission name, update it here and it will
 * automatically apply everywhere it's used.
 *
 * Usage: import { PERMISSIONS } from '@/constants/permissions';
 * Then use: PERMISSIONS.VIEW_USER instead of 'view-user'
 */

export const PERMISSIONS = {
    // User Management
    VIEW_USER: 'view-user',
    CREATE_USER: 'create-user',
    EDIT_USER: 'edit-user',
    DELETE_USER: 'delete-user',

    // Role Management
    VIEW_ROLE: 'view-role',
    CREATE_ROLE: 'create-role',
    EDIT_ROLE: 'edit-role',
    DELETE_ROLE: 'delete-role',

    // Permission Management
    VIEW_PERMISSION: 'view-permission',
    ASSIGN_PERMISSION: 'assign-permission',

    // Article Family Management
    VIEW_ARTICLE_FAMILY: 'view-article-family',
    CREATE_ARTICLE_FAMILY: 'create-article-family',
    EDIT_ARTICLE_FAMILY: 'edit-article-family',
    DELETE_ARTICLE_FAMILY: 'delete-article-family',
    EXPORT_ARTICLE_FAMILY: 'export-article-family',
    FILTER_ARTICLE_FAMILY: 'filter-article-family',

    // Article Management
    VIEW_ARTICLE: 'view-article',
    CREATE_ARTICLE: 'create-article',
    EDIT_ARTICLE: 'edit-article',
    DELETE_ARTICLE: 'delete-article',
    IMPORT_ARTICLE: 'import-article',
} as const;

/**
 * Type for all available permissions
 * This ensures type safety when using permission constants
 */
export type Permission = (typeof PERMISSIONS)[keyof typeof PERMISSIONS];
