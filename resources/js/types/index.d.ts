import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User | null;
    permissions: string[];
}


export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
}

export interface NavSubItem {
    title: string;
    href: string;
    permission?: string;
    items?: NavSubItem[];
}

export interface NavItemWithChildren {
    title: string;
    href: string;
    icon?: LucideIcon;
    isActive?: boolean;
    items?: NavSubItem[];
    permission?: string;
}

export interface ProjectItem {
    name: string;
    url: string;
    icon: LucideIcon;
}

export interface SecondaryNavItem {
    title: string;
    url: string;
    icon: LucideIcon;
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
    [key: string]: unknown;
}

export interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    [key: string]: unknown; // This allows for additional properties...
}
