import { NavMain } from '@/components/nav-main';
import { NavProjects } from '@/components/nav-projects';
import { NavSecondary } from '@/components/nav-secondary';
import { NavUser } from '@/components/nav-user';
import { <PERSON>bar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { PERMISSIONS } from '@/constants/permissions';
import { ProjectItem, SecondaryNavItem, type NavItemWithChildren, type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { BarChart3, Building2, FileText, Package, Package2, Settings2, Users } from 'lucide-react';
import AppLogo from './app-logo';

const getMainNavItems = (permissions: string[]): NavItemWithChildren[] => {
    const allNavItems: NavItemWithChildren[] = [
        {
            title: 'Données de Base',
            href: '/article-families',
            icon: Package,
            items: [
                {
                    title: "Familles d'articles",
                    href: '/article-families',
                    permission: PERMISSIONS.VIEW_ARTICLE_FAMILY,
                    items: [
                        {
                            title: 'Catégories',
                            href: '/article-families/categories',
                            items: [
                                {
                                    title: 'Médicaments',
                                    href: '/article-families/categories/medicaments',
                                    items: [
                                        {
                                            title: 'Antibiotiques',
                                            href: '/article-families/categories/medicaments/antibiotiques',
                                        },
                                        {
                                            title: 'Antalgiques',
                                            href: '/article-families/categories/medicaments/antalgiques',
                                        },
                                    ],
                                },
                                {
                                    title: 'Dispositifs Médicaux',
                                    href: '/article-families/categories/dispositifs',
                                },
                            ],
                        },
                        {
                            title: 'Sous-catégories',
                            href: '/article-families/subcategories',
                        },
                    ],
                },
                {
                    title: 'Articles',
                    href: '/articles',
                    permission: PERMISSIONS.VIEW_ARTICLE
                },
            ],
        },
        {
            title: 'Dossier Lot Électronique',
            href: '/allin-editor',
            icon: FileText,
            items: [
                {
                    title: 'DLE',
                    href: '/allin-editor',
                },
                {
                    title: 'Séparation Doc (LLM)',
                    href: '/document-llm',
                },
                {
                    title: 'Discussion avec doc',
                    href: '/document-chat',
                },
            ],
        },
        {
            title: 'categories',
            href: '/categories',
            icon: Package2
        },
        {
            title: 'Paramétrage',
            href: '/users',
            icon: Users,
            permission: PERMISSIONS.VIEW_USER,
            items: [
                {
                    title: 'Utilisateurs',
                    href: '/users',
                    permission: PERMISSIONS.VIEW_USER,
                },
                {
                    title: 'Rôles',
                    href: '/roles',
                    permission: PERMISSIONS.VIEW_ROLE,
                },
                {
                    title: 'Permissions',
                    href: '/permissions',
                    permission: PERMISSIONS.VIEW_PERMISSION,
                },
            ],
        },
        {
            title: 'Sécurité',
            href: '/settings',
            icon: Settings2,
            items: [
                {
                    title: 'Profil',
                    href: '/settings/profile',
                },
                {
                    title: 'Mot de passe',
                    href: '/settings/password',
                },
            ],
        },
    ];

    return allNavItems.map(item => {
        const filteredItems = item.items?.filter(subItem =>
            !subItem.permission || permissions.includes(subItem.permission)
        );

        const hasVisibleItems = !item.items || (filteredItems && filteredItems.length > 0);
        const hasPermission = !item.permission || permissions.includes(item.permission);

        if (!hasPermission || !hasVisibleItems) {
            return null;
        }

        return {
            ...item,
            items: filteredItems,
        };
    }).filter(Boolean) as NavItemWithChildren[];
};

const projects: ProjectItem[] = [
    {
        name: 'Laravel App',
        url: '/projects/laravel-app',
        icon: Building2,
    },
    {
        name: 'Analytics Dashboard',
        url: '/projects/analytics',
        icon: BarChart3,
    },
    {
        name: 'Documentation Site',
        url: '/projects/docs',
        icon: FileText,
    },
];

const navSecondary: SecondaryNavItem[] = [
    {
        title: 'AdvantryX',
        url: 'https://advantryx.com',
        icon: Building2,
    },
];

export function AppSidebar() {
    const { auth } = usePage<SharedData>().props;
    const mainNavItems = getMainNavItems(auth.permissions);    

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                                    <AppLogo />
                                </div>
                                <div className="grid flex-1 text-left text-sm leading-tight">
                                    <span className="truncate font-medium">PharMes</span>
                                    <span className="truncate text-xs">AdvantryX</span>
                                </div>
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={mainNavItems} />
                <NavProjects projects={projects} />
                <NavSecondary items={navSecondary} className="mt-auto" />
            </SidebarContent>

            <SidebarFooter>
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
