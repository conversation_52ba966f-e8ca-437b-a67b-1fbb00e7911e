import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { XIcon } from "lucide-react"

import { cn } from "@/lib/utils"

function ExpandedDialog({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Root>) {
  return <DialogPrimitive.Root data-slot="expanded-dialog" {...props} />
}

function ExpandedDialogTrigger({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {
  return <DialogPrimitive.Trigger data-slot="expanded-dialog-trigger" {...props} />
}

function ExpandedDialogPortal({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Portal>) {
  return <DialogPrimitive.Portal data-slot="expanded-dialog-portal" {...props} />
}

function ExpandedDialogClose({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Close>) {
  return <DialogPrimitive.Close data-slot="expanded-dialog-close" {...props} />
}

function ExpandedDialogOverlay({
  className,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {
  return (
    <DialogPrimitive.Overlay
      data-slot="expanded-dialog-overlay"
      className={cn(
        "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50 backdrop-blur-sm",
        className
      )}
      {...props}
    />
  )
}

function ExpandedDialogContent({
  className,
  children,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Content>) {
  return (
    <ExpandedDialogPortal>
      <ExpandedDialogOverlay />
      <DialogPrimitive.Content
        data-slot="expanded-dialog-content"
        className={cn(
          "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed z-50 grid w-[calc(100%-2rem)] gap-4 rounded-lg border shadow-lg duration-200",
          // Responsive sizing and positioning
          "top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%]",
          // Responsive width at different breakpoints
          "max-h-[85vh] md:max-h-[90vh]",
          "max-w-[calc(100%-2rem)] sm:max-w-[85%] md:max-w-[80%] lg:max-w-[75%] xl:max-w-[70%] 2xl:max-w-[65%]",
          className
        )}
        {...props}
      >
        {children}
        <DialogPrimitive.Close className="ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4">
          <XIcon />
          <span className="sr-only">Close</span>
        </DialogPrimitive.Close>
      </DialogPrimitive.Content>
    </ExpandedDialogPortal>
  )
}

function ExpandedDialogHeader({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="expanded-dialog-header"
      className={cn("flex flex-col gap-2 text-center sm:text-left", className)}
      {...props}
    />
  )
}

function ExpandedDialogFooter({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="expanded-dialog-footer"
      className={cn(
        "flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",
        className
      )}
      {...props}
    />
  )
}

function ExpandedDialogTitle({
  className,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Title>) {
  return (
    <DialogPrimitive.Title
      data-slot="expanded-dialog-title"
      className={cn("text-lg leading-none font-semibold", className)}
      {...props}
    />
  )
}

function ExpandedDialogDescription({
  className,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Description>) {
  return (
    <DialogPrimitive.Description
      data-slot="expanded-dialog-description"
      className={cn("text-muted-foreground text-sm", className)}
      {...props}
    />
  )
}

function ExpandedDialogBody({
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="expanded-dialog-body"
      className={cn("overflow-auto", className)}
      {...props}
    />
  )
}

export {
  ExpandedDialog,
  ExpandedDialogClose,
  ExpandedDialogContent,
  ExpandedDialogDescription,
  ExpandedDialogFooter,
  ExpandedDialogHeader,
  ExpandedDialogOverlay,
  ExpandedDialogPortal,
  ExpandedDialogTitle,
  ExpandedDialogTrigger,
  ExpandedDialogBody,
}
