import { ChevronRight } from 'lucide-react';

import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
    SidebarGroup,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuAction,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuSub,
    SidebarMenuSubButton,
    SidebarMenuSubItem,
} from '@/components/ui/sidebar';
import { type NavItemWithChildren, type NavSubItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';

function NavSubMenu({ items, level = 0 }: { items: NavSubItem[]; level?: number }) {
    const page = usePage();

    const isItemActive = (item: NavSubItem): boolean => {
        if (item.href === page.url) return true;
        return item.items?.some(isItemActive) || false;
    };

    return (
        <SidebarMenuSub className={level > 0 ? `ml-${Math.min(level * 4, 16)}` : ''}>
            {items.map((item) => {
                const itemIsActive = isItemActive(item);

                return (
                    <Collapsible key={item.title} asChild defaultOpen={itemIsActive}>
                        <SidebarMenuSubItem>
                            <SidebarMenuSubButton
                                asChild
                                isActive={item.href === page.url}
                                size={level > 0 ? 'sm' : 'md'}
                            >
                                <Link href={item.href} prefetch>
                                    <span>{item.title}</span>
                                </Link>
                            </SidebarMenuSubButton>

                            {item.items?.length ? (
                                <>
                                    <CollapsibleTrigger asChild>
                                        <SidebarMenuAction className="data-[state=open]:rotate-90">
                                            <ChevronRight />
                                            <span className="sr-only">Toggle {item.title}</span>
                                        </SidebarMenuAction>
                                    </CollapsibleTrigger>
                                    <CollapsibleContent>
                                        <NavSubMenu items={item.items} level={level + 1} />
                                    </CollapsibleContent>
                                </>
                            ) : null}
                        </SidebarMenuSubItem>
                    </Collapsible>
                );
            })}
        </SidebarMenuSub>
    );
}

export function NavMain({ items = [] }: { items: NavItemWithChildren[] }) {
    const page = usePage();

    const isItemActive = (item: NavItemWithChildren): boolean => {
        if (item.href === page.url) return true;
        return item.items?.some((subItem) => {
            if (subItem.href === page.url) return true;
            const checkNested = (items: NavSubItem[]): boolean =>
                items.some(nested => nested.href === page.url || (nested.items && checkNested(nested.items)));
            return subItem.items ? checkNested(subItem.items) : false;
        }) || false;
    };

    return (
        <SidebarGroup className="px-2 py-0">
            <SidebarGroupLabel>Platform</SidebarGroupLabel>
            <SidebarMenu>
                {items.map((item) => {
                    const itemIsActive = isItemActive(item);

                    return (
                        <Collapsible key={item.title} asChild defaultOpen={itemIsActive}>
                            <SidebarMenuItem>
                                <SidebarMenuButton asChild isActive={item.href === page.url} tooltip={{ children: item.title }}>
                                    <Link href={item.href} prefetch>
                                        {item.icon && <item.icon />}
                                        <span>{item.title}</span>
                                    </Link>
                                </SidebarMenuButton>

                                {item.items?.length ? (
                                    <>
                                        <CollapsibleTrigger asChild>
                                            <SidebarMenuAction className="data-[state=open]:rotate-90">
                                                <ChevronRight />
                                                <span className="sr-only">Toggle {item.title}</span>
                                            </SidebarMenuAction>
                                        </CollapsibleTrigger>
                                        <CollapsibleContent>
                                            <NavSubMenu items={item.items} />
                                        </CollapsibleContent>
                                    </>
                                ) : null}
                            </SidebarMenuItem>
                        </Collapsible>
                    );
                })}
            </SidebarMenu>
        </SidebarGroup>
    );
}
